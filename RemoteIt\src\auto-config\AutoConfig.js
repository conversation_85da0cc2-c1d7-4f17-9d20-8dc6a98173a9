const os = require('os');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const axios = require('axios');

// Auto-Configuration System for Zero-Setup Experience
class AutoConfig {
  constructor() {
    this.configPath = this.getConfigPath();
    this.deviceId = null;
    this.isConfigured = false;
    this.cloudEndpoints = {
      production: 'https://api.remoteit.com',
      staging: 'https://staging-api.remoteit.com',
      development: 'http://localhost:3000'
    };
    
    this.environment = process.env.NODE_ENV || 'production';
    this.apiUrl = this.cloudEndpoints[this.environment];
    
    console.log('🔧 AutoConfig initialized');
  }

  // Get platform-specific config directory
  getConfigPath() {
    const platform = os.platform();
    const homeDir = os.homedir();
    
    switch (platform) {
      case 'win32':
        return path.join(process.env.APPDATA || homeDir, 'RemoteIt');
      case 'darwin':
        return path.join(homeDir, 'Library', 'Application Support', 'RemoteIt');
      case 'linux':
        return path.join(homeDir, '.config', 'remoteit');
      default:
        return path.join(homeDir, '.remoteit');
    }
  }

  // Initialize auto-configuration
  async initialize() {
    console.log('🚀 Starting auto-configuration...');
    
    try {
      // Create config directory if it doesn't exist
      if (!fs.existsSync(this.configPath)) {
        fs.mkdirSync(this.configPath, { recursive: true });
        console.log('📁 Created config directory:', this.configPath);
      }
      
      // Load or create device configuration
      await this.loadOrCreateDeviceConfig();
      
      // Test cloud connectivity
      await this.testCloudConnectivity();
      
      // Register device with cloud
      await this.registerDevice();
      
      // Setup auto-start (if not portable)
      if (!process.env.REMOTEIT_PORTABLE) {
        await this.setupAutoStart();
      }
      
      this.isConfigured = true;
      console.log('✅ Auto-configuration completed successfully');
      
      return {
        success: true,
        deviceId: this.deviceId,
        apiUrl: this.apiUrl,
        configPath: this.configPath
      };
      
    } catch (error) {
      console.error('❌ Auto-configuration failed:', error.message);
      throw error;
    }
  }

  // Load existing config or create new one
  async loadOrCreateDeviceConfig() {
    const configFile = path.join(this.configPath, 'device.json');
    
    if (fs.existsSync(configFile)) {
      try {
        const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));
        this.deviceId = config.deviceId;
        console.log('📋 Loaded existing device config:', this.deviceId);
        return;
      } catch (error) {
        console.warn('⚠️ Corrupted config file, creating new one');
      }
    }
    
    // Generate new device configuration
    this.deviceId = this.generateDeviceId();
    const deviceConfig = {
      deviceId: this.deviceId,
      name: this.getDeviceName(),
      platform: os.platform(),
      arch: os.arch(),
      version: '1.0.0',
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      capabilities: this.getDeviceCapabilities()
    };
    
    fs.writeFileSync(configFile, JSON.stringify(deviceConfig, null, 2));
    console.log('✅ Created new device config:', this.deviceId);
  }

  // Generate unique 9-digit device ID
  generateDeviceId() {
    // Use machine-specific info for consistency
    const machineInfo = [
      os.hostname(),
      os.platform(),
      os.arch(),
      os.cpus()[0]?.model || 'unknown'
    ].join('|');
    
    const hash = crypto.createHash('sha256').update(machineInfo).digest('hex');
    const timestamp = Date.now().toString().slice(-6);
    const hashPart = hash.slice(0, 3);
    
    return timestamp + hashPart.toUpperCase();
  }

  // Get user-friendly device name
  getDeviceName() {
    const hostname = os.hostname();
    const platform = os.platform();
    const username = os.userInfo().username;
    
    // Create readable name
    let deviceName = hostname;
    
    if (platform === 'win32') {
      deviceName = `${username}'s PC`;
    } else if (platform === 'darwin') {
      deviceName = `${username}'s Mac`;
    } else if (platform === 'linux') {
      deviceName = `${username}'s Linux`;
    }
    
    return deviceName;
  }

  // Get device capabilities
  getDeviceCapabilities() {
    const platform = os.platform();
    
    return {
      screenSharing: true,
      remoteControl: true,
      fileTransfer: true,
      audioRedirection: platform !== 'linux', // Limited on Linux
      clipboardSync: true,
      multiMonitor: true,
      unattendedAccess: true,
      wakeOnLan: platform === 'win32' || platform === 'linux'
    };
  }

  // Test connectivity to cloud services
  async testCloudConnectivity() {
    console.log('🌐 Testing cloud connectivity...');
    
    try {
      const response = await axios.get(`${this.apiUrl}/health`, {
        timeout: 10000,
        headers: {
          'User-Agent': 'RemoteIt-AutoConfig/1.0'
        }
      });
      
      if (response.status === 200) {
        console.log('✅ Cloud connectivity test passed');
        return true;
      }
      
    } catch (error) {
      console.warn('⚠️ Cloud connectivity test failed:', error.message);
      
      // Try fallback endpoints
      for (const [env, url] of Object.entries(this.cloudEndpoints)) {
        if (url !== this.apiUrl) {
          try {
            await axios.get(`${url}/health`, { timeout: 5000 });
            console.log(`✅ Fallback to ${env} endpoint: ${url}`);
            this.apiUrl = url;
            return true;
          } catch (fallbackError) {
            console.warn(`❌ Fallback ${env} failed`);
          }
        }
      }
      
      throw new Error('No cloud endpoints available');
    }
  }

  // Register device with cloud
  async registerDevice() {
    console.log('📡 Registering device with cloud...');
    
    const deviceInfo = {
      deviceId: this.deviceId,
      name: this.getDeviceName(),
      platform: os.platform(),
      arch: os.arch(),
      hostname: os.hostname(),
      version: '1.0.0',
      capabilities: this.getDeviceCapabilities(),
      networkInfo: this.getNetworkInfo(),
      systemInfo: this.getSystemInfo()
    };
    
    try {
      const response = await axios.post(`${this.apiUrl}/devices/register`, deviceInfo, {
        timeout: 15000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'RemoteIt-AutoConfig/1.0'
        }
      });
      
      if (response.status === 200 || response.status === 201) {
        console.log('✅ Device registered successfully');
        
        // Save registration info
        const regFile = path.join(this.configPath, 'registration.json');
        fs.writeFileSync(regFile, JSON.stringify({
          deviceId: this.deviceId,
          registeredAt: new Date().toISOString(),
          cloudEndpoint: this.apiUrl,
          status: 'registered'
        }, null, 2));
        
        return response.data;
      }
      
    } catch (error) {
      if (error.response?.status === 409) {
        console.log('ℹ️ Device already registered');
        return { status: 'already_registered' };
      }
      
      console.error('❌ Device registration failed:', error.message);
      throw error;
    }
  }

  // Get network information
  getNetworkInfo() {
    const interfaces = os.networkInterfaces();
    const networkInfo = [];
    
    Object.keys(interfaces).forEach(name => {
      interfaces[name].forEach(iface => {
        if (iface.family === 'IPv4' && !iface.internal) {
          networkInfo.push({
            interface: name,
            address: iface.address,
            netmask: iface.netmask,
            mac: iface.mac
          });
        }
      });
    });
    
    return networkInfo;
  }

  // Get system information
  getSystemInfo() {
    return {
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      cpuCount: os.cpus().length,
      cpuModel: os.cpus()[0]?.model || 'Unknown',
      uptime: os.uptime(),
      loadAverage: os.loadavg(),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    };
  }

  // Setup auto-start on system boot
  async setupAutoStart() {
    const platform = os.platform();
    
    try {
      if (platform === 'win32') {
        await this.setupWindowsAutoStart();
      } else if (platform === 'darwin') {
        await this.setupMacAutoStart();
      } else if (platform === 'linux') {
        await this.setupLinuxAutoStart();
      }
      
      console.log('✅ Auto-start configured');
      
    } catch (error) {
      console.warn('⚠️ Auto-start setup failed:', error.message);
      // Non-critical error, continue
    }
  }

  // Windows auto-start setup
  async setupWindowsAutoStart() {
    const { execSync } = require('child_process');
    const appPath = process.execPath;
    const regKey = 'HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run';
    
    try {
      execSync(`reg add "${regKey}" /v "RemoteIt" /t REG_SZ /d "${appPath}" /f`, {
        stdio: 'ignore'
      });
    } catch (error) {
      throw new Error('Failed to add Windows registry entry');
    }
  }

  // macOS auto-start setup
  async setupMacAutoStart() {
    const plistContent = `
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>Label</key>
  <string>com.remoteit.app</string>
  <key>ProgramArguments</key>
  <array>
    <string>${process.execPath}</string>
  </array>
  <key>RunAtLoad</key>
  <true/>
  <key>KeepAlive</key>
  <true/>
</dict>
</plist>
`;
    
    const launchAgentsDir = path.join(os.homedir(), 'Library', 'LaunchAgents');
    const plistPath = path.join(launchAgentsDir, 'com.remoteit.app.plist');
    
    if (!fs.existsSync(launchAgentsDir)) {
      fs.mkdirSync(launchAgentsDir, { recursive: true });
    }
    
    fs.writeFileSync(plistPath, plistContent);
  }

  // Linux auto-start setup
  async setupLinuxAutoStart() {
    const desktopEntry = `
[Desktop Entry]
Type=Application
Name=RemoteIt
Comment=Remote Desktop Access
Exec=${process.execPath}
Icon=remoteit
Terminal=false
StartupNotify=true
X-GNOME-Autostart-enabled=true
`;
    
    const autostartDir = path.join(os.homedir(), '.config', 'autostart');
    const desktopPath = path.join(autostartDir, 'remoteit.desktop');
    
    if (!fs.existsSync(autostartDir)) {
      fs.mkdirSync(autostartDir, { recursive: true });
    }
    
    fs.writeFileSync(desktopPath, desktopEntry);
  }

  // Get current configuration
  getConfig() {
    return {
      deviceId: this.deviceId,
      apiUrl: this.apiUrl,
      configPath: this.configPath,
      isConfigured: this.isConfigured,
      environment: this.environment
    };
  }

  // Update configuration
  async updateConfig(updates) {
    const configFile = path.join(this.configPath, 'device.json');
    
    if (fs.existsSync(configFile)) {
      const config = JSON.parse(fs.readFileSync(configFile, 'utf8'));
      Object.assign(config, updates, { lastUpdated: new Date().toISOString() });
      fs.writeFileSync(configFile, JSON.stringify(config, null, 2));
      
      console.log('✅ Configuration updated');
    }
  }
}

module.exports = AutoConfig;
