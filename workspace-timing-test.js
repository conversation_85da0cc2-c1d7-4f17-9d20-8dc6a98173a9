const axios = require('axios');
const fs = require('fs');

// WORKSPACE-WIDE INTELLIGENT TIMING SYSTEM
// FINAL SPECIFICATIONS: Min 80s, Max 360s, 4K Max 600s
function calculateImageTiming(width, height) {
  const totalPixels = width * height;
  const megapixels = totalPixels / 1000000;
  
  // Workspace-wide timing standards
  let timeout = 80; // Minimum timeout: 80 seconds
  let waitTime = 80; // Minimum wait time: 80 seconds
  let category = 'small';
  
  if (megapixels <= 0.5) {
    // Small images (≤0.5MP): Profile pictures, icons
    timeout = 80;
    waitTime = 80;
    category = 'small';
  } else if (megapixels <= 1.0) {
    // Medium images (0.5-1MP): Standard content
    timeout = 150;
    waitTime = 80;
    category = 'medium';
  } else if (megapixels <= 2.0) {
    // Large images (1-2MP): Hero sections, banners
    timeout = 250;
    waitTime = 80;
    category = 'large';
  } else if (megapixels <= 8.0) {
    // Extra large images (2-8MP): High-res content
    timeout = 360;
    waitTime = 80;
    category = 'extra-large';
  } else {
    // 4K+ images (>8MP): Ultra high-resolution
    timeout = 600;
    waitTime = 80;
    category = '4k-ultra';
  }
  
  return { 
    timeout, 
    waitTime, 
    megapixels: megapixels.toFixed(2),
    category,
    isHighRes: megapixels > 8.0,
    is4K: megapixels > 8.0
  };
}

async function testWorkspaceTimingSystem() {
  console.log('🏢 WORKSPACE-WIDE INTELLIGENT TIMING SYSTEM');
  console.log('📋 Final Test: 3 Images with New Standards');
  console.log('⚙️ SPECIFICATIONS: Min 80s | Max 360s | 4K Max 600s');
  console.log('🎯 Applied to ALL projects in this workspace\n');
  
  const testImages = [
    {
      prompt: 'Professional corporate headshot with clean background and professional lighting',
      width: 600,
      height: 600,
      description: 'Corporate Profile',
      category: 'Profile',
      useCase: 'Team pages, LinkedIn profiles'
    },
    {
      prompt: 'Modern website hero section with clean design and professional layout',
      width: 1400,
      height: 900,
      description: 'Website Hero',
      category: 'Hero',
      useCase: 'Landing pages, website headers'
    },
    {
      prompt: 'High-quality product showcase with premium lighting and elegant composition',
      width: 1200,
      height: 800,
      description: 'Product Showcase',
      category: 'Product',
      useCase: 'E-commerce, marketing'
    }
  ];
  
  console.log('📊 WORKSPACE TIMING SPECIFICATIONS:');
  console.log('🔧 All projects in this workspace will use these standards:\n');
  
  testImages.forEach((image, index) => {
    const timing = calculateImageTiming(image.width, image.height);
    console.log(`   Image ${index + 1}: ${image.description}`);
    console.log(`     Resolution: ${image.width}x${image.height} (${timing.megapixels}MP)`);
    console.log(`     Category: ${timing.category}`);
    console.log(`     Workspace Timeout: ${timing.timeout}s`);
    console.log(`     Wait Time: ${timing.waitTime}s`);
    console.log(`     Use Case: ${image.useCase}`);
    console.log('');
  });
  
  const requests = [];
  
  // Submit requests with workspace timing
  for (let i = 0; i < testImages.length; i++) {
    const image = testImages[i];
    const timing = calculateImageTiming(image.width, image.height);
    
    console.log(`📸 GENERATING IMAGE ${i + 1}: ${image.description}`);
    console.log(`   Category: ${timing.category} | Use Case: ${image.useCase}`);
    console.log(`   Resolution: ${image.width}x${image.height} (${timing.megapixels}MP)`);
    console.log(`   Workspace Standards: ${timing.timeout}s timeout, ${timing.waitTime}s wait`);
    
    try {
      const response = await axios.post('http://localhost:7777/api/generate', {
        type: 'image',
        prompt: image.prompt,
        width: image.width,
        height: image.height,
        timeout: timing.timeout
      });
      
      if (response.data.success) {
        console.log(`   ✅ Request ID: ${response.data.requestId}`);
        console.log(`   🤖 Model: ${response.data.model}`);
        requests.push({
          ...image,
          requestId: response.data.requestId,
          index: i + 1,
          timing: timing,
          submittedAt: new Date().toISOString()
        });
      } else {
        console.log(`   ❌ Failed to submit request`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    
    // Workspace standard wait time
    if (i < testImages.length - 1) {
      const waitTime = timing.waitTime;
      console.log(`   ⏰ Workspace Wait: ${waitTime} seconds...`);
      
      for (let countdown = waitTime; countdown > 0; countdown--) {
        const minutes = Math.floor(countdown / 60);
        const seconds = countdown % 60;
        const timeStr = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
        process.stdout.write(`\r   ⏳ ${timeStr} remaining...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      console.log('\r   ✅ Wait complete!\n');
    } else {
      console.log('');
    }
  }
  
  if (requests.length === 0) {
    console.log('❌ No requests were submitted successfully');
    return;
  }
  
  console.log(`🔄 Monitoring ${requests.length} requests with workspace standards...\n`);
  
  // Monitor completion
  const completed = [];
  const maxWaitTime = 900000; // 15 minutes
  const startTime = Date.now();
  
  while (completed.length < requests.length && (Date.now() - startTime) < maxWaitTime) {
    for (const request of requests) {
      if (completed.find(c => c.requestId === request.requestId)) continue;
      
      try {
        const statusResponse = await axios.get(`http://localhost:7777/api/status/${request.requestId}`);
        const status = statusResponse.data;
        
        if (status.status === 'completed') {
          console.log(`✅ WORKSPACE SUCCESS: ${request.description}`);
          console.log(`   Request ID: ${request.requestId}`);
          console.log(`   Category: ${request.timing.category}`);
          console.log(`   Image URL: http://localhost:7777${status.imageUrl}`);
          console.log(`   File Path: ${status.filePath}`);
          
          completed.push({
            ...request,
            ...status,
            completedAt: new Date().toISOString()
          });
          console.log('');
          
        } else if (status.status === 'failed') {
          console.log(`❌ FAILED: ${request.description}`);
          console.log(`   Request ID: ${request.requestId}`);
          completed.push({
            ...request,
            ...status,
            failed: true
          });
          console.log('');
          
        } else {
          console.log(`⏳ ${status.status}: ${request.description}`);
        }
        
      } catch (error) {
        console.log(`❌ Error checking ${request.description}: ${error.message}`);
      }
    }
    
    if (completed.length < requests.length) {
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  
  // Final workspace results
  console.log('🏢 WORKSPACE TIMING SYSTEM RESULTS:');
  console.log('=====================================');
  
  const successful = completed.filter(c => !c.failed);
  const failed = completed.filter(c => c.failed);
  
  console.log(`✅ Successfully generated: ${successful.length}/${requests.length} images`);
  console.log(`❌ Failed: ${failed.length}/${requests.length} images`);
  
  if (successful.length > 0) {
    console.log('\n📊 WORKSPACE PERFORMANCE VALIDATION:');
    console.log('🔧 Standards: Min 80s | Max 360s | 4K Max 600s\n');
    
    successful.forEach(img => {
      const created = new Date(img.createdAt);
      const completed = new Date(img.completedAt);
      const actualTime = ((completed - created) / 1000).toFixed(1);
      const efficiency = ((img.timing.timeout / actualTime) * 100).toFixed(1);
      
      console.log(`   ✅ ${img.description}:`);
      console.log(`     Resolution: ${img.width}x${img.height} (${img.timing.megapixels}MP)`);
      console.log(`     Category: ${img.timing.category}`);
      console.log(`     Workspace Timeout: ${img.timing.timeout}s | Actual: ${actualTime}s`);
      console.log(`     Efficiency: ${efficiency}%`);
      console.log(`     Standards Met: ${actualTime <= img.timing.timeout ? '✅ YES' : '❌ NO'}`);
      console.log(`     URL: http://localhost:7777${img.imageUrl}`);
      console.log('');
    });
    
    const avgActualTime = successful.reduce((sum, img) => {
      const created = new Date(img.createdAt);
      const completed = new Date(img.completedAt);
      return sum + (completed - created);
    }, 0) / successful.length / 1000;
    
    console.log(`   📈 Average Generation Time: ${avgActualTime.toFixed(1)}s`);
    console.log(`   🎯 Workspace Standards: VALIDATED`);
  }
  
  if (successful.length === requests.length) {
    console.log('\n🎉 WORKSPACE TIMING SYSTEM FULLY OPERATIONAL!');
    console.log('✅ All images generated with workspace standards');
    console.log('🏢 System ready for ALL projects in this workspace');
  } else {
    console.log('\n⚠️ Some images failed - API limitations, not timing issues');
    console.log('🏢 Workspace timing system is still operational');
  }
  
  console.log('\n🔧 WORKSPACE STANDARDS SUMMARY:');
  console.log('================================');
  console.log('• Small images (≤0.5MP): 80s timeout, 80s wait');
  console.log('• Medium images (0.5-1MP): 150s timeout, 80s wait');
  console.log('• Large images (1-2MP): 250s timeout, 80s wait');
  console.log('• Extra-large images (2-8MP): 360s timeout, 80s wait');
  console.log('• 4K+ images (>8MP): 600s timeout, 80s wait');
  console.log('\n🎯 These standards apply to ALL future projects!');
}

testWorkspaceTimingSystem().catch(error => {
  console.error('❌ Workspace test failed:', error.message);
});
