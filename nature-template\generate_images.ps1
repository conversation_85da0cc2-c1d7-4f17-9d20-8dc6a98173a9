# Green Nature Tech - Comprehensive Image Generation Tracker
# This script ensures all images are generated successfully with quality control

param(
    [string]$OutputDir = "images",
    [int]$MaxRetries = 3,
    [int]$WaitTime = 60,
    [switch]$Force
)

# Ensure output directory exists
if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

# Image generation configuration
$ImageRequests = @(
    @{
        Name = "hero-background"
        Filename = "hero-background.jpg"
        Prompt = "Dark forest landscape with subtle green technology elements, misty atmosphere, tall trees silhouettes, green glowing accents, professional nature photography, high resolution, 1920x1080 aspect ratio"
        Priority = "High"
        Size = "Hero"
    },
    @{
        Name = "green-innovation-hero"
        Filename = "green-innovation.jpg"
        Prompt = "Modern sustainable technology concept, green innovation lab, eco-friendly devices, clean energy solutions, professional photography, bright green accents, high quality"
        Priority = "High"
        Size = "Card"
    },
    @{
        Name = "forest-technology"
        Filename = "forest-tech.jpg"
        Prompt = "Dense green forest with subtle technology integration, sustainable innovation, nature and tech harmony, professional landscape photography"
        Priority = "Medium"
        Size = "Card"
    },
    @{
        Name = "eco-solutions"
        Filename = "eco-solutions.jpg"
        Prompt = "Eco-friendly technology solutions, renewable energy concepts, sustainable design, green innovation, modern clean aesthetic"
        Priority = "Medium"
        Size = "Card"
    },
    @{
        Name = "green-tech-solutions"
        Filename = "green-tech-solutions.jpg"
        Prompt = "Green technology solutions showcase, sustainable innovation display, eco-conscious design, modern environmental technology"
        Priority = "Medium"
        Size = "Card"
    },
    @{
        Name = "nature-innovation"
        Filename = "nature-innovation.jpg"
        Prompt = "Nature-inspired innovation, biomimicry technology, green research and development, sustainable future concepts"
        Priority = "Low"
        Size = "Card"
    },
    @{
        Name = "sustainable-future"
        Filename = "sustainable-future.jpg"
        Prompt = "Sustainable future vision, green cities, renewable energy, environmental technology, clean modern design"
        Priority = "Low"
        Size = "Card"
    }
)

# Color codes for output
$Colors = @{
    Success = "Green"
    Warning = "Yellow"
    Error = "Red"
    Info = "Cyan"
    Progress = "Magenta"
}

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Test-ServiceHealth {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:7777/health" -TimeoutSec 5
        return $response.status -eq "healthy"
    }
    catch {
        return $false
    }
}

function Submit-ImageRequest {
    param([hashtable]$ImageConfig)

    try {
        $body = @{
            type = "image"
            prompt = $ImageConfig.Prompt
        } | ConvertTo-Json

        $response = Invoke-RestMethod -Uri "http://localhost:7777/api/generate" -Method POST -Body $body -ContentType "application/json"

        if ($response.success) {
            return @{
                Success = $true
                RequestId = $response.requestId
                EstimatedTime = $response.estimatedTime
            }
        }
        else {
            return @{ Success = $false; Error = "API returned success=false" }
        }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

function Wait-ForCompletion {
    param([string]$RequestId, [string]$ImageName, [int]$TimeoutMinutes = 5)
    
    $startTime = Get-Date
    $timeout = $startTime.AddMinutes($TimeoutMinutes)
    
    Write-ColorOutput "⏳ Waiting for '$ImageName' (ID: $RequestId)..." "Progress"
    
    while ((Get-Date) -lt $timeout) {
        try {
            $status = Invoke-RestMethod -Uri "http://localhost:7777/api/status/$RequestId"
            
            switch ($status.status) {
                "completed" {
                    Write-ColorOutput "✅ '$ImageName' completed successfully!" "Success"
                    return @{ Success = $true; Status = $status }
                }
                "failed" {
                    Write-ColorOutput "❌ '$ImageName' generation failed!" "Error"
                    return @{ Success = $false; Error = "Generation failed" }
                }
                "processing" {
                    Write-ColorOutput "🔄 '$ImageName' is processing..." "Info"
                }
                "pending" {
                    Write-ColorOutput "⏸️ '$ImageName' is pending..." "Warning"
                }
            }
        }
        catch {
            Write-ColorOutput "⚠️ Error checking status for '$ImageName': $($_.Exception.Message)" "Warning"
        }
        
        Start-Sleep -Seconds 10
    }
    
    Write-ColorOutput "⏰ Timeout waiting for '$ImageName'" "Error"
    return @{ Success = $false; Error = "Timeout" }
}

function Download-Image {
    param([string]$RequestId, [string]$OutputPath, [string]$ImageName)
    
    try {
        Write-ColorOutput "📥 Downloading '$ImageName'..." "Info"
        Invoke-WebRequest -Uri "http://localhost:7777/api/image/$RequestId" -OutFile $OutputPath
        
        if (Test-Path $OutputPath) {
            $fileSize = (Get-Item $OutputPath).Length
            Write-ColorOutput "✅ '$ImageName' downloaded successfully! Size: $([math]::Round($fileSize/1KB, 2)) KB" "Success"
            return $true
        }
        else {
            Write-ColorOutput "❌ Failed to download '$ImageName'" "Error"
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ Error downloading '$ImageName': $($_.Exception.Message)" "Error"
        return $false
    }
}

function Process-ImageRequest {
    param([hashtable]$ImageConfig, [int]$RetryCount = 0)
    
    $imageName = $ImageConfig.Name
    $outputPath = Join-Path $OutputDir $ImageConfig.Filename
    
    # Check if image already exists and not forcing regeneration
    if ((Test-Path $outputPath) -and !$Force) {
        Write-ColorOutput "⏭️ '$imageName' already exists, skipping..." "Warning"
        return @{ Success = $true; Skipped = $true }
    }
    
    Write-ColorOutput "🚀 Starting generation for '$imageName' (Priority: $($ImageConfig.Priority))" "Info"
    Write-ColorOutput "📝 Prompt: $($ImageConfig.Prompt)" "Info"
    
    # Submit request
    $submitResult = Submit-ImageRequest -ImageConfig $ImageConfig
    if (!$submitResult.Success) {
        Write-ColorOutput "❌ Failed to submit '$imageName': $($submitResult.Error)" "Error"
        return @{ Success = $false; Error = $submitResult.Error }
    }
    
    Write-ColorOutput "✅ '$imageName' submitted successfully! Request ID: $($submitResult.RequestId)" "Success"
    
    # Wait for completion with extended timeout for hero images
    $timeoutMinutes = if ($ImageConfig.Size -eq "Hero") { 8 } else { 5 }
    $waitResult = Wait-ForCompletion -RequestId $submitResult.RequestId -ImageName $imageName -TimeoutMinutes $timeoutMinutes
    
    if (!$waitResult.Success) {
        if ($RetryCount -lt $MaxRetries) {
            Write-ColorOutput "🔄 Retrying '$imageName' (Attempt $($RetryCount + 2)/$($MaxRetries + 1))" "Warning"
            Start-Sleep -Seconds 30
            return Process-ImageRequest -ImageConfig $ImageConfig -RetryCount ($RetryCount + 1)
        }
        else {
            Write-ColorOutput "❌ '$imageName' failed after $($MaxRetries + 1) attempts" "Error"
            return @{ Success = $false; Error = $waitResult.Error }
        }
    }
    
    # Download image
    $downloadSuccess = Download-Image -RequestId $submitResult.RequestId -OutputPath $outputPath -ImageName $imageName
    
    if ($downloadSuccess) {
        return @{ Success = $true; RequestId = $submitResult.RequestId; FilePath = $outputPath }
    }
    else {
        return @{ Success = $false; Error = "Download failed" }
    }
}

# Main execution
Write-ColorOutput "🌿 GREEN NATURE TECH - IMAGE GENERATION TRACKER" "Success"
Write-ColorOutput "================================================" "Success"

# Check service health
Write-ColorOutput "🏥 Checking image generation service..." "Info"
if (!(Test-ServiceHealth)) {
    Write-ColorOutput "❌ Image generation service is not available at http://localhost:7777" "Error"
    Write-ColorOutput "Please ensure the service is running before proceeding." "Error"
    exit 1
}
Write-ColorOutput "✅ Service is healthy and ready!" "Success"

# Process images by priority
$priorityOrder = @("High", "Medium", "Low")
$results = @()
$totalImages = $ImageRequests.Count
$processedCount = 0

foreach ($priority in $priorityOrder) {
    $priorityImages = $ImageRequests | Where-Object { $_.Priority -eq $priority }
    
    if ($priorityImages.Count -gt 0) {
        Write-ColorOutput "`n🎯 Processing $priority Priority Images ($($priorityImages.Count) images)" "Progress"
        Write-ColorOutput "=" * 50 "Progress"
        
        foreach ($imageConfig in $priorityImages) {
            $processedCount++
            Write-ColorOutput "`n[$processedCount/$totalImages] Processing: $($imageConfig.Name)" "Progress"
            
            $result = Process-ImageRequest -ImageConfig $imageConfig
            $result.ImageName = $imageConfig.Name
            $result.Priority = $imageConfig.Priority
            $results += $result
            
            # Small delay between requests to avoid overwhelming the service
            if ($processedCount -lt $totalImages) {
                Write-ColorOutput "⏸️ Waiting 5 seconds before next request..." "Info"
                Start-Sleep -Seconds 5
            }
        }
    }
}

# Generate summary report
Write-ColorOutput "`n📊 GENERATION SUMMARY REPORT" "Success"
Write-ColorOutput "=============================" "Success"

$successful = $results | Where-Object { $_.Success -and !$_.Skipped }
$skipped = $results | Where-Object { $_.Skipped }
$failed = $results | Where-Object { !$_.Success }

Write-ColorOutput "✅ Successfully Generated: $($successful.Count)" "Success"
Write-ColorOutput "⏭️ Skipped (Already Exist): $($skipped.Count)" "Warning"
Write-ColorOutput "❌ Failed: $($failed.Count)" "Error"

if ($successful.Count -gt 0) {
    Write-ColorOutput "`n🎉 Successfully Generated Images:" "Success"
    foreach ($result in $successful) {
        Write-ColorOutput "  ✅ $($result.ImageName)" "Success"
    }
}

if ($failed.Count -gt 0) {
    Write-ColorOutput "`n❌ Failed Images:" "Error"
    foreach ($result in $failed) {
        Write-ColorOutput "  ❌ $($result.ImageName): $($result.Error)" "Error"
    }
}

Write-ColorOutput "`n🌿 Image generation process completed!" "Success"
Write-ColorOutput "Check the '$OutputDir' folder for your generated images." "Info"
