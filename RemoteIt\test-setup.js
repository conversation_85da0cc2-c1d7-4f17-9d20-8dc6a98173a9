#!/usr/bin/env node

// RemoteIt Test Setup Script
// This script helps verify that everything is working correctly

const os = require('os');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

console.log('🚀 RemoteIt Test Setup Verification\n');

// Check Node.js version
console.log('📋 System Information:');
console.log(`   Node.js: ${process.version}`);
console.log(`   Platform: ${os.platform()}`);
console.log(`   Architecture: ${os.arch()}`);
console.log(`   Hostname: ${os.hostname()}`);

// Get network interfaces
const interfaces = os.networkInterfaces();
console.log('\n🌐 Network Interfaces:');
Object.keys(interfaces).forEach(name => {
  interfaces[name].forEach(iface => {
    if (iface.family === 'IPv4' && !iface.internal) {
      console.log(`   ${name}: ${iface.address}`);
    }
  });
});

// Check if required files exist
console.log('\n📁 File Check:');
const requiredFiles = [
  'package.json',
  'src/main.js',
  'src/preload.js',
  'server/auth/AuthServer.js',
  'server/relay/RelayServer.js',
  'config.js'
];

requiredFiles.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file));
  console.log(`   ${file}: ${exists ? '✅' : '❌'}`);
});

// Check if node_modules exists
const nodeModulesExists = fs.existsSync(path.join(__dirname, 'node_modules'));
console.log(`   node_modules: ${nodeModulesExists ? '✅' : '❌'}`);

if (!nodeModulesExists) {
  console.log('\n⚠️  Dependencies not installed. Run: npm install');
}

// Test server connectivity
console.log('\n🔧 Testing Server Connectivity...');

function testServer(name, command, args, port) {
  return new Promise((resolve) => {
    console.log(`   Starting ${name}...`);
    
    const server = spawn(command, args, {
      stdio: 'pipe',
      cwd: __dirname
    });
    
    let started = false;
    
    server.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes(`port ${port}`) || output.includes('started')) {
        if (!started) {
          console.log(`   ${name}: ✅ Started on port ${port}`);
          started = true;
          setTimeout(() => {
            server.kill();
            resolve(true);
          }, 2000);
        }
      }
    });
    
    server.stderr.on('data', (data) => {
      console.log(`   ${name} Error: ${data.toString()}`);
    });
    
    server.on('close', (code) => {
      if (!started) {
        console.log(`   ${name}: ❌ Failed to start (code ${code})`);
        resolve(false);
      }
    });
    
    // Timeout after 10 seconds
    setTimeout(() => {
      if (!started) {
        console.log(`   ${name}: ❌ Timeout`);
        server.kill();
        resolve(false);
      }
    }, 10000);
  });
}

async function runTests() {
  try {
    // Test Auth Server
    const authResult = await testServer(
      'Auth Server',
      'node',
      ['server/auth/AuthServer.js'],
      3000
    );
    
    // Test Relay Server
    const relayResult = await testServer(
      'Relay Server', 
      'node',
      ['server/relay/RelayServer.js'],
      8080
    );
    
    console.log('\n📊 Test Results:');
    console.log(`   Auth Server: ${authResult ? '✅' : '❌'}`);
    console.log(`   Relay Server: ${relayResult ? '✅' : '❌'}`);
    
    if (authResult && relayResult) {
      console.log('\n🎉 All tests passed! RemoteIt is ready for testing.');
      console.log('\n📝 Next Steps:');
      console.log('   1. Update config.js with your server IP address');
      console.log('   2. Start the servers: npm run start:servers');
      console.log('   3. Launch the desktop app: npm run start');
      console.log('   4. Register devices and test connection');
    } else {
      console.log('\n❌ Some tests failed. Please check the errors above.');
    }
    
  } catch (error) {
    console.error('Test error:', error.message);
  }
}

// Check if we have the required dependencies
const requiredPackages = [
  'express',
  'ws',
  'bcryptjs',
  'jsonwebtoken',
  'electron'
];

console.log('\n📦 Package Check:');
let allPackagesInstalled = true;

requiredPackages.forEach(pkg => {
  try {
    require.resolve(pkg);
    console.log(`   ${pkg}: ✅`);
  } catch (e) {
    console.log(`   ${pkg}: ❌`);
    allPackagesInstalled = false;
  }
});

if (!allPackagesInstalled) {
  console.log('\n⚠️  Some packages are missing. Installing...');
  
  const install = spawn('npm', ['install'], {
    stdio: 'inherit',
    cwd: __dirname
  });
  
  install.on('close', (code) => {
    if (code === 0) {
      console.log('\n✅ Dependencies installed successfully!');
      runTests();
    } else {
      console.log('\n❌ Failed to install dependencies');
    }
  });
} else {
  runTests();
}
