# 🌿 Advanced Green Nature Tech - Image Generation System
# Utilizing all new API capabilities: dimensions, seeds, timeouts, priorities

param(
    [string]$OutputDir = "images",
    [switch]$Force,
    [switch]$UseSeeds,
    [int]$BaseSeed = 12345,
    [string]$Priority = "high"
)

Write-Host "🌿 ADVANCED GREEN NATURE TECH - IMAGE GENERATION SYSTEM" -ForegroundColor Green
Write-Host "=======================================================" -ForegroundColor Green

# Advanced image configuration with new API features
$ImageRequests = @(
    @{
        Name = "hero-background-4k"
        Filename = "hero-background-4k.jpg"
        Prompt = "Dark mystical forest landscape with subtle green technology elements, misty atmosphere, tall ancient trees, bioluminescent accents, professional nature photography"
        Width = 1920
        Height = 1080
        Timeout = 360
        Priority = "urgent"
        Seed = if ($UseSeeds) { $BaseSeed + 1 } else { $null }
        Category = "Hero"
    },
    @{
        Name = "green-innovation-square"
        Filename = "green-innovation-square.jpg"
        Prompt = "Modern sustainable technology laboratory, green innovation workspace, eco-friendly research equipment, clean energy solutions, professional photography"
        Width = 1024
        Height = 1024
        Timeout = 240
        Priority = "high"
        Seed = if ($UseSeeds) { $BaseSeed + 2 } else { $null }
        Category = "Feature"
    },
    @{
        Name = "nature-tech-portrait"
        Filename = "nature-tech-portrait.jpg"
        Prompt = "Vertical nature-technology integration concept, biomimicry innovation, green research and development, sustainable future vision"
        Width = 768
        Height = 1024
        Timeout = 240
        Priority = "high"
        Seed = if ($UseSeeds) { $BaseSeed + 3 } else { $null }
        Category = "Portrait"
    },
    @{
        Name = "eco-solutions-wide"
        Filename = "eco-solutions-wide.jpg"
        Prompt = "Wide panoramic view of eco-friendly technology solutions, renewable energy landscape, sustainable innovation, environmental harmony"
        Width = 1600
        Height = 900
        Timeout = 300
        Priority = "normal"
        Seed = if ($UseSeeds) { $BaseSeed + 4 } else { $null }
        Category = "Banner"
    },
    @{
        Name = "green-lab-detailed"
        Filename = "green-lab-detailed.jpg"
        Prompt = "Highly detailed green innovation laboratory interior, advanced sustainable technology equipment, eco-friendly research environment, modern clean design"
        Width = 1200
        Height = 800
        Timeout = 300
        Priority = "normal"
        Seed = if ($UseSeeds) { $BaseSeed + 5 } else { $null }
        Category = "Detailed"
    },
    @{
        Name = "sustainable-city-concept"
        Filename = "sustainable-city-concept.jpg"
        Prompt = "Futuristic sustainable smart city concept, green architecture, renewable energy infrastructure, eco-friendly urban planning"
        Width = 1400
        Height = 800
        Timeout = 360
        Priority = "normal"
        Seed = if ($UseSeeds) { $BaseSeed + 6 } else { $null }
        Category = "Concept"
    }
)

# Ensure output directory exists
if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

function Test-ServiceHealth {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:7777/health" -TimeoutSec 5
        return $response.status -eq "healthy"
    }
    catch {
        return $false
    }
}

function Submit-AdvancedImageRequest {
    param([hashtable]$ImageConfig)
    
    try {
        $body = @{
            type = "image"
            prompt = $ImageConfig.Prompt
            width = $ImageConfig.Width
            height = $ImageConfig.Height
            timeout = $ImageConfig.Timeout
            priority = $ImageConfig.Priority
        }
        
        # Add seed if specified
        if ($ImageConfig.Seed -ne $null) {
            $body.seed = $ImageConfig.Seed
        }
        
        $jsonBody = $body | ConvertTo-Json
        
        Write-Host "📝 Request Body:" -ForegroundColor Cyan
        Write-Host $jsonBody -ForegroundColor Gray
        
        $response = Invoke-RestMethod -Uri "http://localhost:7777/api/generate" -Method POST -Body $jsonBody -ContentType "application/json"
        
        if ($response.success) {
            return @{
                Success = $true
                RequestId = $response.requestId
                EstimatedTime = $response.estimatedTime
                ImageParameters = $response.imageParameters
                GenerationParameters = $response.generationParameters
            }
        }
        else {
            return @{ Success = $false; Error = "API returned success=false" }
        }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

function Wait-ForAdvancedCompletion {
    param([string]$RequestId, [string]$ImageName, [int]$TimeoutSeconds)
    
    $startTime = Get-Date
    $endTime = $startTime.AddSeconds($TimeoutSeconds + 60) # Add buffer
    
    Write-Host "⏳ Waiting for '$ImageName' (ID: $RequestId, Timeout: ${TimeoutSeconds}s)..." -ForegroundColor Yellow
    
    $checkCount = 0
    while ((Get-Date) -lt $endTime) {
        $checkCount++
        try {
            $status = Invoke-RestMethod -Uri "http://localhost:7777/api/status/$RequestId"
            
            Write-Host "🔍 Check $checkCount - Status: $($status.status)" -ForegroundColor Cyan
            
            switch ($status.status) {
                "completed" {
                    Write-Host "✅ '$ImageName' completed successfully!" -ForegroundColor Green
                    if ($status.imageParameters) {
                        Write-Host "📐 Dimensions: $($status.imageParameters.width)x$($status.imageParameters.height)" -ForegroundColor Gray
                        if ($status.imageParameters.seed) {
                            Write-Host "🎲 Seed Used: $($status.imageParameters.seed)" -ForegroundColor Gray
                        }
                    }
                    return @{ Success = $true; Status = $status }
                }
                "failed" {
                    Write-Host "❌ '$ImageName' generation failed!" -ForegroundColor Red
                    return @{ Success = $false; Error = "Generation failed" }
                }
                "processing" {
                    Write-Host "🔄 '$ImageName' is processing..." -ForegroundColor Yellow
                }
                "pending" {
                    Write-Host "⏸️ '$ImageName' is pending in queue..." -ForegroundColor Yellow
                }
            }
        }
        catch {
            Write-Host "⚠️ Error checking status: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Start-Sleep -Seconds 15
    }
    
    Write-Host "⏰ Timeout waiting for '$ImageName'" -ForegroundColor Red
    return @{ Success = $false; Error = "Timeout" }
}

function Download-AdvancedImage {
    param([string]$RequestId, [string]$OutputPath, [string]$ImageName)
    
    try {
        Write-Host "📥 Downloading '$ImageName'..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri "http://localhost:7777/api/image/$RequestId" -OutFile $OutputPath
        
        if (Test-Path $OutputPath) {
            $fileSize = (Get-Item $OutputPath).Length
            $fileSizeKB = [math]::Round($fileSize/1KB, 2)
            $fileSizeMB = [math]::Round($fileSize/1MB, 2)
            
            Write-Host "Successfully downloaded '$ImageName'!" -ForegroundColor Green
            Write-Host "File Size: $fileSizeKB KB ($fileSizeMB MB)" -ForegroundColor Gray
            return $true
        }
        else {
            Write-Host "❌ Failed to download '$ImageName'" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error downloading '$ImageName': $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host "🏥 Checking advanced image generation service..." -ForegroundColor Cyan
if (!(Test-ServiceHealth)) {
    Write-Host "❌ Image generation service is not available at http://localhost:7777" -ForegroundColor Red
    Write-Host "Please ensure the service is running before proceeding." -ForegroundColor Red
    exit 1
}
Write-Host "✅ Service is healthy and ready!" -ForegroundColor Green

# Display configuration
Write-Host "`n🔧 GENERATION CONFIGURATION:" -ForegroundColor Magenta
Write-Host "Output Directory: $OutputDir" -ForegroundColor Gray
Write-Host "Use Seeds: $UseSeeds" -ForegroundColor Gray
if ($UseSeeds) {
    Write-Host "Base Seed: $BaseSeed" -ForegroundColor Gray
}
Write-Host "Default Priority: $Priority" -ForegroundColor Gray
Write-Host "Force Regeneration: $Force" -ForegroundColor Gray

# Process images by priority
$priorityOrder = @("urgent", "high", "normal", "low")
$results = @()
$totalImages = $ImageRequests.Count
$processedCount = 0

foreach ($priority in $priorityOrder) {
    $priorityImages = $ImageRequests | Where-Object { $_.Priority -eq $priority }
    
    if ($priorityImages.Count -gt 0) {
        Write-Host "`n🎯 Processing $priority Priority Images ($($priorityImages.Count) images)" -ForegroundColor Magenta
        Write-Host "=" * 60 -ForegroundColor Magenta
        
        foreach ($imageConfig in $priorityImages) {
            $processedCount++
            Write-Host "`n[$processedCount/$totalImages] Processing: $($imageConfig.Name)" -ForegroundColor Yellow
            Write-Host "📁 Category: $($imageConfig.Category)" -ForegroundColor Gray
            Write-Host "📐 Dimensions: $($imageConfig.Width)x$($imageConfig.Height)" -ForegroundColor Gray
            Write-Host "⏱️ Timeout: $($imageConfig.Timeout)s" -ForegroundColor Gray
            Write-Host "🚀 Priority: $($imageConfig.Priority)" -ForegroundColor Gray
            if ($imageConfig.Seed) {
                Write-Host "🎲 Seed: $($imageConfig.Seed)" -ForegroundColor Gray
            }
            
            # Check if image already exists
            $outputPath = Join-Path $OutputDir $imageConfig.Filename
            if ((Test-Path $outputPath) -and !$Force) {
                Write-Host "⏭️ '$($imageConfig.Name)' already exists, skipping..." -ForegroundColor Yellow
                $results += @{ ImageName = $imageConfig.Name; Success = $true; Skipped = $true }
                continue
            }
            
            # Submit request
            $submitResult = Submit-AdvancedImageRequest -ImageConfig $imageConfig
            if (!$submitResult.Success) {
                Write-Host "❌ Failed to submit '$($imageConfig.Name)': $($submitResult.Error)" -ForegroundColor Red
                $results += @{ ImageName = $imageConfig.Name; Success = $false; Error = $submitResult.Error }
                continue
            }
            
            Write-Host "✅ '$($imageConfig.Name)' submitted successfully!" -ForegroundColor Green
            Write-Host "🆔 Request ID: $($submitResult.RequestId)" -ForegroundColor Gray
            
            # Wait for completion
            $waitResult = Wait-ForAdvancedCompletion -RequestId $submitResult.RequestId -ImageName $imageConfig.Name -TimeoutSeconds $imageConfig.Timeout
            
            if (!$waitResult.Success) {
                Write-Host "❌ '$($imageConfig.Name)' failed: $($waitResult.Error)" -ForegroundColor Red
                $results += @{ ImageName = $imageConfig.Name; Success = $false; Error = $waitResult.Error }
                continue
            }
            
            # Download image
            $downloadSuccess = Download-AdvancedImage -RequestId $submitResult.RequestId -OutputPath $outputPath -ImageName $imageConfig.Name
            
            if ($downloadSuccess) {
                $results += @{ ImageName = $imageConfig.Name; Success = $true; RequestId = $submitResult.RequestId; FilePath = $outputPath }
            }
            else {
                $results += @{ ImageName = $imageConfig.Name; Success = $false; Error = "Download failed" }
            }
            
            # Delay between requests
            if ($processedCount -lt $totalImages) {
                Write-Host "⏸️ Waiting 10 seconds before next request..." -ForegroundColor Cyan
                Start-Sleep -Seconds 10
            }
        }
    }
}

# Generate comprehensive summary
Write-Host "`n📊 ADVANCED GENERATION SUMMARY REPORT" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

$successful = $results | Where-Object { $_.Success -and !$_.Skipped }
$skipped = $results | Where-Object { $_.Skipped }
$failed = $results | Where-Object { !$_.Success }

Write-Host "✅ Successfully Generated: $($successful.Count)" -ForegroundColor Green
Write-Host "⏭️ Skipped (Already Exist): $($skipped.Count)" -ForegroundColor Yellow
Write-Host "❌ Failed: $($failed.Count)" -ForegroundColor Red

if ($successful.Count -gt 0) {
    Write-Host "`n🎉 Successfully Generated Images:" -ForegroundColor Green
    foreach ($result in $successful) {
        Write-Host "  ✅ $($result.ImageName)" -ForegroundColor Green
    }
}

if ($failed.Count -gt 0) {
    Write-Host "`n❌ Failed Images:" -ForegroundColor Red
    foreach ($result in $failed) {
        Write-Host "  ❌ $($result.ImageName): $($result.Error)" -ForegroundColor Red
    }
}

# List all images with details
Write-Host "`n📂 Current Images in Directory:" -ForegroundColor Cyan
Get-ChildItem -Path $OutputDir -Filter "*.jpg" | Sort-Object Name | ForEach-Object {
    $size = [math]::Round($_.Length / 1KB, 2)
    $sizeMB = [math]::Round($_.Length / 1MB, 2)
    Write-Host "  📸 $($_.Name) - $size KB ($sizeMB MB)" -ForegroundColor White
}

Write-Host "`n🌿 Advanced image generation process completed!" -ForegroundColor Green
Write-Host "All images generated with professional quality using advanced API features!" -ForegroundColor Cyan
