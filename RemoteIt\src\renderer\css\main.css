/* RemoteIt Main Styles */

:root {
  --primary-color: #007acc;
  --primary-hover: #005a9e;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  
  --bg-primary: #1e1e1e;
  --bg-secondary: #2d2d30;
  --bg-tertiary: #3e3e42;
  --bg-hover: #404040;
  
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-muted: #999999;
  
  --border-color: #404040;
  --border-radius: 6px;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  
  --sidebar-width: 250px;
  --title-bar-height: 32px;
  
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background: var(--bg-primary);
  color: var(--text-primary);
  overflow: hidden;
  user-select: none;
  -webkit-user-select: none;
}

.hidden {
  display: none !important;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
}

.loading-content .logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
}

.loading-content .logo i {
  color: var(--primary-color);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-secondary);
  font-size: 14px;
}

/* App Layout */
.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* Title Bar */
.title-bar {
  height: var(--title-bar-height);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
  -webkit-app-region: drag;
}

.title-bar-left,
.title-bar-center,
.title-bar-right {
  display: flex;
  align-items: center;
  flex: 1;
}

.title-bar-center {
  justify-content: center;
}

.title-bar-right {
  justify-content: flex-end;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.app-logo i {
  color: var(--primary-color);
}

#title-text {
  font-size: 12px;
  color: var(--text-secondary);
}

.title-bar-button {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  -webkit-app-region: no-drag;
  transition: all 0.2s ease;
}

.title-bar-button:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.title-bar-button.close:hover {
  background: var(--danger-color);
  color: white;
}

/* Main Layout */
.app > main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Sidebar */
.sidebar {
  width: var(--sidebar-width);
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.nav-section {
  padding: 16px 0;
}

.nav-section:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.nav-item:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.nav-item.active {
  background: var(--primary-color);
  color: white;
}

.nav-item i {
  width: 16px;
  text-align: center;
}

.nav-footer {
  margin-top: auto;
  padding: 16px;
  border-top: 1px solid var(--border-color);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.user-status {
  font-size: 12px;
  color: var(--text-muted);
}

.user-status.online {
  color: var(--success-color);
}

.logout-btn {
  width: 100%;
  padding: 8px;
  background: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

/* Main Content */
.main-content {
  flex: 1;
  background: var(--bg-primary);
  overflow-y: auto;
  padding: 24px;
}

/* Views */
.view {
  display: none;
}

.view.active {
  display: block;
}

.view-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.view-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* Buttons */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-hover);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-hover);
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

/* Dashboard */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.dashboard-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.card-header i {
  color: var(--primary-color);
}

.card-content {
  padding: 20px;
}

/* Quick Connect */
.quick-connect-form {
  display: flex;
  gap: 12px;
}

.quick-connect-form input {
  flex: 1;
  padding: 8px 12px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-size: 14px;
}

.quick-connect-form input:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Connection List */
.connection-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.connection-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.connection-item:hover {
  background: var(--bg-hover);
}

.connection-icon {
  width: 32px;
  height: 32px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.connection-details {
  flex: 1;
}

.connection-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.connection-info {
  font-size: 12px;
  color: var(--text-muted);
}

/* Status Grid */
.status-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.status-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.status-value.online {
  color: var(--success-color);
}

/* Performance Metrics */
.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-label {
  font-size: 12px;
  color: var(--text-secondary);
  min-width: 60px;
}

.metric-bar {
  flex: 1;
  height: 6px;
  background: var(--bg-primary);
  border-radius: 3px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

.metric-value {
  font-size: 12px;
  color: var(--text-primary);
  min-width: 40px;
  text-align: right;
}

/* Devices Grid */
.devices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.device-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 20px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.device-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow);
}

.device-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.device-icon {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.device-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.device-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.device-status.online {
  background: rgba(40, 167, 69, 0.2);
  color: var(--success-color);
}

.device-status.offline {
  background: rgba(108, 117, 125, 0.2);
  color: var(--text-muted);
}

.device-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.device-detail {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.device-detail .label {
  color: var(--text-secondary);
}

.device-detail .value {
  color: var(--text-primary);
}

.device-actions {
  display: flex;
  gap: 8px;
}

.device-actions .btn {
  flex: 1;
  justify-content: center;
  font-size: 12px;
  padding: 6px 12px;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-body {
  padding: 20px 24px;
}

/* Form Styles */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.form-actions .btn {
  flex: 1;
  justify-content: center;
}

.error-message {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid var(--danger-color);
  color: var(--danger-color);
  padding: 12px;
  border-radius: var(--border-radius);
  font-size: 14px;
  margin-top: 16px;
}

/* Settings Styles */
.settings-container {
  max-width: 600px;
}

.settings-section {
  margin-bottom: 32px;
}

.settings-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-secondary);
  cursor: pointer;
}

.setting-item input[type="checkbox"] {
  width: auto;
}

.setting-item input[type="range"] {
  flex: 1;
  margin: 0 12px;
}

/* Help Styles */
.help-container {
  max-width: 600px;
}

.help-section {
  margin-bottom: 32px;
}

.help-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.help-section ul {
  list-style: none;
  padding: 0;
}

.help-section li {
  margin-bottom: 8px;
}

.help-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.help-link:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

.support-info {
  background: var(--bg-secondary);
  padding: 16px;
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.support-info p {
  margin-bottom: 12px;
  font-size: 14px;
  color: var(--text-secondary);
}

/* File Transfer Styles */
.file-transfer-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 20px;
}

.transfer-queue {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.transfer-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
}

.transfer-icon {
  width: 32px;
  height: 32px;
  background: var(--info-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.transfer-details {
  flex: 1;
}

.transfer-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.transfer-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--success-color);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: var(--text-muted);
  min-width: 60px;
  text-align: right;
}

/* Connection Info */
.connection-info {
  background: var(--bg-primary);
  padding: 16px;
  border-radius: var(--border-radius);
  margin: 16px 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  color: var(--text-secondary);
}

.info-item .value {
  color: var(--text-primary);
  font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .devices-grid {
    grid-template-columns: 1fr;
  }

  .main-content {
    padding: 16px;
  }

  .modal-content {
    margin: 20px;
    width: calc(100% - 40px);
  }
}
