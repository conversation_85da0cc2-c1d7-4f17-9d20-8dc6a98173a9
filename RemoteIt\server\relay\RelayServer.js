const WebSocket = require('ws');
const express = require('express');
const http = require('http');
const cors = require('cors');
const helmet = require('helmet');
const { RateLimiterMemory } = require('rate-limiter-flexible');
const crypto = require('crypto');
// Note: In production, this would be a separate logger for server components
class Logger {
  constructor(module) {
    this.module = module;
  }

  info(message, meta = {}) {
    console.log(`[INFO] [${this.module}] ${message}`, meta);
  }

  error(message, meta = {}) {
    console.error(`[ERROR] [${this.module}] ${message}`, meta);
  }

  warn(message, meta = {}) {
    console.warn(`[WARN] [${this.module}] ${message}`, meta);
  }

  debug(message, meta = {}) {
    console.log(`[DEBUG] [${this.module}] ${message}`, meta);
  }
}

class RelayServer {
  constructor(options = {}) {
    this.port = options.port || 8080;
    this.wsPort = options.wsPort || 8081;
    this.logger = new Logger('relay-server');
    
    // Connected devices and sessions
    this.devices = new Map();
    this.sessions = new Map();
    this.connections = new Map(); // WebSocket connections
    
    // Rate limiting
    this.rateLimiter = new RateLimiterMemory({
      keyGenerator: (req) => req.ip,
      points: 100, // Number of requests
      duration: 60, // Per 60 seconds
    });
    
    // Statistics
    this.stats = {
      totalConnections: 0,
      activeConnections: 0,
      totalSessions: 0,
      activeSessions: 0,
      dataTransferred: 0,
      startTime: Date.now()
    };
    
    this.setupServer();
  }

  setupServer() {
    // Express app for HTTP API
    this.app = express();
    this.server = http.createServer(this.app);
    
    // WebSocket server for real-time communication
    this.wss = new WebSocket.Server({ 
      port: this.wsPort,
      verifyClient: (info) => this.verifyClient(info)
    });
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
  }

  setupMiddleware() {
    this.app.use(helmet());
    this.app.use(cors());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));
    
    // Rate limiting middleware
    this.app.use(async (req, res, next) => {
      try {
        await this.rateLimiter.consume(req.ip);
        next();
      } catch (rejRes) {
        res.status(429).json({ error: 'Too many requests' });
      }
    });
    
    // Logging middleware
    this.app.use((req, res, next) => {
      this.logger.info(`${req.method} ${req.path}`, { ip: req.ip });
      next();
    });
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: Date.now() - this.stats.startTime,
        stats: this.getStats()
      });
    });

    // Get server statistics
    this.app.get('/stats', (req, res) => {
      res.json(this.getStats());
    });

    // Get connected devices
    this.app.get('/devices', (req, res) => {
      const deviceList = Array.from(this.devices.values()).map(device => ({
        id: device.id,
        name: device.name,
        platform: device.platform,
        isOnline: device.isOnline,
        lastSeen: device.lastSeen,
        capabilities: device.capabilities
      }));
      
      res.json({ devices: deviceList });
    });

    // Get active sessions
    this.app.get('/sessions', (req, res) => {
      const sessionList = Array.from(this.sessions.values()).map(session => ({
        id: session.id,
        hostDeviceId: session.hostDeviceId,
        clientDeviceId: session.clientDeviceId,
        startTime: session.startTime,
        status: session.status
      }));
      
      res.json({ sessions: sessionList });
    });

    // Initiate connection between devices
    this.app.post('/connect', (req, res) => {
      const { hostDeviceId, clientDeviceId } = req.body;
      
      try {
        const sessionId = this.initiateConnection(hostDeviceId, clientDeviceId);
        res.json({ sessionId, status: 'initiated' });
      } catch (error) {
        res.status(400).json({ error: error.message });
      }
    });

    // End session
    this.app.delete('/sessions/:sessionId', (req, res) => {
      const { sessionId } = req.params;
      
      try {
        this.endSession(sessionId);
        res.json({ status: 'ended' });
      } catch (error) {
        res.status(400).json({ error: error.message });
      }
    });
  }

  setupWebSocket() {
    this.wss.on('connection', (ws, req) => {
      this.handleNewConnection(ws, req);
    });

    this.wss.on('error', (error) => {
      this.logger.error('WebSocket server error:', error.message);
    });
  }

  verifyClient(info) {
    // Basic verification - in production, add proper authentication
    const origin = info.origin;
    const userAgent = info.req.headers['user-agent'];
    
    // Allow RemoteIt clients
    if (userAgent && userAgent.includes('RemoteIt')) {
      return true;
    }
    
    // Allow localhost for development
    if (origin && origin.includes('localhost')) {
      return true;
    }
    
    return false;
  }

  handleNewConnection(ws, req) {
    const connectionId = crypto.randomUUID();
    const clientIp = req.socket.remoteAddress;
    
    this.logger.info('New WebSocket connection:', connectionId, 'from', clientIp);
    
    const connection = {
      id: connectionId,
      ws: ws,
      ip: clientIp,
      connectedAt: Date.now(),
      deviceId: null,
      lastActivity: Date.now()
    };
    
    this.connections.set(connectionId, connection);
    this.stats.totalConnections++;
    this.stats.activeConnections++;
    
    // Setup message handlers
    ws.on('message', (data) => {
      this.handleMessage(connectionId, data);
    });
    
    ws.on('close', (code, reason) => {
      this.handleDisconnection(connectionId, code, reason);
    });
    
    ws.on('error', (error) => {
      this.logger.error('WebSocket connection error:', connectionId, error.message);
    });
    
    // Send welcome message
    this.sendMessage(connectionId, {
      type: 'welcome',
      connectionId: connectionId,
      timestamp: Date.now()
    });
  }

  handleMessage(connectionId, data) {
    try {
      const message = JSON.parse(data.toString());
      const connection = this.connections.get(connectionId);
      
      if (!connection) {
        this.logger.warn('Message from unknown connection:', connectionId);
        return;
      }
      
      connection.lastActivity = Date.now();
      
      this.logger.debug('Received message:', message.type, 'from', connectionId);
      
      switch (message.type) {
        case 'register':
          this.handleDeviceRegistration(connectionId, message);
          break;
          
        case 'connect':
          this.handleConnectionRequest(connectionId, message);
          break;
          
        case 'signal':
          this.handleSignaling(connectionId, message);
          break;
          
        case 'disconnect':
          this.handleDisconnectRequest(connectionId, message);
          break;
          
        case 'heartbeat':
          this.handleHeartbeat(connectionId, message);
          break;
          
        default:
          this.logger.warn('Unknown message type:', message.type);
      }
      
    } catch (error) {
      this.logger.error('Failed to handle message:', error.message);
      this.sendError(connectionId, 'Invalid message format');
    }
  }

  handleDeviceRegistration(connectionId, message) {
    const { deviceId, deviceInfo } = message;
    
    if (!deviceId || !deviceInfo) {
      this.sendError(connectionId, 'Invalid registration data');
      return;
    }
    
    const device = {
      id: deviceId,
      name: deviceInfo.name || deviceInfo.hostname,
      platform: deviceInfo.platform,
      arch: deviceInfo.arch,
      version: deviceInfo.version,
      capabilities: deviceInfo.capabilities || {},
      connectionId: connectionId,
      isOnline: true,
      registeredAt: Date.now(),
      lastSeen: Date.now()
    };
    
    this.devices.set(deviceId, device);
    
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.deviceId = deviceId;
    }
    
    this.logger.info('Device registered:', deviceId, device.name);
    
    this.sendMessage(connectionId, {
      type: 'registered',
      deviceId: deviceId,
      timestamp: Date.now()
    });
    
    // Notify other devices about new device
    this.broadcastDeviceUpdate(device);
  }

  handleConnectionRequest(connectionId, message) {
    const { sessionId, targetDeviceId } = message;
    
    const targetDevice = this.devices.get(targetDeviceId);
    if (!targetDevice || !targetDevice.isOnline) {
      this.sendError(connectionId, 'Target device not available');
      return;
    }
    
    const clientDevice = this.getDeviceByConnection(connectionId);
    if (!clientDevice) {
      this.sendError(connectionId, 'Client device not registered');
      return;
    }
    
    // Create session
    const session = {
      id: sessionId,
      hostDeviceId: targetDeviceId,
      clientDeviceId: clientDevice.id,
      hostConnectionId: targetDevice.connectionId,
      clientConnectionId: connectionId,
      status: 'pending',
      startTime: Date.now(),
      lastActivity: Date.now()
    };
    
    this.sessions.set(sessionId, session);
    this.stats.totalSessions++;
    this.stats.activeSessions++;
    
    // Send connection request to target device
    this.sendMessage(targetDevice.connectionId, {
      type: 'connectionRequest',
      sessionId: sessionId,
      fromDevice: {
        id: clientDevice.id,
        name: clientDevice.name,
        platform: clientDevice.platform
      },
      timestamp: Date.now()
    });
    
    this.logger.info('Connection request sent:', sessionId, 'from', clientDevice.id, 'to', targetDeviceId);
  }

  handleSignaling(connectionId, message) {
    const { sessionId, signal } = message;
    
    const session = this.sessions.get(sessionId);
    if (!session) {
      this.sendError(connectionId, 'Session not found');
      return;
    }
    
    // Forward signal to the other peer
    const targetConnectionId = connectionId === session.hostConnectionId 
      ? session.clientConnectionId 
      : session.hostConnectionId;
    
    this.sendMessage(targetConnectionId, {
      type: 'signal',
      sessionId: sessionId,
      signal: signal,
      timestamp: Date.now()
    });
    
    session.lastActivity = Date.now();
  }

  handleDisconnectRequest(connectionId, message) {
    const { sessionId } = message;
    this.endSession(sessionId);
  }

  handleHeartbeat(connectionId, message) {
    const connection = this.connections.get(connectionId);
    if (connection && connection.deviceId) {
      const device = this.devices.get(connection.deviceId);
      if (device) {
        device.lastSeen = Date.now();
      }
    }
    
    this.sendMessage(connectionId, {
      type: 'heartbeat',
      timestamp: Date.now()
    });
  }

  handleDisconnection(connectionId, code, reason) {
    this.logger.info('WebSocket disconnected:', connectionId, 'Code:', code, 'Reason:', reason.toString());
    
    const connection = this.connections.get(connectionId);
    if (connection) {
      // Mark device as offline
      if (connection.deviceId) {
        const device = this.devices.get(connection.deviceId);
        if (device) {
          device.isOnline = false;
          device.lastSeen = Date.now();
          this.broadcastDeviceUpdate(device);
        }
      }
      
      // End any active sessions
      this.endSessionsForConnection(connectionId);
      
      this.connections.delete(connectionId);
      this.stats.activeConnections--;
    }
  }

  initiateConnection(hostDeviceId, clientDeviceId) {
    const hostDevice = this.devices.get(hostDeviceId);
    const clientDevice = this.devices.get(clientDeviceId);
    
    if (!hostDevice || !hostDevice.isOnline) {
      throw new Error('Host device not available');
    }
    
    if (!clientDevice || !clientDevice.isOnline) {
      throw new Error('Client device not available');
    }
    
    const sessionId = crypto.randomUUID();
    
    // This would trigger the connection request flow
    this.handleConnectionRequest(clientDevice.connectionId, {
      sessionId: sessionId,
      targetDeviceId: hostDeviceId
    });
    
    return sessionId;
  }

  endSession(sessionId) {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }
    
    // Notify both peers
    this.sendMessage(session.hostConnectionId, {
      type: 'sessionEnded',
      sessionId: sessionId,
      timestamp: Date.now()
    });
    
    this.sendMessage(session.clientConnectionId, {
      type: 'sessionEnded',
      sessionId: sessionId,
      timestamp: Date.now()
    });
    
    session.endTime = Date.now();
    session.status = 'ended';
    
    this.sessions.delete(sessionId);
    this.stats.activeSessions--;
    
    this.logger.info('Session ended:', sessionId);
  }

  endSessionsForConnection(connectionId) {
    for (const [sessionId, session] of this.sessions) {
      if (session.hostConnectionId === connectionId || session.clientConnectionId === connectionId) {
        this.endSession(sessionId);
      }
    }
  }

  getDeviceByConnection(connectionId) {
    const connection = this.connections.get(connectionId);
    return connection && connection.deviceId ? this.devices.get(connection.deviceId) : null;
  }

  sendMessage(connectionId, message) {
    const connection = this.connections.get(connectionId);
    if (connection && connection.ws.readyState === WebSocket.OPEN) {
      connection.ws.send(JSON.stringify(message));
    }
  }

  sendError(connectionId, error) {
    this.sendMessage(connectionId, {
      type: 'error',
      error: error,
      timestamp: Date.now()
    });
  }

  broadcastDeviceUpdate(device) {
    const message = {
      type: 'deviceStatusUpdate',
      deviceId: device.id,
      isOnline: device.isOnline,
      lastSeen: device.lastSeen,
      timestamp: Date.now()
    };
    
    for (const connection of this.connections.values()) {
      if (connection.deviceId && connection.deviceId !== device.id) {
        this.sendMessage(connection.id, message);
      }
    }
  }

  getStats() {
    return {
      ...this.stats,
      uptime: Date.now() - this.stats.startTime,
      connectedDevices: Array.from(this.devices.values()).filter(d => d.isOnline).length,
      totalDevices: this.devices.size
    };
  }

  start() {
    return new Promise((resolve, reject) => {
      this.server.listen(this.port, (error) => {
        if (error) {
          reject(error);
        } else {
          this.logger.info(`Relay server started on port ${this.port}`);
          this.logger.info(`WebSocket server started on port ${this.wsPort}`);
          resolve();
        }
      });
    });
  }

  stop() {
    return new Promise((resolve) => {
      this.wss.close(() => {
        this.server.close(() => {
          this.logger.info('Relay server stopped');
          resolve();
        });
      });
    });
  }
}

module.exports = RelayServer;

// Start server if run directly
if (require.main === module) {
  const server = new RelayServer({
    port: process.env.PORT || 8080,
    wsPort: process.env.WS_PORT || 8081
  });
  
  server.start().catch(error => {
    console.error('Failed to start relay server:', error);
    process.exit(1);
  });
  
  // Graceful shutdown
  process.on('SIGINT', async () => {
    console.log('Shutting down relay server...');
    await server.stop();
    process.exit(0);
  });
}
