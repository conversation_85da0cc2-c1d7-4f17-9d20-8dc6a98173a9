const { EventEmitter } = require('events');
const robot = require('robotjs');
const Logger = require('../utils/Logger');

class InputHandler extends EventEmitter {
  constructor() {
    super();
    this.logger = new Logger('input-handler');
    this.isEnabled = false;
    this.mousePosition = { x: 0, y: 0 };
    this.screenSize = { width: 1920, height: 1080 };
    this.inputQueue = [];
    this.processingInput = false;
    
    // Input filtering and security
    this.allowedKeys = new Set([
      'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
      'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
      '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
      'space', 'enter', 'backspace', 'delete', 'tab', 'escape',
      'shift', 'control', 'alt', 'meta', 'capslock',
      'f1', 'f2', 'f3', 'f4', 'f5', 'f6', 'f7', 'f8', 'f9', 'f10', 'f11', 'f12',
      'up', 'down', 'left', 'right', 'home', 'end', 'pageup', 'pagedown',
      'insert', 'printscreen', 'scrolllock', 'pause'
    ]);
    
    this.blockedKeys = new Set([
      'cmd+alt+delete', 'ctrl+alt+delete', 'cmd+shift+q'
    ]);
    
    // Statistics
    this.stats = {
      mouseEvents: 0,
      keyboardEvents: 0,
      scrollEvents: 0,
      totalEvents: 0,
      startTime: Date.now()
    };
  }

  async initialize() {
    this.logger.info('Initializing input handler...');
    
    try {
      // Set robot.js settings for better performance
      robot.setMouseDelay(0);
      robot.setKeyboardDelay(0);
      
      // Get screen size
      const screenSize = robot.getScreenSize();
      this.screenSize = screenSize;
      
      this.logger.info(`Input handler initialized. Screen size: ${screenSize.width}x${screenSize.height}`);
      
    } catch (error) {
      this.logger.error('Failed to initialize input handler:', error.message);
      throw error;
    }
  }

  enable() {
    this.isEnabled = true;
    this.logger.info('Input handler enabled');
    this.emit('inputEnabled');
  }

  disable() {
    this.isEnabled = false;
    this.logger.info('Input handler disabled');
    this.emit('inputDisabled');
  }

  async sendEvent(inputEvent) {
    if (!this.isEnabled) {
      this.logger.warn('Input handler is disabled, ignoring event');
      return false;
    }

    // Add to queue for processing
    this.inputQueue.push({
      ...inputEvent,
      timestamp: Date.now()
    });

    // Process queue if not already processing
    if (!this.processingInput) {
      this.processInputQueue();
    }

    return true;
  }

  async processInputQueue() {
    this.processingInput = true;

    while (this.inputQueue.length > 0) {
      const event = this.inputQueue.shift();
      
      try {
        await this.processInputEvent(event);
        this.stats.totalEvents++;
      } catch (error) {
        this.logger.error('Failed to process input event:', error.message);
        this.emit('inputError', { event, error });
      }
    }

    this.processingInput = false;
  }

  async processInputEvent(event) {
    const { type, data } = event;

    switch (type) {
      case 'mouse':
        await this.handleMouseEvent(data);
        this.stats.mouseEvents++;
        break;

      case 'keyboard':
        await this.handleKeyboardEvent(data);
        this.stats.keyboardEvents++;
        break;

      case 'scroll':
        await this.handleScrollEvent(data);
        this.stats.scrollEvents++;
        break;

      default:
        this.logger.warn('Unknown input event type:', type);
    }

    this.emit('inputProcessed', event);
  }

  async handleMouseEvent(data) {
    const { x, y, button, action, clicks = 1 } = data;

    // Validate coordinates
    if (x < 0 || x > this.screenSize.width || y < 0 || y > this.screenSize.height) {
      this.logger.warn('Mouse coordinates out of bounds:', { x, y });
      return;
    }

    try {
      switch (action) {
        case 'move':
          robot.moveMouse(x, y);
          this.mousePosition = { x, y };
          break;

        case 'down':
          robot.moveMouse(x, y);
          robot.mouseToggle('down', button || 'left');
          this.mousePosition = { x, y };
          break;

        case 'up':
          robot.moveMouse(x, y);
          robot.mouseToggle('up', button || 'left');
          this.mousePosition = { x, y };
          break;

        case 'click':
          robot.moveMouse(x, y);
          robot.mouseClick(button || 'left', clicks === 2);
          this.mousePosition = { x, y };
          break;

        case 'drag':
          robot.dragMouse(x, y);
          this.mousePosition = { x, y };
          break;

        default:
          this.logger.warn('Unknown mouse action:', action);
      }
    } catch (error) {
      this.logger.error('Mouse event error:', error.message);
      throw error;
    }
  }

  async handleKeyboardEvent(data) {
    const { key, action, modifiers = [] } = data;

    // Security check - block dangerous key combinations
    const keyCombo = this.getKeyCombo(key, modifiers);
    if (this.blockedKeys.has(keyCombo)) {
      this.logger.warn('Blocked dangerous key combination:', keyCombo);
      return;
    }

    // Validate key
    if (!this.allowedKeys.has(key.toLowerCase())) {
      this.logger.warn('Key not in allowed list:', key);
      return;
    }

    try {
      switch (action) {
        case 'down':
          if (modifiers.length > 0) {
            robot.keyToggle(key, 'down', modifiers);
          } else {
            robot.keyToggle(key, 'down');
          }
          break;

        case 'up':
          if (modifiers.length > 0) {
            robot.keyToggle(key, 'up', modifiers);
          } else {
            robot.keyToggle(key, 'up');
          }
          break;

        case 'press':
          if (modifiers.length > 0) {
            robot.keyTap(key, modifiers);
          } else {
            robot.keyTap(key);
          }
          break;

        case 'type':
          // For typing strings
          robot.typeString(key);
          break;

        default:
          this.logger.warn('Unknown keyboard action:', action);
      }
    } catch (error) {
      this.logger.error('Keyboard event error:', error.message);
      throw error;
    }
  }

  async handleScrollEvent(data) {
    const { x, y, deltaX = 0, deltaY = 0 } = data;

    // Move mouse to scroll position
    if (x !== undefined && y !== undefined) {
      robot.moveMouse(x, y);
      this.mousePosition = { x, y };
    }

    try {
      // Convert scroll deltas to robot.js scroll
      const scrollX = Math.round(deltaX / 10);
      const scrollY = Math.round(deltaY / 10);

      if (scrollY !== 0) {
        robot.scrollMouse(scrollX, scrollY);
      }
    } catch (error) {
      this.logger.error('Scroll event error:', error.message);
      throw error;
    }
  }

  getKeyCombo(key, modifiers) {
    const sortedModifiers = modifiers.sort();
    return [...sortedModifiers, key].join('+').toLowerCase();
  }

  // Clipboard operations
  async getClipboard() {
    try {
      // Note: robot.js doesn't have clipboard support
      // This would need platform-specific implementation
      if (process.platform === 'win32') {
        return await this.getClipboardWindows();
      } else if (process.platform === 'darwin') {
        return await this.getClipboardMacOS();
      } else {
        return await this.getClipboardLinux();
      }
    } catch (error) {
      this.logger.error('Failed to get clipboard:', error.message);
      return '';
    }
  }

  async setClipboard(text) {
    try {
      if (process.platform === 'win32') {
        await this.setClipboardWindows(text);
      } else if (process.platform === 'darwin') {
        await this.setClipboardMacOS(text);
      } else {
        await this.setClipboardLinux(text);
      }
    } catch (error) {
      this.logger.error('Failed to set clipboard:', error.message);
    }
  }

  async getClipboardWindows() {
    const { exec } = require('child_process');
    return new Promise((resolve, reject) => {
      exec('powershell.exe -command "Get-Clipboard"', (error, stdout) => {
        if (error) {
          reject(error);
        } else {
          resolve(stdout.trim());
        }
      });
    });
  }

  async setClipboardWindows(text) {
    const { exec } = require('child_process');
    return new Promise((resolve, reject) => {
      exec(`powershell.exe -command "Set-Clipboard -Value '${text.replace(/'/g, "''")}'`, (error) => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  async getClipboardMacOS() {
    const { exec } = require('child_process');
    return new Promise((resolve, reject) => {
      exec('pbpaste', (error, stdout) => {
        if (error) {
          reject(error);
        } else {
          resolve(stdout);
        }
      });
    });
  }

  async setClipboardMacOS(text) {
    const { exec } = require('child_process');
    return new Promise((resolve, reject) => {
      exec(`echo '${text.replace(/'/g, "\\'")}' | pbcopy`, (error) => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  async getClipboardLinux() {
    const { exec } = require('child_process');
    return new Promise((resolve, reject) => {
      exec('xclip -selection clipboard -o', (error, stdout) => {
        if (error) {
          reject(error);
        } else {
          resolve(stdout);
        }
      });
    });
  }

  async setClipboardLinux(text) {
    const { exec } = require('child_process');
    return new Promise((resolve, reject) => {
      exec(`echo '${text.replace(/'/g, "\\'")}' | xclip -selection clipboard`, (error) => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  // Utility methods
  getCurrentMousePosition() {
    try {
      return robot.getMousePos();
    } catch (error) {
      this.logger.error('Failed to get mouse position:', error.message);
      return this.mousePosition;
    }
  }

  getScreenSize() {
    return this.screenSize;
  }

  updateScreenSize(width, height) {
    this.screenSize = { width, height };
    this.logger.info(`Screen size updated: ${width}x${height}`);
  }

  getStats() {
    const runtime = Date.now() - this.stats.startTime;
    
    return {
      ...this.stats,
      runtime: runtime,
      eventsPerSecond: this.stats.totalEvents / (runtime / 1000),
      isEnabled: this.isEnabled,
      queueLength: this.inputQueue.length,
      screenSize: this.screenSize,
      mousePosition: this.mousePosition
    };
  }

  // Security methods
  addAllowedKey(key) {
    this.allowedKeys.add(key.toLowerCase());
    this.logger.info('Added allowed key:', key);
  }

  removeAllowedKey(key) {
    this.allowedKeys.delete(key.toLowerCase());
    this.logger.info('Removed allowed key:', key);
  }

  addBlockedKey(keyCombo) {
    this.blockedKeys.add(keyCombo.toLowerCase());
    this.logger.info('Added blocked key combination:', keyCombo);
  }

  removeBlockedKey(keyCombo) {
    this.blockedKeys.delete(keyCombo.toLowerCase());
    this.logger.info('Removed blocked key combination:', keyCombo);
  }

  cleanup() {
    this.logger.info('Cleaning up input handler...');
    this.disable();
    this.inputQueue = [];
    this.removeAllListeners();
    this.logger.info('Input handler cleanup complete');
  }
}

module.exports = InputHandler;
