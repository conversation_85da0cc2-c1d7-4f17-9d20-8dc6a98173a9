/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    overflow-x: hidden;
    line-height: 1.6;
    position: relative;
}

/* Background Elements */
.background-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.3;
}

.bg-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 255, 136, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 136, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

.bg-particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.bg-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #00ff88;
    border-radius: 50%;
    opacity: 0.6;
    animation: floatParticle 15s ease-in-out infinite;
}

.bg-particle:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 12s;
}

.bg-particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 3s;
    animation-duration: 18s;
}

.bg-particle:nth-child(3) {
    top: 80%;
    left: 20%;
    animation-delay: 6s;
    animation-duration: 15s;
}

.bg-particle:nth-child(4) {
    top: 30%;
    left: 70%;
    animation-delay: 9s;
    animation-duration: 20s;
}

.bg-particle:nth-child(5) {
    top: 50%;
    left: 50%;
    animation-delay: 12s;
    animation-duration: 14s;
}

@keyframes floatParticle {
    0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.3; }
    25% { transform: translateY(-30px) translateX(20px); opacity: 0.8; }
    50% { transform: translateY(-10px) translateX(-15px); opacity: 0.6; }
    75% { transform: translateY(-40px) translateX(10px); opacity: 0.9; }
}

.bg-lines {
    position: absolute;
    width: 100%;
    height: 100%;
}

.bg-line {
    position: absolute;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.2), transparent);
    height: 1px;
    animation: lineMove 8s ease-in-out infinite;
}

.bg-line.line-1 {
    top: 25%;
    width: 300px;
    left: -300px;
    animation-delay: 0s;
}

.bg-line.line-2 {
    top: 55%;
    width: 400px;
    right: -400px;
    animation-delay: 3s;
    animation-direction: reverse;
}

.bg-line.line-3 {
    top: 75%;
    width: 250px;
    left: -250px;
    animation-delay: 6s;
}

@keyframes lineMove {
    0% { transform: translateX(0); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateX(calc(100vw + 300px)); opacity: 0; }
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #0a0a0a;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.loading-sphere {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
    position: relative;
    animation: loadingSpin 2s linear infinite;
    box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
}

.loading-sphere-inner {
    position: absolute;
    top: 20%;
    left: 20%;
    width: 60%;
    height: 60%;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    border-radius: 50%;
    transform: rotate(-45deg);
}

.loading-text {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
    font-weight: 500;
    color: #ffffff;
}

.loading-dots {
    display: flex;
    gap: 0.2rem;
}

.loading-dots span {
    animation: loadingDots 1.5s ease-in-out infinite;
}

.loading-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

.loading-progress {
    width: 200px;
    height: 3px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
}

.loading-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #00ff88, #00cc6a);
    border-radius: 2px;
    animation: loadingProgress 3s ease-in-out infinite;
}

@keyframes loadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes loadingDots {
    0%, 60%, 100% { opacity: 0.3; }
    30% { opacity: 1; }
}

@keyframes loadingProgress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 1.2rem;
}

/* Futuristic Logo */
.brand-logo {
    width: 40px;
    height: 40px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-core {
    width: 16px;
    height: 16px;
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    border-radius: 50%;
    position: relative;
    z-index: 3;
    animation: logoCorePulse 2s ease-in-out infinite;
}

.logo-ring {
    position: absolute;
    width: 30px;
    height: 30px;
    border: 2px solid rgba(0, 255, 136, 0.4);
    border-radius: 50%;
    border-top-color: #00ff88;
    animation: logoRingRotate 3s linear infinite;
}

.logo-dots {
    position: absolute;
    width: 100%;
    height: 100%;
}

.logo-dots .dot {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #00ff88;
    border-radius: 50%;
    animation: logoDotOrbit 4s linear infinite;
}

.logo-dots .dot-1 {
    top: 2px;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
}

.logo-dots .dot-2 {
    top: 50%;
    right: 2px;
    transform: translateY(-50%);
    animation-delay: 1.33s;
}

.logo-dots .dot-3 {
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 2.66s;
}

.brand-text {
    font-weight: 700;
    font-size: 1.1rem;
    letter-spacing: 0.1em;
    background: linear-gradient(135deg, #ffffff 0%, #00ff88 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

@keyframes logoCorePulse {
    0%, 100% { transform: scale(1); box-shadow: 0 0 5px rgba(0, 255, 136, 0.5); }
    50% { transform: scale(1.1); box-shadow: 0 0 15px rgba(0, 255, 136, 0.8); }
}

@keyframes logoRingRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes logoDotOrbit {
    0% { opacity: 1; transform: translateX(-50%) translateY(-50%) rotate(0deg) translateX(15px); }
    25% { opacity: 0.5; }
    50% { opacity: 1; }
    75% { opacity: 0.5; }
    100% { opacity: 1; transform: translateX(-50%) translateY(-50%) rotate(360deg) translateX(15px); }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 400;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #00ff88;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icons a {
    color: #ffffff;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.social-icons a:hover {
    color: #00ff88;
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 1001;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #ffffff;
    margin: 3px 0;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-menu {
    display: none;
    position: fixed;
    top: 80px;
    left: 0;
    width: 100%;
    height: calc(100vh - 80px);
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    z-index: 999;
    padding: 2rem;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.mobile-menu.active {
    transform: translateX(0);
}

.mobile-nav-menu {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 3rem;
}

.mobile-nav-menu a {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav-menu a:hover {
    color: #00ff88;
}

.mobile-social-icons {
    display: flex;
    gap: 2rem;
    justify-content: center;
}

.mobile-social-icons a {
    color: #ffffff;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.mobile-social-icons a:hover {
    color: #00ff88;
}

/* Main Content */
.main-content {
    margin-top: 80px;
    min-height: 100vh;
}

/* Hero Section */
.hero {
    padding: 4rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 255, 136, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 255, 136, 0.05) 0%, transparent 50%),
        url('images/1753261350942.png') center/cover no-repeat;
    background-blend-mode: overlay;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(10, 10, 10, 0.7);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 3rem;
    align-items: start;
    min-height: 80vh;
}

/* Hero Left */
.hero-left {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.hero-title {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.title-small {
    font-size: 1.2rem;
    font-weight: 300;
    color: #cccccc;
}

.vision-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.vision-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ffffff;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #00ff88;
}

.stat-label {
    font-size: 0.8rem;
    color: #cccccc;
}

.solution-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.solution-card h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.solution-card p {
    font-size: 0.9rem;
    color: #cccccc;
    margin-bottom: 1rem;
}

.card-stats {
    display: flex;
    gap: 1rem;
}

.card-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.card-stat .number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #00ff88;
}

.card-stat .label {
    font-size: 0.8rem;
    color: #cccccc;
}

/* Hero Center */
.hero-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
}

.main-title {
    font-size: 4rem;
    font-weight: 700;
    letter-spacing: 0.2em;
    color: #333333;
    margin-bottom: 2rem;
    text-transform: uppercase;
}

.sphere-container {
    position: relative;
    width: 300px;
    height: 300px;
    margin: 2rem 0;
}

.sphere {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    position: relative;
    box-shadow:
        0 20px 40px rgba(0, 255, 136, 0.2),
        inset 0 10px 20px rgba(0, 0, 0, 0.2);
    animation: float 6s ease-in-out infinite, glowPulse 4s ease-in-out infinite;
    transition: all 0.4s ease;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sphere-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    filter: brightness(1.1) contrast(1.2);
    transition: all 0.4s ease;
}

.sphere:hover {
    animation-play-state: paused;
    transform: scale(1.1);
    box-shadow:
        0 30px 60px rgba(0, 255, 136, 0.4),
        inset 0 15px 30px rgba(0, 0, 0, 0.3);
}

.sphere:hover .sphere-image {
    filter: brightness(1.3) contrast(1.4) saturate(1.2);
    transform: scale(1.05);
}

.sphere::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 20%;
    width: 60%;
    height: 60%;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    border-radius: 50%;
    transform: rotate(-45deg);
}

.sphere::after {
    content: '';
    position: absolute;
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
    background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 50%);
    border-radius: 50%;
    animation: sphereReflection 4s ease-in-out infinite;
}

@keyframes sphereReflection {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 0.9; transform: scale(1.05); }
}

.sphere-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle, rgba(0, 255, 136, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 4s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

.center-stats {
    margin-top: 2rem;
}

.center-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.center-stat .number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #00ff88;
}

.center-stat .label {
    font-size: 0.9rem;
    color: #cccccc;
}

/* Hero Right */
.hero-right {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.right-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-card .number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #00ff88;
}

.stat-card .label {
    font-size: 0.8rem;
    color: #cccccc;
}

.innovation-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.innovation-card h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.geometric-pattern {
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #00ff88 0%, transparent 50%);
    opacity: 0.1;
    clip-path: polygon(0 0, 100% 0, 100% 100%);
}

.software-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.software-section h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.software-visual {
    width: 100%;
    height: 80px;
    background: linear-gradient(90deg, #333333 0%, #555555 50%, #333333 100%);
    border-radius: 8px;
    margin: 1rem 0;
    position: relative;
    overflow: hidden;
}

.software-visual::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(0, 255, 136, 0.3) 50%, transparent 100%);
    animation: scan 3s linear infinite;
}

@keyframes scan {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Enhanced Animations */
@keyframes slideInLeft {
    0% {
        opacity: 0;
        transform: translateX(-50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    0% {
        opacity: 0;
        transform: translateY(50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    0% {
        opacity: 0;
        transform: rotate(-180deg) scale(0.5);
    }
    100% {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

@keyframes glowPulse {
    0%, 100% {
        box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
    }
    50% {
        box-shadow: 0 0 40px rgba(0, 255, 136, 0.8);
    }
}

@keyframes textGlow {
    0%, 100% {
        text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    }
    50% {
        text-shadow: 0 0 20px rgba(0, 255, 136, 1);
    }
}

@keyframes borderGlow {
    0%, 100% {
        border-color: rgba(0, 255, 136, 0.3);
        box-shadow: 0 0 10px rgba(0, 255, 136, 0.2);
    }
    50% {
        border-color: rgba(0, 255, 136, 0.8);
        box-shadow: 0 0 25px rgba(0, 255, 136, 0.5);
    }
}

.software-section p {
    font-size: 0.9rem;
    color: #cccccc;
}

/* Services Section */
.services {
    display: flex;
    justify-content: center;
    gap: 2rem;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.service-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    flex: 1;
    max-width: 300px;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-blend-mode: overlay;
}

.service-item:nth-child(1) {
    background-image:
        linear-gradient(rgba(10, 10, 10, 0.8), rgba(10, 10, 10, 0.8)),
        url('images/1753265683925.png');
}

.service-item:nth-child(2) {
    background-image:
        linear-gradient(rgba(10, 10, 10, 0.8), rgba(10, 10, 10, 0.8)),
        url('images/1753265421200.png');
}

.service-item:nth-child(3) {
    background-image:
        linear-gradient(rgba(10, 10, 10, 0.8), rgba(10, 10, 10, 0.8)),
        url('images/1753265350724.png');
}

.service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.1), transparent);
    transition: left 0.6s ease;
}

.service-item:hover::before {
    left: 100%;
}

.service-item:hover {
    transform: translateY(-10px) scale(1.02);
    border-color: rgba(0, 255, 136, 0.5);
    box-shadow: 0 15px 35px rgba(0, 255, 136, 0.2);
}

.service-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #00ff88;
    transition: all 0.3s ease;
}

.service-item:hover h3 {
    animation: textGlow 2s ease-in-out infinite;
}

.service-item p {
    font-size: 0.9rem;
    color: #cccccc;
    transition: color 0.3s ease;
}

.service-item:hover p {
    color: #ffffff;
}

/* Service Icons */
.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* AI Solutions Icon */
.ai-icon .icon-core {
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    border-radius: 50%;
    position: relative;
    z-index: 3;
    animation: aiPulse 2s ease-in-out infinite;
}

.ai-icon .ring {
    position: absolute;
    border: 2px solid rgba(0, 255, 136, 0.3);
    border-radius: 50%;
    animation: aiRotate 4s linear infinite;
}

.ai-icon .ring-1 {
    width: 50px;
    height: 50px;
    animation-duration: 3s;
}

.ai-icon .ring-2 {
    width: 65px;
    height: 65px;
    animation-duration: 4s;
    animation-direction: reverse;
}

.ai-icon .ring-3 {
    width: 80px;
    height: 80px;
    animation-duration: 5s;
}

@keyframes aiPulse {
    0%, 100% { transform: scale(1); box-shadow: 0 0 10px rgba(0, 255, 136, 0.5); }
    50% { transform: scale(1.1); box-shadow: 0 0 20px rgba(0, 255, 136, 0.8); }
}

@keyframes aiRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Consulting Icon */
.consulting-icon .icon-base {
    width: 40px;
    height: 40px;
    background: rgba(0, 255, 136, 0.1);
    border: 2px solid #00ff88;
    border-radius: 8px;
    position: relative;
    z-index: 2;
}

.consulting-icon .node {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #00ff88;
    border-radius: 50%;
    animation: nodeGlow 2s ease-in-out infinite;
}

.consulting-icon .node-1 {
    top: -10px;
    left: -10px;
    animation-delay: 0s;
}

.consulting-icon .node-2 {
    top: -10px;
    right: -10px;
    animation-delay: 0.5s;
}

.consulting-icon .node-3 {
    bottom: -10px;
    left: -10px;
    animation-delay: 1s;
}

.consulting-icon .node-4 {
    bottom: -10px;
    right: -10px;
    animation-delay: 1.5s;
}

.consulting-icon .connection {
    position: absolute;
    background: linear-gradient(45deg, transparent, #00ff88, transparent);
    height: 2px;
    animation: connectionFlow 3s ease-in-out infinite;
}

.consulting-icon .con-1 {
    width: 30px;
    top: 15px;
    left: -15px;
    transform: rotate(45deg);
}

.consulting-icon .con-2 {
    width: 30px;
    top: 15px;
    right: -15px;
    transform: rotate(-45deg);
    animation-delay: 1s;
}

.consulting-icon .con-3 {
    width: 42px;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 2s;
}

@keyframes nodeGlow {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

@keyframes connectionFlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

/* Development Icon */
.development-icon .code-blocks {
    position: relative;
    width: 60px;
    height: 50px;
}

.development-icon .code-block {
    position: absolute;
    background: #00ff88;
    border-radius: 2px;
    animation: codeType 3s ease-in-out infinite;
}

.development-icon .block-1 {
    width: 25px;
    height: 4px;
    top: 5px;
    left: 0;
    animation-delay: 0s;
}

.development-icon .block-2 {
    width: 35px;
    height: 4px;
    top: 15px;
    left: 10px;
    animation-delay: 0.5s;
}

.development-icon .block-3 {
    width: 20px;
    height: 4px;
    top: 25px;
    left: 5px;
    animation-delay: 1s;
}

.development-icon .code-cursor {
    position: absolute;
    width: 2px;
    height: 15px;
    background: #00ff88;
    top: 20px;
    right: 10px;
    animation: cursorBlink 1s ease-in-out infinite;
}

@keyframes codeType {
    0%, 70%, 100% { opacity: 0.3; transform: scaleX(1); }
    35% { opacity: 1; transform: scaleX(1.1); }
}

@keyframes cursorBlink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Testimonials Section */
.testimonials-section {
    padding: 4rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    background:
        linear-gradient(rgba(10, 10, 10, 0.9), rgba(10, 10, 10, 0.9)),
        url('images/1753265210270.png') center/cover no-repeat;
    border-radius: 20px;
    margin-bottom: 4rem;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    background: linear-gradient(135deg, #ffffff 0%, #00ff88 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.05), transparent);
    transition: left 0.6s ease;
}

.testimonial-card:hover::before {
    left: 100%;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 255, 136, 0.3);
    box-shadow: 0 20px 40px rgba(0, 255, 136, 0.1);
}

.testimonial-content {
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
}

.quote-icon {
    font-size: 2rem;
    color: #00ff88;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #cccccc;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000000;
    font-size: 1.2rem;
    position: relative;
    overflow: hidden;
}

.author-avatar .avatar-bg {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: avatarShine 3s ease-in-out infinite;
}

@keyframes avatarShine {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.author-info h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.author-info span {
    font-size: 0.9rem;
    color: #888888;
}

/* Contact Section */
.contact-section {
    padding: 4rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
    background:
        linear-gradient(rgba(10, 10, 10, 0.9), rgba(10, 10, 10, 0.9)),
        url('images/1753261430998.png') center/cover no-repeat;
    border-radius: 20px;
    margin-bottom: 4rem;
    position: relative;
}

.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #ffffff;
    background: linear-gradient(135deg, #ffffff 0%, #00ff88 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.contact-info p {
    font-size: 1.1rem;
    color: #cccccc;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.contact-item:hover {
    border-color: rgba(0, 255, 136, 0.3);
    transform: translateX(10px);
}

.contact-item i {
    font-size: 1.5rem;
    color: #00ff88;
    width: 30px;
    text-align: center;
}

.contact-item h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.contact-item p {
    font-size: 0.9rem;
    color: #cccccc;
    margin: 0;
}

/* Contact Form */
.contact-form-container {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #ffffff;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00ff88;
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
    background: rgba(255, 255, 255, 0.08);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #888888;
}

.error-message {
    font-size: 0.8rem;
    color: #ff4444;
    display: none;
}

.error-message.show {
    display: block;
}

.submit-btn {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
    border: none;
    border-radius: 8px;
    color: #000000;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 255, 136, 0.4);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn.loading {
    pointer-events: none;
}

.btn-text,
.btn-loading {
    transition: opacity 0.3s ease;
}

.btn-loading {
    display: none;
}

.submit-btn.loading .btn-text {
    display: none;
}

.submit-btn.loading .btn-loading {
    display: inline;
}

/* Bottom Section */
.bottom-section {
    padding: 4rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.vision-bottom {
    text-align: center;
}

.vision-bottom h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: #ffffff;
}

.description-cards {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.desc-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 500px;
}

.desc-card h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #00ff88;
}

.desc-card p {
    font-size: 1rem;
    color: #cccccc;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .main-title {
        font-size: 3rem;
    }

    .sphere-container {
        width: 250px;
        height: 250px;
    }

    .contact-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-info h2 {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .social-icons {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mobile-menu {
        display: block;
    }

    .services {
        flex-direction: column;
        align-items: center;
    }

    .description-cards {
        flex-direction: column;
        align-items: center;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .sphere-container {
        width: 200px;
        height: 200px;
    }
}

/* Enhanced Animation Classes */
.animate-slide-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.8s ease;
}

.animate-slide-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.8s ease;
}

.animate-fade-scale {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.8s ease;
}

.animate-in {
    opacity: 1;
    transform: translateX(0) translateY(0) scale(1);
}

/* Loading animations */
.loading-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Performance Optimizations */
.low-performance .sphere {
    animation: none;
}

.low-performance .particle {
    display: none;
}

.low-performance .loading-shimmer {
    animation: none;
}

/* Lazy loading styles */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .sphere {
        animation: none;
    }

    .particle {
        display: none;
    }
}
