# Test Generate-and-Wait Endpoint with New Features
Write-Host "Testing Generate-and-Wait Endpoint" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green

# Check service
try {
    $health = Invoke-RestMethod -Uri "http://localhost:7777/health" -TimeoutSec 5
    Write-Host "Service is healthy" -ForegroundColor Green
} catch {
    Write-Host "Service not available" -ForegroundColor Red
    exit 1
}

# Test with new parameters
$body = @{
    type = "image"
    prompt = "Green technology innovation concept"
    width = 800
    height = 600
    seed = 54321
    timeout = 180
    priority = "high"
} | ConvertTo-Json

Write-Host "`nUsing generate-and-wait endpoint..." -ForegroundColor Cyan
Write-Host "Request body:" -ForegroundColor Gray
Write-Host $body -ForegroundColor Gray

try {
    Write-Host "`nSubmitting request and waiting for completion..." -ForegroundColor Yellow
    $response = Invoke-RestMethod -Uri "http://localhost:7777/api/generate-and-wait" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 200
    
    if ($response.success -and $response.status -eq "completed") {
        Write-Host "SUCCESS! Image generated via generate-and-wait!" -ForegroundColor Green
        Write-Host "  Request ID: $($response.requestId)" -ForegroundColor White
        Write-Host "  Status: $($response.status)" -ForegroundColor White
        Write-Host "  File Path: $($response.filePath)" -ForegroundColor White
        Write-Host "  Created: $($response.createdAt)" -ForegroundColor White
        Write-Host "  Completed: $($response.completedAt)" -ForegroundColor White
        
        # Show image parameters
        if ($response.imageParameters) {
            Write-Host "Image Parameters:" -ForegroundColor Cyan
            Write-Host "  Dimensions: $($response.imageParameters.width)x$($response.imageParameters.height)" -ForegroundColor White
            Write-Host "  Seed: $($response.imageParameters.seed)" -ForegroundColor White
        }
        
        # Show generation parameters
        if ($response.generationParameters) {
            Write-Host "Generation Parameters:" -ForegroundColor Cyan
            Write-Host "  Timeout: $($response.generationParameters.timeout)s" -ForegroundColor White
            Write-Host "  Priority: $($response.generationParameters.priority)" -ForegroundColor White
        }
        
        # Download image
        $outputPath = "images\test-wait-endpoint.jpg"
        Write-Host "`nDownloading image..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri "http://localhost:7777/api/image/$($response.requestId)" -OutFile $outputPath
        
        if (Test-Path $outputPath) {
            $fileSize = (Get-Item $outputPath).Length
            Write-Host "Downloaded successfully! Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
            Write-Host "File saved as: $outputPath" -ForegroundColor Green
        } else {
            Write-Host "Download failed!" -ForegroundColor Red
        }
        
        Write-Host "`nGenerate-and-wait test completed successfully!" -ForegroundColor Green
        
    } else {
        Write-Host "Request failed or incomplete" -ForegroundColor Red
        Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
