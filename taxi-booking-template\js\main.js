// Taxi Booking Template JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initBookingForm();
    initContactForm();
    initScrollAnimations();
    initMobileMenu();
});

// Booking Form Functionality
function initBookingForm() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const scheduleTimeField = document.querySelector('.schedule-time');
    const currentLocationBtn = document.getElementById('current-location');
    const bookBtn = document.querySelector('.book-btn');
    const pickupInput = document.getElementById('pickup-input');
    const destinationInput = document.getElementById('destination-input');
    const rideTypeSelect = document.getElementById('ride-type');

    // Tab switching functionality
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // Remove active class from all tabs
            tabBtns.forEach(tab => tab.classList.remove('active'));
            // Add active class to clicked tab
            btn.classList.add('active');

            const tabType = btn.getAttribute('data-type');
            
            if (tabType === 'later') {
                scheduleTimeField.style.display = 'block';
                bookBtn.innerHTML = '<i class="fas fa-calendar"></i> Schedule Ride';
            } else {
                scheduleTimeField.style.display = 'none';
                bookBtn.innerHTML = '<i class="fas fa-search"></i> Find Rides';
            }
        });
    });

    // Current location functionality
    currentLocationBtn?.addEventListener('click', () => {
        if (navigator.geolocation) {
            currentLocationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    // Simulate reverse geocoding
                    setTimeout(() => {
                        pickupInput.value = 'Current Location';
                        currentLocationBtn.innerHTML = '<i class="fas fa-crosshairs"></i>';
                        showNotification('Location detected successfully!', 'success');
                    }, 1000);
                },
                (error) => {
                    currentLocationBtn.innerHTML = '<i class="fas fa-crosshairs"></i>';
                    showNotification('Unable to detect location. Please enter manually.', 'error');
                }
            );
        } else {
            showNotification('Geolocation is not supported by this browser.', 'error');
        }
    });

    // Book ride functionality
    bookBtn?.addEventListener('click', (e) => {
        e.preventDefault();
        
        const pickup = pickupInput.value.trim();
        const destination = destinationInput.value.trim();
        const rideType = rideTypeSelect.value;

        if (!pickup || !destination) {
            showNotification('Please enter both pickup and destination locations.', 'error');
            return;
        }

        // Simulate ride search
        bookBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
        bookBtn.disabled = true;

        setTimeout(() => {
            showRideOptions(pickup, destination, rideType);
            bookBtn.innerHTML = '<i class="fas fa-search"></i> Find Rides';
            bookBtn.disabled = false;
        }, 2000);
    });

    // Auto-complete simulation for location inputs
    [pickupInput, destinationInput].forEach(input => {
        if (input) {
            input.addEventListener('input', debounce(handleLocationInput, 300));
        }
    });
}

function handleLocationInput(event) {
    const query = event.target.value;
    if (query.length < 3) return;

    // Simulate location suggestions
    console.log(`Searching for locations matching: ${query}`);
    // In a real app, this would call a geocoding API
}

function showRideOptions(pickup, destination, rideType) {
    // Simulate ride options display
    const rideOptions = [
        { type: 'Economy', price: '$12.50', eta: '3 min', driver: 'John D.' },
        { type: 'Comfort', price: '$16.75', eta: '5 min', driver: 'Sarah M.' },
        { type: 'Premium', price: '$24.00', eta: '7 min', driver: 'Mike R.' }
    ];

    let optionsHtml = `
        <div class="ride-options-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Available Rides</h3>
                    <button class="close-modal">&times;</button>
                </div>
                <div class="trip-info">
                    <p><strong>From:</strong> ${pickup}</p>
                    <p><strong>To:</strong> ${destination}</p>
                </div>
                <div class="ride-options">
    `;

    rideOptions.forEach(option => {
        optionsHtml += `
            <div class="ride-option" data-type="${option.type}">
                <div class="option-info">
                    <h4>${option.type}</h4>
                    <p>Driver: ${option.driver}</p>
                    <p>ETA: ${option.eta}</p>
                </div>
                <div class="option-price">${option.price}</div>
            </div>
        `;
    });

    optionsHtml += `
                </div>
                <button class="btn btn-primary confirm-ride">Confirm Ride</button>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', optionsHtml);

    // Add event listeners for the modal
    const modal = document.querySelector('.ride-options-modal');
    const closeBtn = modal.querySelector('.close-modal');
    const rideOptionElements = modal.querySelectorAll('.ride-option');
    const confirmBtn = modal.querySelector('.confirm-ride');

    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    rideOptionElements.forEach(option => {
        option.addEventListener('click', () => {
            rideOptionElements.forEach(opt => opt.classList.remove('selected'));
            option.classList.add('selected');
        });
    });

    confirmBtn.addEventListener('click', () => {
        const selectedOption = modal.querySelector('.ride-option.selected');
        if (selectedOption) {
            const rideType = selectedOption.getAttribute('data-type');
            showNotification(`${rideType} ride booked successfully! Driver will arrive shortly.`, 'success');
            document.body.removeChild(modal);
        } else {
            showNotification('Please select a ride option.', 'error');
        }
    });

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

// Contact Form Functionality
function initContactForm() {
    const contactForm = document.querySelector('.contact-form form');
    
    contactForm?.addEventListener('submit', (e) => {
        e.preventDefault();
        
        const formData = new FormData(contactForm);
        const name = contactForm.querySelector('input[type="text"]').value;
        const email = contactForm.querySelector('input[type="email"]').value;
        const topic = contactForm.querySelector('select').value;
        const message = contactForm.querySelector('textarea').value;

        if (!name || !email || !topic || !message) {
            showNotification('Please fill in all fields.', 'error');
            return;
        }

        // Simulate form submission
        const submitBtn = contactForm.querySelector('.btn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Sending...';
        submitBtn.disabled = true;

        setTimeout(() => {
            showNotification('Message sent successfully! We\'ll get back to you soon.', 'success');
            contactForm.reset();
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });
}

// Scroll Animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-item, .service-card, .step-item, .safety-feature, .benefit-item');
    animateElements.forEach(el => observer.observe(el));
}

// Mobile Menu
function initMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const nav = document.querySelector('.nav');

    mobileToggle?.addEventListener('click', () => {
        nav.classList.toggle('active');
        const icon = mobileToggle.querySelector('i');
        
        if (nav.classList.contains('active')) {
            icon.classList.remove('fa-bars');
            icon.classList.add('fa-times');
        } else {
            icon.classList.remove('fa-times');
            icon.classList.add('fa-bars');
        }
    });

    // Close menu when clicking on links
    const navLinks = document.querySelectorAll('.nav-list a');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            nav.classList.remove('active');
            const icon = mobileToggle.querySelector('i');
            icon.classList.remove('fa-times');
            icon.classList.add('fa-bars');
        });
    });
}

// Utility Functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '1rem 1.5rem',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '500',
        zIndex: '9999',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        maxWidth: '300px'
    });

    // Set background color based on type
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        info: '#fbbf24',
        warning: '#f59e0b'
    };
    notification.style.backgroundColor = colors[type] || colors.info;
    notification.style.color = type === 'info' ? '#1f2937' : 'white';

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 4 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Header scroll effect
window.addEventListener('scroll', () => {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(255, 255, 255, 0.98)';
        header.style.backdropFilter = 'blur(20px)';
    } else {
        header.style.background = 'rgba(255, 255, 255, 0.95)';
        header.style.backdropFilter = 'blur(10px)';
    }
});

// Add CSS for animations and modal
const style = document.createElement('style');
style.textContent = `
    .animate-in {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .ride-options-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
    }

    .modal-content {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .close-modal {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #6b7280;
    }

    .trip-info {
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: #f8fafc;
        border-radius: 8px;
    }

    .ride-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        margin-bottom: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .ride-option:hover {
        border-color: #fbbf24;
    }

    .ride-option.selected {
        border-color: #fbbf24;
        background: #fffbeb;
    }

    .option-price {
        font-size: 1.2rem;
        font-weight: 700;
        color: #fbbf24;
    }

    .confirm-ride {
        width: 100%;
        margin-top: 1rem;
    }

    @media (max-width: 768px) {
        .nav.active {
            display: block;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 1rem;
        }

        .nav-list {
            flex-direction: column;
            gap: 1rem;
        }
    }
`;
document.head.appendChild(style);
