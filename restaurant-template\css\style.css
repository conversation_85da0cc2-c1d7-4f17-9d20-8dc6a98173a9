/* Restaurant Template Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.logo h1 {
    color: #dc2626;
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    font-weight: 700;
}

.logo span {
    color: #6b7280;
    font-size: 0.9rem;
    font-weight: 400;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-list a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-list a:hover {
    color: #dc2626;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-primary {
    background: #dc2626;
    color: white;
}

.btn-primary:hover {
    background: #b91c1c;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: #dc2626;
    border: 2px solid #dc2626;
}

.btn-outline:hover {
    background: #dc2626;
    color: white;
}

.btn-secondary {
    background: #f59e0b;
    color: white;
}

.btn-secondary:hover {
    background: #d97706;
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.8), rgba(185, 28, 28, 0.6));
    z-index: -1;
}

.hero-content {
    position: relative;
    z-index: 1;
    color: white;
    text-align: center;
    width: 100%;
}

.hero-text h1 {
    font-family: 'Playfair Display', serif;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Features Section */
.features {
    padding: 4rem 0;
    background: #f8fafc;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-item i {
    font-size: 3rem;
    color: #dc2626;
    margin-bottom: 1rem;
}

.feature-item h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.feature-item p {
    color: #6b7280;
    line-height: 1.6;
}

/* Menu Section */
.menu-section {
    padding: 4rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
}

.menu-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 0.75rem 2rem;
    border: 2px solid #e5e7eb;
    background: white;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-btn.active,
.tab-btn:hover {
    background: #dc2626;
    color: white;
    border-color: #dc2626;
}

.menu-content {
    max-width: 800px;
    margin: 0 auto;
}

.menu-category {
    display: none;
}

.menu-category.active {
    display: block;
}

.menu-items {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.menu-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.menu-item:hover {
    transform: translateY(-2px);
}

.item-info h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.item-info p {
    color: #6b7280;
    line-height: 1.6;
}

.item-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #dc2626;
    margin-left: 2rem;
}

/* About Section */
.about-section {
    padding: 4rem 0;
    background: #f8fafc;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.about-text p {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.stat {
    text-align: center;
}

.stat .number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #dc2626;
    margin-bottom: 0.5rem;
}

.stat .label {
    font-size: 0.9rem;
    color: #6b7280;
}

.about-image {
    background: #e5e7eb;
    border-radius: 16px;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    overflow: hidden;
}

.about-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 16px;
}

/* Chefs Section */
.chefs-section {
    padding: 4rem 0;
}

.chefs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.chef-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.chef-card:hover {
    transform: translateY(-5px);
}

.chef-image {
    background: #f3f4f6;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    overflow: hidden;
}

.chef-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.chef-info {
    padding: 1.5rem;
    text-align: center;
}

.chef-info h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.chef-title {
    color: #dc2626;
    font-weight: 500;
    margin-bottom: 1rem;
}

.chef-bio {
    color: #6b7280;
    line-height: 1.6;
}

/* Reservation Section */
.reservation-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    text-align: center;
}

.reservation-content h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.reservation-content p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.reservation-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

/* Contact Section */
.contact-section {
    padding: 4rem 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.contact-info h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-item i {
    color: #dc2626;
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.contact-item h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.contact-item p {
    color: #6b7280;
    line-height: 1.6;
}

.contact-map {
    background: #f3f4f6;
    border-radius: 16px;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: #6b7280;
}

.map-placeholder i {
    font-size: 3rem;
    color: #dc2626;
    margin-bottom: 1rem;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #dc2626;
}

.footer-section h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-section p {
    opacity: 0.8;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-section ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #dc2626;
}

.footer-section ul li i {
    color: #dc2626;
    width: 16px;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #374151;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #dc2626;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    text-align: center;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .about-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .menu-tabs {
        flex-direction: column;
        align-items: center;
    }
    
    .item-price {
        margin-left: 1rem;
    }
}
