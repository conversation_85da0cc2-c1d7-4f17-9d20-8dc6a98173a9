const axios = require('axios');
const fs = require('fs');

// WORKSPACE-WIDE INTELLIGENT TIMING SYSTEM
// Updated specifications: Min 80s, Max 360s, 4K Max 600s
function calculateImageTiming(width, height) {
  const totalPixels = width * height;
  const megapixels = totalPixels / 1000000;

  // Workspace-wide timing standards
  let timeout = 80; // Minimum timeout: 80 seconds
  let waitTime = 80; // Minimum wait time: 80 seconds
  let category = 'small';

  if (megapixels <= 0.5) {
    // Small images (≤0.5MP): Profile pictures, icons
    timeout = 80;
    waitTime = 80;
    category = 'small';
  } else if (megapixels <= 1.0) {
    // Medium images (0.5-1MP): Standard content
    timeout = 150;
    waitTime = 80;
    category = 'medium';
  } else if (megapixels <= 2.0) {
    // Large images (1-2MP): Hero sections, banners
    timeout = 250;
    waitTime = 80;
    category = 'large';
  } else if (megapixels <= 8.0) {
    // Extra large images (2-8MP): High-res content
    timeout = 360;
    waitTime = 80;
    category = 'extra-large';
  } else {
    // 4K+ images (>8MP): Ultra high-resolution
    timeout = 600;
    waitTime = 80;
    category = '4k-ultra';
  }

  return {
    timeout,
    waitTime,
    megapixels: megapixels.toFixed(2),
    category,
    isHighRes: megapixels > 8.0
  };
}

async function testIntelligentTiming() {
  console.log('🧪 WORKSPACE-WIDE INTELLIGENT TIMING SYSTEM TEST');
  console.log('📋 Testing 3 images with updated timing specifications...');
  console.log('⚙️ Min: 80s | Max: 360s | 4K Max: 600s\n');

  const testImages = [
    {
      prompt: 'Elegant luxury product photography with premium lighting and sophisticated composition, commercial grade quality',
      width: 1024,
      height: 768,
      description: 'Premium Product Shot',
      category: 'Product',
      useCase: 'E-commerce, marketing materials'
    },
    {
      prompt: 'Stunning panoramic landscape with dramatic sky and vibrant colors, ultra-wide cinematic composition',
      width: 2560,
      height: 1080,
      description: 'Ultra-Wide Hero Banner',
      category: 'Ultra-Hero',
      useCase: 'Website headers, large displays'
    },
    {
      prompt: 'Futuristic tech interface with holographic elements and neon accents, high-detail sci-fi aesthetic',
      width: 1920,
      height: 1200,
      description: 'High-Res Tech Interface',
      category: 'Tech-Art',
      useCase: 'App backgrounds, tech presentations'
    }
  ];
  
  const requests = [];
  
  console.log('📊 WORKSPACE TIMING ANALYSIS:');
  console.log('🔧 Updated Standards: Min 80s, Max 360s, 4K Max 600s\n');

  testImages.forEach((image, index) => {
    const timing = calculateImageTiming(image.width, image.height);
    console.log(`   Image ${index + 1}: ${image.description}`);
    console.log(`     Resolution: ${image.width}x${image.height} (${timing.megapixels}MP)`);
    console.log(`     Category: ${timing.category}`);
    console.log(`     Use Case: ${image.useCase}`);
    console.log(`     Timeout: ${timing.timeout}s | Wait: ${timing.waitTime}s`);
    if (timing.isHighRes) {
      console.log(`     🎯 HIGH-RES: Extended timeout for quality`);
    }
    console.log('');
  });
  
  // Submit requests with intelligent timing
  for (let i = 0; i < testImages.length; i++) {
    const image = testImages[i];
    const timing = calculateImageTiming(image.width, image.height);
    
    console.log(`📸 SUBMITTING IMAGE ${i + 1}: ${image.description}`);
    console.log(`   Category: ${timing.category} | Use Case: ${image.useCase}`);
    console.log(`   Prompt: ${image.prompt}`);
    console.log(`   Resolution: ${image.width}x${image.height} (${timing.megapixels}MP)`);
    console.log(`   Workspace Timeout: ${timing.timeout}s (Min: 80s, Max: ${timing.isHighRes ? '600s' : '360s'})`);
    if (timing.isHighRes) {
      console.log(`   🎯 4K+ QUALITY: Extended timeout for ultra-high resolution`);
    }
    
    try {
      const response = await axios.post('http://localhost:7777/api/generate', {
        type: 'image',
        prompt: image.prompt,
        width: image.width,
        height: image.height,
        timeout: timing.timeout
      });
      
      if (response.data.success) {
        console.log(`   ✅ Request ID: ${response.data.requestId}`);
        console.log(`   🤖 Model: ${response.data.model}`);
        requests.push({
          ...image,
          requestId: response.data.requestId,
          index: i + 1,
          timing: timing,
          submittedAt: new Date().toISOString()
        });
      } else {
        console.log(`   ❌ Failed to submit request`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
    
    // Intelligent wait time
    if (i < testImages.length - 1) {
      const waitTime = timing.waitTime;
      console.log(`   ⏰ Waiting ${waitTime} seconds (intelligent timing)...`);
      
      for (let countdown = waitTime; countdown > 0; countdown--) {
        const minutes = Math.floor(countdown / 60);
        const seconds = countdown % 60;
        const timeStr = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
        process.stdout.write(`\r   ⏳ ${timeStr} remaining...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      console.log('\r   ✅ Wait complete!\n');
    } else {
      console.log('');
    }
  }
  
  if (requests.length === 0) {
    console.log('❌ No requests were submitted successfully');
    return;
  }
  
  console.log(`🔄 Monitoring ${requests.length} requests...\n`);
  
  // Monitor completion
  const completed = [];
  const maxWaitTime = 600000; // 10 minutes
  const startTime = Date.now();
  
  while (completed.length < requests.length && (Date.now() - startTime) < maxWaitTime) {
    for (const request of requests) {
      if (completed.find(c => c.requestId === request.requestId)) continue;
      
      try {
        const statusResponse = await axios.get(`http://localhost:7777/api/status/${request.requestId}`);
        const status = statusResponse.data;
        
        if (status.status === 'completed') {
          console.log(`✅ COMPLETED: ${request.description}`);
          console.log(`   Request ID: ${request.requestId}`);
          console.log(`   Image URL: http://localhost:7777${status.imageUrl}`);
          console.log(`   File Path: ${status.filePath}`);
          
          completed.push({
            ...request,
            ...status,
            completedAt: new Date().toISOString()
          });
          console.log('');
          
        } else if (status.status === 'failed') {
          console.log(`❌ FAILED: ${request.description}`);
          console.log(`   Request ID: ${request.requestId}`);
          completed.push({
            ...request,
            ...status,
            failed: true
          });
          console.log('');
          
        } else {
          console.log(`⏳ ${status.status}: ${request.description}`);
        }
        
      } catch (error) {
        console.log(`❌ Error checking ${request.description}: ${error.message}`);
      }
    }
    
    if (completed.length < requests.length) {
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  
  // Results
  console.log('🎯 FINAL RESULTS:');
  console.log('================');
  
  const successful = completed.filter(c => !c.failed);
  const failed = completed.filter(c => c.failed);
  
  console.log(`✅ Successfully generated: ${successful.length}/${requests.length} images`);
  console.log(`❌ Failed: ${failed.length}/${requests.length} images`);
  
  if (successful.length > 0) {
    console.log('\n📊 WORKSPACE PERFORMANCE ANALYSIS:');
    console.log('🔧 Standards: Min 80s | Max 360s | 4K Max 600s\n');

    successful.forEach(img => {
      const created = new Date(img.createdAt);
      const completed = new Date(img.completedAt);
      const actualTime = ((completed - created) / 1000).toFixed(1);
      const efficiency = ((img.timing.timeout / actualTime) * 100).toFixed(1);

      console.log(`   ${img.description}:`);
      console.log(`     Resolution: ${img.width}x${img.height} (${img.timing.megapixels}MP)`);
      console.log(`     Category: ${img.timing.category}`);
      console.log(`     Use Case: ${img.useCase}`);
      console.log(`     Workspace Timeout: ${img.timing.timeout}s | Actual: ${actualTime}s`);
      console.log(`     Efficiency: ${efficiency}%`);
      console.log(`     Quality Level: ${img.timing.isHighRes ? '4K+ Ultra' : 'Standard High'}`);
      console.log(`     URL: http://localhost:7777${img.imageUrl}`);
      console.log('');
    });
    
    const avgActualTime = successful.reduce((sum, img) => {
      const created = new Date(img.createdAt);
      const completed = new Date(img.completedAt);
      return sum + (completed - created);
    }, 0) / successful.length / 1000;
    
    console.log(`\n   Average Generation Time: ${avgActualTime.toFixed(1)}s`);
  }
  
  if (successful.length === requests.length) {
    console.log('\n🎉 INTELLIGENT TIMING TEST PASSED!');
    console.log('✅ All images generated successfully with optimal timing');
  } else {
    console.log('\n⚠️ Some images failed - may be API limitations');
  }
}

testIntelligentTiming().catch(error => {
  console.error('❌ Test failed:', error.message);
});
