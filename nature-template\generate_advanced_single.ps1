# Advanced Single Image Generator with New API Features
param(
    [string]$ImageName = "test-image",
    [string]$Filename = "test-image.jpg",
    [string]$Prompt = "Green nature technology concept",
    [int]$Width = 1024,
    [int]$Height = 1024,
    [int]$Seed = 12345,
    [int]$Timeout = 240,
    [string]$Priority = "high",
    [switch]$UseWaitEndpoint = $false
)

Write-Host "ADVANCED IMAGE GENERATION" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Green
Write-Host "Image: $ImageName" -ForegroundColor Cyan
Write-Host "Prompt: $Prompt" -ForegroundColor Gray
Write-Host "Dimensions: ${Width}x${Height}" -ForegroundColor Cyan
Write-Host "Seed: $Seed" -ForegroundColor Cyan
Write-Host "Timeout: ${Timeout}s" -ForegroundColor Cyan
Write-Host "Priority: $Priority" -ForegroundColor Cyan
Write-Host "Method: $(if($UseWaitEndpoint) {'generate-and-wait'} else {'polling'})" -ForegroundColor Cyan

# Check service health
Write-Host "`nChecking enhanced API service..." -ForegroundColor Cyan
try {
    $health = Invoke-RestMethod -Uri "http://localhost:7777/health" -TimeoutSec 10
    if ($health.status -eq "healthy") {
        Write-Host "Service healthy - Database: $($health.database)" -ForegroundColor Green
    } else {
        Write-Host "Service not healthy" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Cannot connect to API service" -ForegroundColor Red
    exit 1
}

# Get models info
try {
    $models = Invoke-RestMethod -Uri "http://localhost:7777/api/models"
    Write-Host "🎨 Using Image Model: $($models.defaults.image)" -ForegroundColor Gray
} catch {
    Write-Host "⚠️ Could not fetch models info" -ForegroundColor Yellow
}

# Prepare request body with all new parameters
$body = @{
    type = "image"
    prompt = $Prompt
    width = $Width
    height = $Height
    seed = $Seed
    timeout = $Timeout
    priority = $Priority
} | ConvertTo-Json

Write-Host "`n🚀 Starting generation..." -ForegroundColor Cyan

try {
    if ($UseWaitEndpoint) {
        # Use generate-and-wait endpoint
        Write-Host "🔄 Using generate-and-wait endpoint..." -ForegroundColor Cyan
        $response = Invoke-RestMethod -Uri "http://localhost:7777/api/generate-and-wait" -Method POST -Body $body -ContentType "application/json" -TimeoutSec ($Timeout + 30)
        
        if ($response.success -and $response.status -eq "completed") {
            Write-Host "✅ Generation completed!" -ForegroundColor Green
            
            # Download image
            $outputPath = "images\$Filename"
            Write-Host "📥 Downloading to $outputPath..." -ForegroundColor Cyan
            Invoke-WebRequest -Uri "http://localhost:7777/api/image/$($response.requestId)" -OutFile $outputPath
            
            if (Test-Path $outputPath) {
                $fileSize = (Get-Item $outputPath).Length
                Write-Host "✅ SUCCESS!" -ForegroundColor Green
                Write-Host "   📊 Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
                Write-Host "   📐 Actual Dimensions: ${Width}x${Height}" -ForegroundColor Green
                Write-Host "   🎲 Seed Used: $Seed" -ForegroundColor Green
                Write-Host "   ⏱️ Generation Time: $($response.completedAt - $response.createdAt)" -ForegroundColor Green
                exit 0
            } else {
                Write-Host "❌ Download failed" -ForegroundColor Red
                exit 1
            }
        } else {
            Write-Host "❌ Generation failed: $($response.message)" -ForegroundColor Red
            exit 1
        }
    } else {
        # Use traditional polling method
        Write-Host "🚀 Submitting request..." -ForegroundColor Cyan
        $response = Invoke-RestMethod -Uri "http://localhost:7777/api/generate" -Method POST -Body $body -ContentType "application/json"
        
        if ($response.success) {
            $requestId = $response.requestId
            Write-Host "✅ Request submitted! ID: $requestId" -ForegroundColor Green
            Write-Host "📊 Estimated time: $($response.estimatedTime)" -ForegroundColor Cyan
            Write-Host "📐 Confirmed dimensions: $($response.imageParameters.width)x$($response.imageParameters.height)" -ForegroundColor Cyan
            Write-Host "🎲 Confirmed seed: $($response.imageParameters.seed)" -ForegroundColor Cyan
            
            # Enhanced polling
            $maxAttempts = [math]::Ceiling($Timeout / 10) + 5
            $attempt = 0
            
            Write-Host "⏳ Monitoring status..." -ForegroundColor Yellow
            
            while ($attempt -lt $maxAttempts) {
                $attempt++
                Start-Sleep -Seconds 10
                
                try {
                    $status = Invoke-RestMethod -Uri "http://localhost:7777/api/status/$requestId"
                    
                    if ($status.status -eq "completed") {
                        Write-Host "✅ Generation completed!" -ForegroundColor Green
                        
                        # Download image
                        $outputPath = "images\$Filename"
                        Write-Host "📥 Downloading to $outputPath..." -ForegroundColor Cyan
                        Invoke-WebRequest -Uri "http://localhost:7777/api/image/$requestId" -OutFile $outputPath
                        
                        if (Test-Path $outputPath) {
                            $fileSize = (Get-Item $outputPath).Length
                            Write-Host "✅ SUCCESS!" -ForegroundColor Green
                            Write-Host "   📊 Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
                            Write-Host "   📐 Dimensions: $($status.imageParameters.width)x$($status.imageParameters.height)" -ForegroundColor Green
                            Write-Host "   🎲 Seed: $($status.imageParameters.seed)" -ForegroundColor Green
                            Write-Host "   ⏱️ Total Time: $($status.completedAt - $status.createdAt)" -ForegroundColor Green
                            exit 0
                        } else {
                            Write-Host "❌ Download failed" -ForegroundColor Red
                            exit 1
                        }
                    } elseif ($status.status -eq "failed") {
                        Write-Host "❌ Generation failed!" -ForegroundColor Red
                        exit 1
                    } elseif ($status.status -eq "processing") {
                        Write-Host "🔄 Processing... (attempt $attempt/$maxAttempts)" -ForegroundColor Yellow
                    } elseif ($status.status -eq "pending") {
                        Write-Host "⏸️ Pending... (attempt $attempt/$maxAttempts)" -ForegroundColor Yellow
                    } else {
                        Write-Host "❓ Status: $($status.status) (attempt $attempt/$maxAttempts)" -ForegroundColor Yellow
                    }
                } catch {
                    Write-Host "⚠️ Status check failed: $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }
            
            Write-Host "⏰ Timeout after $maxAttempts attempts" -ForegroundColor Red
            exit 1
        } else {
            Write-Host "❌ Failed to submit request" -ForegroundColor Red
            exit 1
        }
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
