# Green Nature Tech - Simple Image Generation Tracker
Write-Host "🌿 GREEN NATURE TECH - IMAGE GENERATION TRACKER" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

# Check service health
Write-Host "🏥 Checking image generation service..." -ForegroundColor Cyan
try {
    $health = Invoke-RestMethod -Uri "http://localhost:7777/health" -TimeoutSec 5
    if ($health.status -eq "healthy") {
        Write-Host "✅ Service is healthy and ready!" -ForegroundColor Green
    } else {
        Write-Host "❌ Service is not healthy" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Cannot connect to image generation service" -ForegroundColor Red
    exit 1
}

# Define images to generate
$images = @(
    @{
        name = "hero-background"
        filename = "hero-background.jpg"
        prompt = "Dark forest landscape with subtle green technology elements, misty atmosphere, tall trees silhouettes, green glowing accents, professional nature photography, high resolution"
    },
    @{
        name = "green-innovation-card"
        filename = "green-innovation.jpg"
        prompt = "Modern sustainable technology concept, green innovation lab, eco-friendly devices, clean energy solutions, professional photography, bright green accents"
    },
    @{
        name = "forest-technology"
        filename = "forest-tech.jpg"
        prompt = "Dense green forest landscape, sustainable innovation, nature and technology harmony, professional landscape photography"
    },
    @{
        name = "eco-solutions"
        filename = "eco-solutions.jpg"
        prompt = "Eco-friendly technology solutions, renewable energy concepts, sustainable design, green innovation, modern clean aesthetic"
    },
    @{
        name = "green-tech-solutions"
        filename = "green-tech-solutions.jpg"
        prompt = "Green technology solutions showcase, sustainable innovation display, eco-conscious design, modern environmental technology"
    },
    @{
        name = "nature-innovation"
        filename = "nature-innovation.jpg"
        prompt = "Nature-inspired innovation, biomimicry technology, green research and development, sustainable future concepts"
    }
)

$results = @()
$imageCount = 0

foreach ($img in $images) {
    $imageCount++
    Write-Host "`n[$imageCount/$($images.Count)] Processing: $($img.name)" -ForegroundColor Magenta
    Write-Host "Prompt: $($img.prompt)" -ForegroundColor Cyan
    
    # Check if image already exists
    $outputPath = "images\$($img.filename)"
    if (Test-Path $outputPath) {
        Write-Host "⏭️ Image already exists, skipping..." -ForegroundColor Yellow
        $results += @{ name = $img.name; status = "skipped" }
        continue
    }
    
    try {
        # Submit generation request
        $body = @{
            type = "image"
            prompt = $img.prompt
        } | ConvertTo-Json
        
        Write-Host "🚀 Submitting generation request..." -ForegroundColor Cyan
        $response = Invoke-RestMethod -Uri "http://localhost:7777/api/generate" -Method POST -Body $body -ContentType "application/json"
        
        if ($response.success) {
            $requestId = $response.requestId
            Write-Host "✅ Request submitted! ID: $requestId" -ForegroundColor Green
            
            # Wait for completion
            Write-Host "⏳ Waiting 60 seconds for generation..." -ForegroundColor Yellow
            Start-Sleep -Seconds 60
            
            # Check status
            $maxAttempts = 10
            $attempt = 0
            $completed = $false
            
            while ($attempt -lt $maxAttempts -and !$completed) {
                $attempt++
                Write-Host "🔍 Checking status (attempt $attempt/$maxAttempts)..." -ForegroundColor Cyan
                
                try {
                    $status = Invoke-RestMethod -Uri "http://localhost:7777/api/status/$requestId"
                    
                    switch ($status.status) {
                        "completed" {
                            Write-Host "✅ Generation completed!" -ForegroundColor Green
                            $completed = $true
                            
                            # Download image
                            Write-Host "📥 Downloading image..." -ForegroundColor Cyan
                            Invoke-WebRequest -Uri "http://localhost:7777/api/image/$requestId" -OutFile $outputPath
                            
                            if (Test-Path $outputPath) {
                                $fileSize = (Get-Item $outputPath).Length
                                Write-Host "✅ Downloaded successfully! Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
                                $results += @{ name = $img.name; status = "success"; size = $fileSize }
                            } else {
                                Write-Host "❌ Download failed!" -ForegroundColor Red
                                $results += @{ name = $img.name; status = "download_failed" }
                            }
                        }
                        "failed" {
                            Write-Host "❌ Generation failed!" -ForegroundColor Red
                            $results += @{ name = $img.name; status = "failed" }
                            $completed = $true
                        }
                        "processing" {
                            Write-Host "🔄 Still processing..." -ForegroundColor Yellow
                            Start-Sleep -Seconds 15
                        }
                        "pending" {
                            Write-Host "⏸️ Still pending..." -ForegroundColor Yellow
                            Start-Sleep -Seconds 15
                        }
                        default {
                            Write-Host "❓ Unknown status: $($status.status)" -ForegroundColor Yellow
                            Start-Sleep -Seconds 15
                        }
                    }
                } catch {
                    Write-Host "⚠️ Error checking status: $($_.Exception.Message)" -ForegroundColor Yellow
                    Start-Sleep -Seconds 15
                }
            }
            
            if (!$completed) {
                Write-Host "⏰ Timeout waiting for completion" -ForegroundColor Red
                $results += @{ name = $img.name; status = "timeout" }
            }
            
        } else {
            Write-Host "❌ Failed to submit request" -ForegroundColor Red
            $results += @{ name = $img.name; status = "submit_failed" }
        }
        
    } catch {
        Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        $results += @{ name = $img.name; status = "error"; error = $_.Exception.Message }
    }
    
    # Small delay between requests
    if ($imageCount -lt $images.Count) {
        Write-Host "⏸️ Waiting 10 seconds before next request..." -ForegroundColor Cyan
        Start-Sleep -Seconds 10
    }
}

# Summary
Write-Host "`n📊 GENERATION SUMMARY" -ForegroundColor Green
Write-Host "=====================" -ForegroundColor Green

$successful = $results | Where-Object { $_.status -eq "success" }
$skipped = $results | Where-Object { $_.status -eq "skipped" }
$failed = $results | Where-Object { $_.status -ne "success" -and $_.status -ne "skipped" }

Write-Host "✅ Successfully Generated: $($successful.Count)" -ForegroundColor Green
Write-Host "⏭️ Skipped (Already Exist): $($skipped.Count)" -ForegroundColor Yellow
Write-Host "❌ Failed: $($failed.Count)" -ForegroundColor Red

if ($successful.Count -gt 0) {
    Write-Host "`n🎉 Successfully Generated:" -ForegroundColor Green
    foreach ($result in $successful) {
        Write-Host "  ✅ $($result.name)" -ForegroundColor Green
    }
}

if ($failed.Count -gt 0) {
    Write-Host "`n❌ Failed Images:" -ForegroundColor Red
    foreach ($result in $failed) {
        Write-Host "  ❌ $($result.name): $($result.status)" -ForegroundColor Red
    }
}

Write-Host "`n🌿 Image generation process completed!" -ForegroundColor Green
Write-Host "Check the 'images' folder for your generated images." -ForegroundColor Cyan
