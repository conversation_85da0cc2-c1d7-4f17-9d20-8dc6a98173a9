/* Spa Wellness Template Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #2d3748;
    background-color: #f7fafc;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.logo h1 {
    color: #38a169;
    font-size: 1.8rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo span {
    color: #68d391;
    font-size: 0.9rem;
    font-weight: 400;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-list a {
    text-decoration: none;
    color: #2d3748;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-list a:hover {
    color: #38a169;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #38a169, #68d391);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2f855a, #38a169);
    transform: translateY(-2px);
}

.btn-secondary {
    background: linear-gradient(135deg, #4299e1, #63b3ed);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #3182ce, #4299e1);
}

.btn-outline {
    background: transparent;
    color: #38a169;
    border: 2px solid #38a169;
}

.btn-outline:hover {
    background: #38a169;
    color: white;
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(56, 161, 105, 0.8), rgba(104, 211, 145, 0.6));
    z-index: -1;
}

.hero-content {
    position: relative;
    z-index: 1;
    color: white;
    text-align: center;
    width: 100%;
}

.hero-text h1 {
    font-family: 'Dancing Script', cursive;
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Features Section */
.features-section {
    padding: 4rem 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
    background: #f7fafc;
    border-radius: 20px;
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #38a169, #68d391);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.feature-item h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2d3748;
}

.feature-item p {
    color: #718096;
    line-height: 1.6;
}

/* Services Section */
.services-section {
    padding: 4rem 0;
    background: #f7fafc;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #718096;
    max-width: 600px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-image {
    height: 200px;
    background: linear-gradient(135deg, #e6fffa, #b2f5ea);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: #38a169;
}

.image-placeholder i {
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

.service-info {
    padding: 2rem;
}

.service-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2d3748;
}

.service-info p {
    color: #718096;
    margin-bottom: 1.5rem;
}

.service-list {
    list-style: none;
    margin-bottom: 1.5rem;
}

.service-list li {
    padding: 0.25rem 0;
    color: #4a5568;
    font-size: 0.9rem;
}

.service-list li:before {
    content: "•";
    color: #38a169;
    margin-right: 0.5rem;
}

.service-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #38a169;
}

/* Treatments Section */
.treatments-section {
    padding: 4rem 0;
    background: white;
}

.treatments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.treatment-item {
    text-align: center;
    padding: 2rem;
    background: #f7fafc;
    border-radius: 20px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.treatment-item:hover {
    border-color: #38a169;
    transform: translateY(-5px);
}

.treatment-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #38a169, #68d391);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.treatment-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.treatment-item p {
    color: #718096;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.treatment-duration {
    color: #4a5568;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.treatment-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: #38a169;
}

/* Therapists Section */
.therapists-section {
    padding: 4rem 0;
    background: #f7fafc;
}

.therapists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.therapist-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.therapist-card:hover {
    transform: translateY(-5px);
}

.therapist-image {
    height: 250px;
    background: linear-gradient(135deg, #e6fffa, #b2f5ea);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: #38a169;
}

.therapist-image i {
    font-size: 4rem;
    margin-bottom: 0.5rem;
}

.therapist-info {
    padding: 2rem;
    text-align: center;
}

.therapist-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.therapist-title {
    color: #38a169;
    font-weight: 500;
    margin-bottom: 1rem;
}

.therapist-bio {
    color: #718096;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.therapist-specialties {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.therapist-specialties span {
    background: #e6fffa;
    color: #38a169;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Packages Section */
.packages-section {
    padding: 4rem 0;
    background: white;
}

.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}

.package-card {
    background: white;
    border-radius: 20px;
    border: 2px solid #e2e8f0;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.package-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
}

.package-card.featured {
    border-color: #38a169;
    transform: scale(1.05);
}

.package-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: #38a169;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.package-header {
    background: linear-gradient(135deg, #f7fafc, #e6fffa);
    padding: 2rem;
    text-align: center;
}

.package-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2d3748;
}

.package-price {
    font-size: 2.5rem;
    font-weight: 800;
    color: #38a169;
    margin-bottom: 0.5rem;
}

.package-duration {
    color: #718096;
    font-size: 0.9rem;
}

.package-content {
    padding: 2rem;
}

.package-includes {
    list-style: none;
    margin-bottom: 2rem;
}

.package-includes li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    color: #4a5568;
}

.package-includes i {
    color: #38a169;
    font-size: 0.9rem;
}

/* Booking Section */
.booking-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #38a169, #68d391);
    color: white;
    text-align: center;
}

.booking-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.booking-content p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.booking-form {
    max-width: 600px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

/* Contact Section */
.contact-section {
    padding: 4rem 0;
    background: #f7fafc;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.contact-info h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-item i {
    color: #38a169;
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.contact-item h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.contact-item p {
    color: #718096;
    line-height: 1.6;
}

.wellness-tips h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1.5rem;
}

.tip-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 10px;
}

.tip-item i {
    color: #38a169;
    margin-top: 0.25rem;
}

.tip-item p {
    color: #4a5568;
    margin: 0;
}

/* Footer */
.footer {
    background: #2d3748;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #68d391;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-section h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-section p {
    opacity: 0.8;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #cbd5e0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #68d391;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #4a5568;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #38a169;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #4a5568;
    padding-top: 1rem;
    text-align: center;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .packages-grid {
        grid-template-columns: 1fr;
    }
    
    .package-card.featured {
        transform: none;
    }
}
