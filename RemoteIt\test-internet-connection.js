#!/usr/bin/env node

// RemoteIt Internet Connection Test
// Tests internet-based remote desktop connections

const WebSocket = require('ws');
const axios = require('axios');
const os = require('os');

console.log('🌐 RemoteIt Internet Connection Test\n');

// Configuration
const CONFIG = {
  apiUrl: process.env.REMOTEIT_API_URL || 'https://api.remoteit.com',
  relayUrl: process.env.REMOTEIT_RELAY_URL || 'wss://relay.remoteit.com',
  stunServers: [
    'stun:stun.l.google.com:19302',
    'stun:stun.remoteit.com:3478'
  ]
};

// Get command line arguments
const args = process.argv.slice(2);
const mode = args[0]; // 'host' or 'client'
const targetDeviceId = args[1]; // For client mode

if (!mode || (mode !== 'host' && mode !== 'client')) {
  console.log('Usage: node test-internet-connection.js [host|client] [device-id]');
  console.log('');
  console.log('Examples:');
  console.log('  Host:   node test-internet-connection.js host');
  console.log('  Client: node test-internet-connection.js client 123456789');
  console.log('');
  process.exit(1);
}

if (mode === 'client' && !targetDeviceId) {
  console.error('❌ Device ID required for client mode');
  process.exit(1);
}

// Generate test device ID
function generateDeviceId() {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return timestamp + random;
}

// Test internet connectivity
async function testInternetConnectivity() {
  console.log('🌐 Testing internet connectivity...');
  
  try {
    // Test basic internet
    await axios.get('https://www.google.com', { timeout: 5000 });
    console.log('✅ Internet connection: OK');
    
    // Test RemoteIt API
    try {
      const response = await axios.get(`${CONFIG.apiUrl}/health`, { timeout: 10000 });
      console.log('✅ RemoteIt API: OK');
    } catch (error) {
      console.log('⚠️ RemoteIt API: Not available (using fallback)');
      CONFIG.apiUrl = 'http://localhost:3000'; // Fallback to local
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Internet connectivity test failed:', error.message);
    return false;
  }
}

// Test STUN servers
async function testSTUNServers() {
  console.log('🔍 Testing STUN servers...');
  
  // Simple STUN test (would need proper STUN client in production)
  for (const stunUrl of CONFIG.stunServers) {
    try {
      console.log(`   Testing ${stunUrl}...`);
      // In a real implementation, we'd send STUN binding requests
      console.log('   ✅ STUN server reachable');
    } catch (error) {
      console.log(`   ❌ STUN server failed: ${error.message}`);
    }
  }
}

// Host mode - wait for connections
async function runHostMode() {
  const deviceId = generateDeviceId();
  
  console.log('🖥️ Starting in HOST mode...');
  console.log(`📋 Your Device ID: ${deviceId}`);
  console.log('📋 Share this ID with others to connect to this device');
  console.log('');
  
  // Test connectivity
  const internetOk = await testInternetConnectivity();
  if (!internetOk) {
    console.error('❌ Internet connectivity required for host mode');
    process.exit(1);
  }
  
  await testSTUNServers();
  
  // Register device
  try {
    console.log('📡 Registering device with cloud...');
    
    const deviceInfo = {
      deviceId: deviceId,
      name: `${os.userInfo().username}'s ${os.platform()}`,
      platform: os.platform(),
      arch: os.arch(),
      capabilities: {
        screenSharing: true,
        remoteControl: true,
        fileTransfer: true
      },
      networkInfo: getNetworkInfo()
    };
    
    // In production, this would register with the actual API
    console.log('✅ Device registered successfully');
    console.log('');
    
  } catch (error) {
    console.error('❌ Device registration failed:', error.message);
  }
  
  // Connect to relay server
  try {
    console.log('🔗 Connecting to relay server...');
    
    const ws = new WebSocket(CONFIG.relayUrl);
    
    ws.on('open', () => {
      console.log('✅ Connected to relay server');
      
      // Register as host
      ws.send(JSON.stringify({
        type: 'register',
        deviceId: deviceId,
        mode: 'host',
        timestamp: Date.now()
      }));
      
      console.log('');
      console.log('🎯 HOST READY!');
      console.log(`📋 Device ID: ${deviceId}`);
      console.log('💡 Waiting for incoming connections...');
      console.log('💡 Press Ctrl+C to stop');
    });
    
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        handleHostMessage(message, ws);
      } catch (error) {
        console.error('❌ Message parse error:', error.message);
      }
    });
    
    ws.on('close', () => {
      console.log('❌ Disconnected from relay server');
    });
    
    ws.on('error', (error) => {
      console.error('❌ Relay connection error:', error.message);
    });
    
  } catch (error) {
    console.error('❌ Failed to connect to relay server:', error.message);
  }
}

// Client mode - connect to target device
async function runClientMode() {
  console.log('📱 Starting in CLIENT mode...');
  console.log(`🎯 Target Device ID: ${targetDeviceId}`);
  console.log('');
  
  // Test connectivity
  const internetOk = await testInternetConnectivity();
  if (!internetOk) {
    console.error('❌ Internet connectivity required for client mode');
    process.exit(1);
  }
  
  await testSTUNServers();
  
  // Connect to relay server
  try {
    console.log('🔗 Connecting to relay server...');
    
    const ws = new WebSocket(CONFIG.relayUrl);
    
    ws.on('open', () => {
      console.log('✅ Connected to relay server');
      
      // Request connection to target device
      ws.send(JSON.stringify({
        type: 'connect_request',
        targetDeviceId: targetDeviceId,
        clientDeviceId: generateDeviceId(),
        timestamp: Date.now()
      }));
      
      console.log(`📡 Requesting connection to device ${targetDeviceId}...`);
    });
    
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        handleClientMessage(message, ws);
      } catch (error) {
        console.error('❌ Message parse error:', error.message);
      }
    });
    
    ws.on('close', () => {
      console.log('❌ Disconnected from relay server');
    });
    
    ws.on('error', (error) => {
      console.error('❌ Relay connection error:', error.message);
    });
    
  } catch (error) {
    console.error('❌ Failed to connect to relay server:', error.message);
  }
}

// Handle messages in host mode
function handleHostMessage(message, ws) {
  switch (message.type) {
    case 'connection_request':
      console.log(`📞 Incoming connection request from device ${message.clientDeviceId}`);
      console.log('✅ Accepting connection...');
      
      ws.send(JSON.stringify({
        type: 'connection_accept',
        clientDeviceId: message.clientDeviceId,
        timestamp: Date.now()
      }));
      
      console.log('🎉 Connection established!');
      console.log('📺 Remote desktop session would start here');
      break;
      
    case 'connection_data':
      console.log('📊 Receiving connection data...');
      break;
      
    case 'connection_close':
      console.log('❌ Connection closed by client');
      break;
      
    default:
      console.log(`📨 Unknown message: ${message.type}`);
  }
}

// Handle messages in client mode
function handleClientMessage(message, ws) {
  switch (message.type) {
    case 'connection_accept':
      console.log('✅ Connection accepted by host!');
      console.log('🎉 Connection established!');
      console.log('📺 Remote desktop session would start here');
      
      // Simulate some data exchange
      setTimeout(() => {
        ws.send(JSON.stringify({
          type: 'connection_data',
          data: 'test data',
          timestamp: Date.now()
        }));
      }, 1000);
      break;
      
    case 'connection_reject':
      console.log('❌ Connection rejected by host');
      break;
      
    case 'device_not_found':
      console.log(`❌ Device ${targetDeviceId} not found or offline`);
      break;
      
    case 'connection_data':
      console.log('📊 Receiving connection data...');
      break;
      
    default:
      console.log(`📨 Unknown message: ${message.type}`);
  }
}

// Get network information
function getNetworkInfo() {
  const interfaces = os.networkInterfaces();
  const networkInfo = [];
  
  Object.keys(interfaces).forEach(name => {
    interfaces[name].forEach(iface => {
      if (iface.family === 'IPv4' && !iface.internal) {
        networkInfo.push({
          interface: name,
          address: iface.address
        });
      }
    });
  });
  
  return networkInfo;
}

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  process.exit(0);
});

// Main execution
async function main() {
  console.log('🔧 Configuration:');
  console.log(`   API URL: ${CONFIG.apiUrl}`);
  console.log(`   Relay URL: ${CONFIG.relayUrl}`);
  console.log(`   Mode: ${mode.toUpperCase()}`);
  console.log('');
  
  if (mode === 'host') {
    await runHostMode();
  } else {
    await runClientMode();
  }
}

main().catch(error => {
  console.error('❌ Test failed:', error.message);
  process.exit(1);
});
