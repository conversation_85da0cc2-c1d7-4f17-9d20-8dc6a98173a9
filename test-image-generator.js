const axios = require('axios');
const fs = require('fs');

// Calculate optimal timeout and wait time based on image resolution
function calculateImageTiming(width, height) {
  const totalPixels = width * height;
  const megapixels = totalPixels / 1000000;

  // Base timing for different resolution categories
  let timeout = 60; // Base timeout in seconds
  let waitTime = 70; // Base wait time between requests

  if (megapixels <= 0.5) {
    // Small images (≤0.5MP): Profile pictures, icons
    timeout = 60;
    waitTime = 70;
  } else if (megapixels <= 1.0) {
    // Medium images (0.5-1MP): Standard content
    timeout = 120;
    waitTime = 70;
  } else if (megapixels <= 2.0) {
    // Large images (1-2MP): Hero sections, banners
    timeout = 200;
    waitTime = 70;
  } else {
    // Extra large images (>2MP): High-res hero sections, detailed artwork
    timeout = 300;
    waitTime = 300; // Longer wait for very large images
  }

  return { timeout, waitTime, megapixels: megapixels.toFixed(2) };
}

async function testImageGeneration() {
  console.log('🧪 Advanced Image Generator Test with Intelligent Timing');
  console.log('📋 Generating 4 test images with resolution-based timing...\n');

  const testImages = [
    {
      prompt: 'Professional headshot of a confident business executive with clean background, corporate style, high quality portrait',
      width: 512,
      height: 512,
      description: 'Profile Picture (Square)',
      category: 'Profile'
    },
    {
      prompt: 'Modern tech startup office with glass walls, collaborative workspace, natural lighting, wide angle view',
      width: 1920,
      height: 1080,
      description: 'Hero Section (Wide)',
      category: 'Hero'
    },
    {
      prompt: 'Elegant minimalist product showcase with soft shadows and premium lighting, commercial photography style',
      width: 800,
      height: 600,
      description: 'Product Image (Standard)',
      category: 'Product'
    },
    {
      prompt: 'Ultra-detailed futuristic cityscape at sunset with flying cars and neon lights, cyberpunk aesthetic, 4K quality',
      width: 2560,
      height: 1440,
      description: 'Large Hero Banner (Ultra-wide)',
      category: 'Ultra-Hero'
    }
  ];
  
  const requests = [];
  
  // Submit requests with intelligent timing based on resolution
  for (let i = 0; i < testImages.length; i++) {
    const image = testImages[i];
    const timing = calculateImageTiming(image.width, image.height);

    console.log(`📸 IMAGE ${i + 1}: ${image.description}`);
    console.log(`   Category: ${image.category}`);
    console.log(`   Prompt: ${image.prompt}`);
    console.log(`   Dimensions: ${image.width}x${image.height} (${timing.megapixels}MP)`);
    console.log(`   Timeout: ${timing.timeout}s | Wait Time: ${timing.waitTime}s`);

    try {
      const response = await axios.post('http://localhost:7777/api/generate', {
        type: 'image',
        prompt: image.prompt,
        width: image.width,
        height: image.height,
        timeout: timing.timeout
      });

      if (response.data.success) {
        console.log(`   ✅ Request ID: ${response.data.requestId}`);
        console.log(`   📊 Estimated Time: ${response.data.estimatedTime}`);
        console.log(`   🤖 Model: ${response.data.model}`);
        requests.push({
          ...image,
          requestId: response.data.requestId,
          index: i + 1,
          submittedAt: new Date().toISOString(),
          timing: timing
        });
      } else {
        console.log(`   ❌ Failed to submit request`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }

    // Intelligent rate limiting based on image size
    if (i < testImages.length - 1) {
      const waitTime = timing.waitTime;
      console.log(`   ⏰ Waiting ${waitTime} seconds (resolution-based timing)...`);

      for (let countdown = waitTime; countdown > 0; countdown--) {
        const minutes = Math.floor(countdown / 60);
        const seconds = countdown % 60;
        const timeStr = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
        process.stdout.write(`\r   ⏳ ${timeStr} remaining...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      console.log('\r   ✅ Wait complete, submitting next request...\n');
    } else {
      console.log('');
    }
  }
  
  if (requests.length === 0) {
    console.log('❌ No requests were submitted successfully');
    return;
  }
  
  console.log(`🔄 Monitoring ${requests.length} requests for completion...\n`);
  console.log(`📊 Intelligent Timing Applied: Resolution-based timeouts and wait times`);

  // Calculate total estimated time
  const totalWaitTime = requests.reduce((sum, req, index) => {
    return sum + (index < requests.length - 1 ? req.timing.waitTime : 0);
  }, 0);
  const avgGenerationTime = requests.reduce((sum, req) => sum + req.timing.timeout, 0) / requests.length;

  console.log(`⏱️ Estimated Duration: ${Math.ceil(totalWaitTime / 60)}m submissions + ${Math.ceil(avgGenerationTime / 60)}m avg generation\n`);

  // Monitor completion with extended timeout for large images
  const completed = [];
  const maxWaitTime = 1200000; // 20 minutes (extended for large images)
  const startTime = Date.now();
  
  while (completed.length < requests.length && (Date.now() - startTime) < maxWaitTime) {
    for (const request of requests) {
      if (completed.find(c => c.requestId === request.requestId)) continue;
      
      try {
        const statusResponse = await axios.get(`http://localhost:7777/api/status/${request.requestId}`);
        const status = statusResponse.data;
        
        if (status.status === 'completed') {
          console.log(`✅ IMAGE ${request.index} COMPLETED: ${request.description}`);
          console.log(`   Request ID: ${request.requestId}`);
          console.log(`   Status: ${status.status}`);
          console.log(`   Image URL: http://localhost:7777${status.imageUrl}`);
          console.log(`   Seed Used: ${status.seedUsed}`);
          console.log(`   File Path: ${status.filePath}`);
          
          // Check file size if available
          if (status.filePath) {
            try {
              const stats = fs.statSync(status.filePath);
              const fileSizeKB = (stats.size / 1024).toFixed(2);
              console.log(`   File Size: ${fileSizeKB} KB`);
            } catch (e) {
              console.log(`   File Size: Unable to determine`);
            }
          }
          
          completed.push({
            ...request,
            ...status,
            completedAt: new Date().toISOString()
          });
          console.log('');
          
        } else if (status.status === 'failed') {
          console.log(`❌ IMAGE ${request.index} FAILED: ${request.description}`);
          console.log(`   Error: ${status.error}`);
          completed.push({
            ...request,
            ...status,
            failed: true
          });
          console.log('');
          
        } else {
          // Still processing
          console.log(`⏳ IMAGE ${request.index}: ${status.status} - ${request.description}`);
        }
        
      } catch (error) {
        console.log(`❌ Error checking status for ${request.description}: ${error.message}`);
      }
    }
    
    if (completed.length < requests.length) {
      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
    }
  }
  
  // Final results
  console.log('🎯 FINAL RESULTS:');
  console.log('================');
  
  const successful = completed.filter(c => !c.failed);
  const failed = completed.filter(c => c.failed);
  
  console.log(`✅ Successfully generated: ${successful.length}/${requests.length} images`);
  console.log(`❌ Failed: ${failed.length}/${requests.length} images`);
  
  if (successful.length > 0) {
    console.log('\n📊 PERFORMANCE METRICS:');
    const totalTime = successful.reduce((sum, img) => {
      const created = new Date(img.createdAt);
      const completed = new Date(img.completedAt);
      return sum + (completed - created);
    }, 0);
    const avgTime = (totalTime / successful.length / 1000).toFixed(1);
    console.log(`   Average Generation Time: ${avgTime} seconds`);

    console.log('\n📏 RESOLUTION ANALYSIS:');
    successful.forEach(img => {
      const timing = calculateImageTiming(img.width, img.height);
      const created = new Date(img.createdAt);
      const completed = new Date(img.completedAt);
      const actualTime = ((completed - created) / 1000).toFixed(1);
      console.log(`   ${img.description}:`);
      console.log(`     Resolution: ${img.width}x${img.height} (${timing.megapixels}MP)`);
      console.log(`     Category: ${img.category}`);
      console.log(`     Expected: ${timing.timeout}s | Actual: ${actualTime}s`);
      console.log(`     Efficiency: ${((timing.timeout / actualTime) * 100).toFixed(1)}%`);
    });

    console.log('\n🌱 SEEDS CAPTURED:');
    successful.forEach(img => {
      console.log(`   ${img.description}: Seed ${img.seedUsed || 'Not captured'}`);
    });

    console.log('\n🔗 IMAGE URLS:');
    successful.forEach(img => {
      console.log(`   ${img.description}: http://localhost:7777${img.imageUrl}`);
    });

    console.log('\n📁 FILE PATHS:');
    successful.forEach(img => {
      console.log(`   ${img.description}: ${img.filePath}`);
    });
  }
  
  if (successful.length === requests.length) {
    console.log('\n🎉 ALL TESTS PASSED! Image generator working perfectly with updated documentation.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the errors above.');
  }
}

testImageGeneration().catch(error => {
  console.error('❌ Test failed:', error.message);
});
