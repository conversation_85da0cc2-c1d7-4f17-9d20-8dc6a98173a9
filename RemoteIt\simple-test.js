// Simple RemoteIt Connection Test
// This tests basic connectivity between two laptops

const WebSocket = require('ws');
const express = require('express');
const http = require('http');

console.log('🧪 RemoteIt Simple Connection Test\n');

// Get command line arguments
const args = process.argv.slice(2);
const mode = args[0] || 'server'; // 'server' or 'client'
const serverIP = args[1] || 'localhost';

if (mode === 'server') {
  console.log('🖥️  Starting as SERVER...');
  startServer();
} else if (mode === 'client') {
  console.log('📱 Starting as CLIENT...');
  startClient(serverIP);
} else {
  console.log('Usage: node simple-test.js [server|client] [server-ip]');
  console.log('Examples:');
  console.log('  Server: node simple-test.js server');
  console.log('  Client: node simple-test.js client *************');
  process.exit(1);
}

function startServer() {
  // Create HTTP server
  const app = express();
  const server = http.createServer(app);
  
  // Create WebSocket server
  const wss = new WebSocket.Server({ port: 8081 });
  
  // HTTP endpoints
  app.get('/health', (req, res) => {
    res.json({ status: 'healthy', timestamp: new Date().toISOString() });
  });
  
  app.get('/test', (req, res) => {
    res.json({ 
      message: 'RemoteIt server is running!',
      server: 'simple-test',
      timestamp: new Date().toISOString()
    });
  });
  
  // WebSocket handling
  wss.on('connection', (ws, req) => {
    const clientIP = req.socket.remoteAddress;
    console.log(`✅ Client connected from: ${clientIP}`);
    
    // Send welcome message
    ws.send(JSON.stringify({
      type: 'welcome',
      message: 'Connected to RemoteIt test server',
      timestamp: new Date().toISOString()
    }));
    
    // Handle messages
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        console.log(`📨 Received: ${message.type} - ${message.data}`);
        
        // Echo back
        ws.send(JSON.stringify({
          type: 'echo',
          original: message,
          timestamp: new Date().toISOString()
        }));
        
      } catch (error) {
        console.error('❌ Message parse error:', error.message);
      }
    });
    
    ws.on('close', () => {
      console.log(`❌ Client disconnected: ${clientIP}`);
    });
    
    ws.on('error', (error) => {
      console.error('❌ WebSocket error:', error.message);
    });
  });
  
  // Start HTTP server
  server.listen(3000, () => {
    console.log('✅ HTTP Server running on port 3000');
    console.log('✅ WebSocket Server running on port 8081');
    console.log('\n📋 Test URLs:');
    console.log('   Health: http://localhost:3000/health');
    console.log('   Test: http://localhost:3000/test');
    console.log('   WebSocket: ws://localhost:8081');
    console.log('\n🌐 Network Info:');
    
    // Show network interfaces
    const os = require('os');
    const interfaces = os.networkInterfaces();
    Object.keys(interfaces).forEach(name => {
      interfaces[name].forEach(iface => {
        if (iface.family === 'IPv4' && !iface.internal) {
          console.log(`   ${name}: ${iface.address}`);
          console.log(`   Client command: node simple-test.js client ${iface.address}`);
        }
      });
    });
    
    console.log('\n💡 Waiting for client connections...');
  });
}

function startClient(serverIP) {
  console.log(`🔗 Connecting to server at: ${serverIP}`);
  
  // Test HTTP connection first
  const http = require('http');
  
  console.log('🧪 Testing HTTP connection...');
  
  const req = http.get(`http://${serverIP}:3000/health`, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        console.log('✅ HTTP connection successful:', response.status);
        
        // Now test WebSocket
        testWebSocket(serverIP);
        
      } catch (error) {
        console.error('❌ HTTP response parse error:', error.message);
      }
    });
  });
  
  req.on('error', (error) => {
    console.error('❌ HTTP connection failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Check if server is running');
    console.log('   2. Check firewall settings');
    console.log('   3. Verify IP address is correct');
    console.log(`   4. Try: curl http://${serverIP}:3000/health`);
  });
  
  req.setTimeout(5000, () => {
    console.error('❌ HTTP connection timeout');
    req.destroy();
  });
}

function testWebSocket(serverIP) {
  console.log('🧪 Testing WebSocket connection...');
  
  const ws = new WebSocket(`ws://${serverIP}:8081`);
  
  ws.on('open', () => {
    console.log('✅ WebSocket connected successfully!');
    
    // Send test message
    ws.send(JSON.stringify({
      type: 'test',
      data: 'Hello from client!',
      timestamp: new Date().toISOString()
    }));
    
    // Send periodic ping
    const pingInterval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'ping',
          data: 'ping',
          timestamp: new Date().toISOString()
        }));
      }
    }, 5000);
    
    // Cleanup on close
    ws.on('close', () => {
      clearInterval(pingInterval);
    });
  });
  
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      console.log(`📨 Server response: ${message.type}`);
      
      if (message.type === 'welcome') {
        console.log(`   ${message.message}`);
      } else if (message.type === 'echo') {
        console.log(`   Echo: ${message.original.data}`);
      }
      
    } catch (error) {
      console.error('❌ Message parse error:', error.message);
    }
  });
  
  ws.on('close', (code, reason) => {
    console.log(`❌ WebSocket disconnected: ${code} - ${reason.toString()}`);
  });
  
  ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Check if server WebSocket is running on port 8081');
    console.log('   2. Check firewall allows port 8081');
    console.log('   3. Try telnet to test port: telnet SERVER_IP 8081');
  });
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down...');
  process.exit(0);
});
