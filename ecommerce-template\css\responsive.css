/* Responsive Design */

/* Tablet Styles */
@media (max-width: 1024px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-content {
        gap: 2rem;
    }
    
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1.5rem;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .offers-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
    
    .search-box input {
        width: 200px;
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    .header-content {
        padding: 0.75rem 0;
    }
    
    .nav {
        display: none;
    }
    
    .search-box {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .hero {
        padding: 2rem 0;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-text h1 {
        font-size: 2rem;
    }
    
    .hero-text p {
        font-size: 1rem;
    }
    
    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .category-card {
        padding: 1.5rem 1rem;
    }
    
    .category-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .filter-tabs {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .product-image {
        height: 200px;
    }
    
    .product-info {
        padding: 1rem;
    }
    
    .offers-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .offer-card {
        padding: 2rem 1rem;
    }
    
    .offer-content h3 {
        font-size: 1.5rem;
    }
    
    .newsletter-form {
        flex-direction: column;
        max-width: 300px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .cart-sidebar {
        width: 100%;
        right: -100%;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .header-content {
        padding: 0.5rem 0;
    }
    
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .user-actions {
        gap: 0.5rem;
    }
    
    .action-btn {
        font-size: 1rem;
    }
    
    .hero {
        padding: 1.5rem 0;
    }
    
    .hero-text h1 {
        font-size: 1.8rem;
    }
    
    .hero-text p {
        font-size: 0.9rem;
    }
    
    .btn {
        padding: 0.6rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .categories {
        padding: 2rem 0;
    }
    
    .featured-products {
        padding: 2rem 0;
    }
    
    .special-offers {
        padding: 2rem 0;
    }
    
    .newsletter {
        padding: 2rem 0;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .category-card {
        padding: 1.5rem;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .product-card {
        max-width: 300px;
        margin: 0 auto;
    }
    
    .filter-tabs {
        gap: 0.5rem;
    }
    
    .filter-btn {
        padding: 0.4rem 1rem;
        font-size: 0.9rem;
    }
    
    .offer-card {
        padding: 1.5rem;
    }
    
    .offer-content h3 {
        font-size: 1.3rem;
    }
    
    .offer-content p {
        font-size: 1rem;
    }
    
    .newsletter-content h2 {
        font-size: 1.5rem;
    }
    
    .newsletter-content p {
        font-size: 1rem;
    }
    
    .newsletter-form input {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
    
    .footer {
        padding: 2rem 0 1rem;
    }
    
    .footer-section h3 {
        font-size: 1.3rem;
    }
    
    .footer-section h4 {
        font-size: 1rem;
    }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Utility Classes */
.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

.bounce {
    animation: bounce 1s;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Focus States */
.btn:focus,
.filter-btn:focus,
input:focus,
button:focus {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1f2937;
        --bg-secondary: #111827;
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --border-color: #374151;
    }
}

/* Print Styles */
@media print {
    .header,
    .mobile-menu,
    .cart-sidebar,
    .newsletter,
    .footer {
        display: none;
    }
    
    .hero {
        margin-top: 0;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .btn-primary {
        background: #000;
        color: #fff;
        border: 2px solid #000;
    }
    
    .btn-secondary {
        background: #fff;
        color: #000;
        border: 2px solid #000;
    }
    
    .product-card {
        border: 2px solid #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
