const { EventEmitter } = require('events');
const WebSocket = require('ws');
const SimplePeer = require('simple-peer');
const axios = require('axios');
const crypto = require('crypto');
const Logger = require('../utils/Logger');

class ConnectionManager extends EventEmitter {
  constructor() {
    super();
    this.logger = new Logger('connection');
    this.sessions = new Map();
    this.devices = new Map();
    this.relaySocket = null;
    this.isHost = false;
    this.isConnected = false;
    
    // Configuration for internet connectivity
    this.relayUrl = process.env.REMOTEIT_RELAY_URL || 'wss://relay.remoteit.com';
    this.apiUrl = process.env.REMOTEIT_API_URL || 'https://api.remoteit.com';

    // Enhanced WebRTC configuration for internet connections
    this.webrtcConfig = {
      iceServers: [
        // Public STUN servers for NAT discovery
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        { urls: 'stun:stun2.l.google.com:19302' },
        { urls: 'stun:stun3.l.google.com:19302' },
        { urls: 'stun:stun4.l.google.com:19302' },

        // RemoteIt STUN servers
        { urls: 'stun:stun.remoteit.com:3478' },
        { urls: 'stun:stun2.remoteit.com:3478' },
        { urls: 'stun:stun-eu.remoteit.com:3478' },
        { urls: 'stun:stun-asia.remoteit.com:3478' },

        // TURN servers for relay when direct connection fails
        {
          urls: 'turn:turn.remoteit.com:3478',
          username: 'remoteit-user',
          credential: 'secure-turn-password'
        },
        {
          urls: 'turn:turn-eu.remoteit.com:3478',
          username: 'remoteit-user',
          credential: 'secure-turn-password'
        }
      ],
      iceCandidatePoolSize: 10,
      bundlePolicy: 'max-bundle',
      rtcpMuxPolicy: 'require'
    };

    // Device identification for internet connections
    this.deviceId = this.generateDeviceId();
    this.deviceInfo = this.getDeviceInfo();
    
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;

    // NAT traversal state
    this.natType = 'unknown';
    this.publicEndpoint = null;
    this.connectionAttempts = new Map();
  }

  // Generate unique 9-digit device ID (like TeamViewer)
  generateDeviceId() {
    const stored = localStorage?.getItem('remoteit-device-id');
    if (stored) return stored;

    // Generate based on timestamp + random + machine info
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const deviceId = timestamp + random;

    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('remoteit-device-id', deviceId);
    }

    return deviceId;
  }

  // Get device information for registration
  getDeviceInfo() {
    const os = require('os');
    return {
      id: this.deviceId,
      name: os.hostname() || 'Unknown Device',
      platform: os.platform(),
      arch: os.arch(),
      version: process.version,
      userAgent: process.platform,
      capabilities: {
        screenSharing: true,
        remoteControl: true,
        fileTransfer: true,
        audioRedirection: false,
        clipboardSync: true
      },
      networkInfo: this.getNetworkInfo()
    };
  }

  // Get network interface information
  getNetworkInfo() {
    const os = require('os');
    const interfaces = os.networkInterfaces();
    const networkInfo = [];

    Object.keys(interfaces).forEach(name => {
      interfaces[name].forEach(iface => {
        if (iface.family === 'IPv4' && !iface.internal) {
          networkInfo.push({
            interface: name,
            address: iface.address,
            netmask: iface.netmask,
            mac: iface.mac
          });
        }
      });
    });

    return networkInfo;
  }

  async initialize() {
    this.logger.info('Initializing connection manager...');
    
    try {
      await this.connectToRelay();
      await this.registerDevice();
      this.setupEventHandlers();
      
      this.logger.info('Connection manager initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize connection manager:', error.message);
      throw error;
    }
  }

  async connectToRelay() {
    return new Promise((resolve, reject) => {
      this.logger.info('Connecting to relay server:', this.relayUrl);
      
      this.relaySocket = new WebSocket(this.relayUrl, {
        headers: {
          'User-Agent': 'RemoteIt Desktop Client'
        }
      });

      this.relaySocket.on('open', () => {
        this.logger.info('Connected to relay server');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.emit('relayConnected');
        resolve();
      });

      this.relaySocket.on('message', (data) => {
        this.handleRelayMessage(data);
      });

      this.relaySocket.on('close', (code, reason) => {
        this.logger.warn('Relay connection closed:', code, reason.toString());
        this.isConnected = false;
        this.emit('relayDisconnected');
        this.handleReconnection();
      });

      this.relaySocket.on('error', (error) => {
        this.logger.error('Relay connection error:', error.message);
        this.emit('relayError', error);
        reject(error);
      });

      // Connection timeout
      setTimeout(() => {
        if (this.relaySocket.readyState !== WebSocket.OPEN) {
          reject(new Error('Relay connection timeout'));
        }
      }, 10000);
    });
  }

  async registerDevice() {
    const deviceInfo = this.getDeviceInfo();
    
    const message = {
      type: 'register',
      deviceId: deviceInfo.id,
      deviceInfo: deviceInfo,
      timestamp: Date.now()
    };

    this.sendRelayMessage(message);
    this.logger.info('Device registration sent');
  }

  async getDevices() {
    try {
      const authManager = require('../auth/AuthManager');
      const token = authManager.getAccessToken();
      
      const response = await axios.get(`${this.apiUrl}/devices`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const devices = response.data.devices;
      
      // Update local device cache
      devices.forEach(device => {
        this.devices.set(device.id, device);
      });

      this.emit('deviceListUpdated', devices);
      return devices;
      
    } catch (error) {
      this.logger.error('Failed to get devices:', error.message);
      throw error;
    }
  }

  async connect(deviceId) {
    try {
      this.logger.info('Initiating connection to device:', deviceId);
      
      const sessionId = crypto.randomUUID();
      const targetDevice = this.devices.get(deviceId);
      
      if (!targetDevice) {
        throw new Error('Device not found');
      }

      if (!targetDevice.isOnline) {
        throw new Error('Device is offline');
      }

      // Create WebRTC peer connection
      const peer = new SimplePeer({
        initiator: true,
        trickle: false,
        config: {
          iceServers: this.stunServers
        }
      });

      const session = {
        id: sessionId,
        deviceId: deviceId,
        peer: peer,
        state: 'connecting',
        startTime: Date.now(),
        isHost: false
      };

      this.sessions.set(sessionId, session);

      // Setup peer event handlers
      this.setupPeerHandlers(peer, sessionId);

      // Request connection through relay
      const connectionRequest = {
        type: 'connect',
        sessionId: sessionId,
        targetDeviceId: deviceId,
        timestamp: Date.now()
      };

      this.sendRelayMessage(connectionRequest);

      this.logger.info('Connection request sent for session:', sessionId);
      this.emit('connectionStateChanged', { sessionId, state: 'connecting' });

      return sessionId;
      
    } catch (error) {
      this.logger.error('Failed to initiate connection:', error.message);
      throw error;
    }
  }

  async disconnect(sessionId) {
    try {
      const session = this.sessions.get(sessionId);
      
      if (!session) {
        this.logger.warn('Session not found for disconnect:', sessionId);
        return;
      }

      this.logger.info('Disconnecting session:', sessionId);

      // Close peer connection
      if (session.peer) {
        session.peer.destroy();
      }

      // Notify relay
      const disconnectMessage = {
        type: 'disconnect',
        sessionId: sessionId,
        timestamp: Date.now()
      };

      this.sendRelayMessage(disconnectMessage);

      // Clean up session
      this.sessions.delete(sessionId);
      
      this.emit('connectionStateChanged', { sessionId, state: 'disconnected' });
      this.logger.info('Session disconnected:', sessionId);
      
    } catch (error) {
      this.logger.error('Failed to disconnect session:', error.message);
      throw error;
    }
  }

  async acceptConnection(sessionId, offer) {
    try {
      this.logger.info('Accepting connection for session:', sessionId);

      // Create WebRTC peer connection as answerer
      const peer = new SimplePeer({
        initiator: false,
        trickle: false,
        config: {
          iceServers: this.stunServers
        }
      });

      const session = {
        id: sessionId,
        peer: peer,
        state: 'connecting',
        startTime: Date.now(),
        isHost: true
      };

      this.sessions.set(sessionId, session);

      // Setup peer event handlers
      this.setupPeerHandlers(peer, sessionId);

      // Signal the offer
      peer.signal(offer);

      this.emit('connectionStateChanged', { sessionId, state: 'connecting' });
      
    } catch (error) {
      this.logger.error('Failed to accept connection:', error.message);
      throw error;
    }
  }

  setupPeerHandlers(peer, sessionId) {
    peer.on('signal', (data) => {
      this.logger.debug('Peer signal for session:', sessionId);
      
      const signalMessage = {
        type: 'signal',
        sessionId: sessionId,
        signal: data,
        timestamp: Date.now()
      };

      this.sendRelayMessage(signalMessage);
    });

    peer.on('connect', () => {
      this.logger.info('Peer connected for session:', sessionId);
      
      const session = this.sessions.get(sessionId);
      if (session) {
        session.state = 'connected';
        session.connectedTime = Date.now();
      }

      this.emit('connectionStateChanged', { sessionId, state: 'connected' });
    });

    peer.on('data', (data) => {
      this.handlePeerData(sessionId, data);
    });

    peer.on('error', (error) => {
      this.logger.error('Peer error for session:', sessionId, error.message);
      this.emit('connectionError', { sessionId, error });
    });

    peer.on('close', () => {
      this.logger.info('Peer connection closed for session:', sessionId);
      this.sessions.delete(sessionId);
      this.emit('connectionStateChanged', { sessionId, state: 'disconnected' });
    });
  }

  handleRelayMessage(data) {
    try {
      const message = JSON.parse(data.toString());
      
      switch (message.type) {
        case 'registered':
          this.logger.info('Device registered successfully');
          this.emit('deviceRegistered');
          break;

        case 'connectionRequest':
          this.handleConnectionRequest(message);
          break;

        case 'signal':
          this.handleSignal(message);
          break;

        case 'connectionAccepted':
          this.handleConnectionAccepted(message);
          break;

        case 'connectionRejected':
          this.handleConnectionRejected(message);
          break;

        case 'deviceStatusUpdate':
          this.handleDeviceStatusUpdate(message);
          break;

        case 'error':
          this.logger.error('Relay error:', message.error);
          this.emit('relayError', new Error(message.error));
          break;

        default:
          this.logger.warn('Unknown relay message type:', message.type);
      }
      
    } catch (error) {
      this.logger.error('Failed to parse relay message:', error.message);
    }
  }

  handleConnectionRequest(message) {
    this.logger.info('Received connection request:', message.sessionId);
    this.emit('connectionRequest', {
      sessionId: message.sessionId,
      fromDevice: message.fromDevice,
      offer: message.offer
    });
  }

  handleSignal(message) {
    const session = this.sessions.get(message.sessionId);
    
    if (session && session.peer) {
      session.peer.signal(message.signal);
    } else {
      this.logger.warn('Received signal for unknown session:', message.sessionId);
    }
  }

  handleConnectionAccepted(message) {
    this.logger.info('Connection accepted for session:', message.sessionId);
    // The actual connection will be established through WebRTC signaling
  }

  handleConnectionRejected(message) {
    this.logger.info('Connection rejected for session:', message.sessionId);
    
    const session = this.sessions.get(message.sessionId);
    if (session) {
      this.sessions.delete(message.sessionId);
      this.emit('connectionStateChanged', { 
        sessionId: message.sessionId, 
        state: 'rejected',
        reason: message.reason 
      });
    }
  }

  handleDeviceStatusUpdate(message) {
    const device = this.devices.get(message.deviceId);
    if (device) {
      device.isOnline = message.isOnline;
      device.lastSeen = message.lastSeen;
      this.emit('deviceStatusChanged', device);
    }
  }

  handlePeerData(sessionId, data) {
    try {
      const message = JSON.parse(data.toString());
      this.emit('peerMessage', { sessionId, message });
    } catch (error) {
      // Handle binary data (screen capture, file transfer, etc.)
      this.emit('peerData', { sessionId, data });
    }
  }

  sendRelayMessage(message) {
    if (this.relaySocket && this.relaySocket.readyState === WebSocket.OPEN) {
      this.relaySocket.send(JSON.stringify(message));
    } else {
      this.logger.warn('Cannot send relay message - not connected');
    }
  }

  sendPeerMessage(sessionId, message) {
    const session = this.sessions.get(sessionId);
    
    if (session && session.peer && session.peer.connected) {
      session.peer.send(JSON.stringify(message));
    } else {
      this.logger.warn('Cannot send peer message - session not connected:', sessionId);
    }
  }

  sendPeerData(sessionId, data) {
    const session = this.sessions.get(sessionId);
    
    if (session && session.peer && session.peer.connected) {
      session.peer.send(data);
    } else {
      this.logger.warn('Cannot send peer data - session not connected:', sessionId);
    }
  }

  handleReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.logger.error('Max reconnection attempts reached');
      this.emit('reconnectionFailed');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    this.logger.info(`Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    setTimeout(async () => {
      try {
        await this.connectToRelay();
        await this.registerDevice();
      } catch (error) {
        this.logger.error('Reconnection failed:', error.message);
        this.handleReconnection();
      }
    }, delay);
  }

  getDeviceInfo() {
    const os = require('os');
    const { app } = require('electron');
    const Store = require('electron-store');
    const store = new Store();
    
    return {
      id: store.get('deviceId'),
      name: os.hostname(),
      platform: os.platform(),
      arch: os.arch(),
      version: app.getVersion(),
      userAgent: `RemoteIt Desktop/${app.getVersion()}`,
      capabilities: {
        screenSharing: true,
        fileTransfer: true,
        remoteControl: true,
        multiMonitor: true
      }
    };
  }

  getSession(sessionId) {
    return this.sessions.get(sessionId);
  }

  getAllSessions() {
    return Array.from(this.sessions.values());
  }

  getHostInfo(deviceId) {
    return this.devices.get(deviceId);
  }

  cleanup() {
    this.logger.info('Cleaning up connection manager...');
    
    // Close all sessions
    for (const [sessionId, session] of this.sessions) {
      if (session.peer) {
        session.peer.destroy();
      }
    }
    this.sessions.clear();

    // Close relay connection
    if (this.relaySocket) {
      this.relaySocket.close();
      this.relaySocket = null;
    }

    this.isConnected = false;
    this.logger.info('Connection manager cleanup complete');
  }
}

module.exports = ConnectionManager;
