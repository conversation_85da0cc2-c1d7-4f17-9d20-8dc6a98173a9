# RemoteIt Internet Deployment Guide

## 🌐 Overview

This guide explains how to deploy RemoteIt for internet-based remote desktop connections, similar to TeamViewer. Users will be able to connect to any device anywhere in the world with just a Device ID.

## 🏗️ Architecture Summary

```
Internet Users ←→ Cloud Infrastructure ←→ Internet Users
     ↓                      ↓                    ↓
[Client App]         [Relay Servers]        [Host App]
     ↓                      ↓                    ↓
Device ID: 123456789  STUN/TURN Servers   Device ID: 987654321
```

## 📋 Requirements

### Cloud Infrastructure
- **Domain**: `remoteit.com` (or your domain)
- **SSL Certificates**: Wildcard SSL for subdomains
- **Cloud Provider**: AWS, Azure, or GCP
- **Estimated Cost**: $500-2000/month (depending on usage)

### Services Needed
1. **Authentication Server** (`api.remoteit.com`)
2. **Relay Servers** (`relay.remoteit.com`, `relay-eu.remoteit.com`)
3. **STUN Servers** (`stun.remoteit.com:3478`)
4. **TURN Servers** (`turn.remoteit.com:3478`)
5. **Database** (PostgreSQL + Redis)
6. **CDN** (for app downloads)

## 🚀 Step-by-Step Deployment

### Phase 1: Cloud Infrastructure Setup

#### 1. Domain Configuration
```bash
# DNS Records needed:
api.remoteit.com        A    YOUR_SERVER_IP
relay.remoteit.com      A    YOUR_RELAY_IP
relay-eu.remoteit.com   A    YOUR_EU_RELAY_IP
stun.remoteit.com       A    YOUR_STUN_IP
turn.remoteit.com       A    YOUR_TURN_IP
download.remoteit.com   A    YOUR_CDN_IP
```

#### 2. Deploy with Terraform (AWS Example)
```bash
cd cloud/terraform
terraform init
terraform plan -var="domain_name=remoteit.com"
terraform apply
```

#### 3. Manual Cloud Setup (Alternative)

**AWS EC2 Instances:**
```bash
# Authentication Server (t3.medium)
aws ec2 run-instances \
  --image-id ami-0c02fb55956c7d316 \
  --instance-type t3.medium \
  --key-name your-key \
  --security-group-ids sg-auth \
  --user-data file://scripts/setup-auth.sh

# Relay Servers (t3.large) - Multiple regions
aws ec2 run-instances \
  --image-id ami-0c02fb55956c7d316 \
  --instance-type t3.large \
  --key-name your-key \
  --security-group-ids sg-relay \
  --user-data file://scripts/setup-relay.sh
```

### Phase 2: Server Configuration

#### 1. Authentication Server Setup
```bash
# SSH to auth server
ssh -i your-key.pem <EMAIL>

# Install dependencies
sudo yum update -y
sudo yum install -y nodejs npm git

# Clone and setup
git clone https://github.com/your-org/remoteit.git
cd remoteit
npm install

# Configure environment
cat > .env << EOF
NODE_ENV=production
PORT=3000
JWT_SECRET=your-super-secret-jwt-key
DB_HOST=your-rds-endpoint
DB_PASSWORD=your-db-password
REDIS_HOST=your-redis-endpoint
EOF

# Start service
npm run start:auth
```

#### 2. Relay Server Setup
```bash
# SSH to relay server
ssh -i your-key.pem <EMAIL>

# Install and configure
sudo yum update -y
sudo yum install -y nodejs npm git

git clone https://github.com/your-org/remoteit.git
cd remoteit
npm install

# Configure relay
cat > .env << EOF
NODE_ENV=production
PORT=8080
WS_PORT=8081
AUTH_SERVER_URL=https://api.remoteit.com
EOF

# Start relay service
npm run start:relay
```

#### 3. STUN/TURN Server Setup
```bash
# SSH to STUN server
ssh -i your-key.pem <EMAIL>

# Install coturn (open source STUN/TURN server)
sudo yum install -y epel-release
sudo yum install -y coturn

# Configure coturn
sudo cat > /etc/turnserver.conf << EOF
listening-port=3478
tls-listening-port=5349
listening-ip=0.0.0.0
relay-ip=YOUR_SERVER_PUBLIC_IP
external-ip=YOUR_SERVER_PUBLIC_IP
realm=remoteit.com
server-name=stun.remoteit.com
fingerprint
lt-cred-mech
user=remoteit:secure-turn-password
total-quota=100
stale-nonce=600
cert=/etc/ssl/certs/remoteit.crt
pkey=/etc/ssl/private/remoteit.key
cipher-list="ECDH+AESGCM:DH+AESGCM:ECDH+AES256:DH+AES256:ECDH+AES128:DH+AES:ECDH+3DES:DH+3DES:RSA+AESGCM:RSA+AES:RSA+3DES:!aNULL:!MD5:!DSS"
no-loopback-peers
no-multicast-peers
EOF

# Start coturn
sudo systemctl enable coturn
sudo systemctl start coturn
```

### Phase 3: Application Build & Distribution

#### 1. Build Installers
```bash
# Build for all platforms
node installer/create-installer.js all

# Upload to CDN/Download server
aws s3 cp dist/ s3://remoteit-downloads/ --recursive
```

#### 2. Create Download Page
```html
<!DOCTYPE html>
<html>
<head>
    <title>Download RemoteIt</title>
</head>
<body>
    <h1>Download RemoteIt</h1>
    <div class="downloads">
        <a href="https://download.remoteit.com/RemoteIt-Setup.exe">
            Windows Installer (64-bit)
        </a>
        <a href="https://download.remoteit.com/RemoteIt.dmg">
            macOS Installer
        </a>
        <a href="https://download.remoteit.com/RemoteIt.AppImage">
            Linux AppImage
        </a>
    </div>
</body>
</html>
```

### Phase 4: SSL/Security Setup

#### 1. SSL Certificates (Let's Encrypt)
```bash
# Install certbot
sudo yum install -y certbot

# Get certificates for all domains
sudo certbot certonly --standalone -d api.remoteit.com
sudo certbot certonly --standalone -d relay.remoteit.com
sudo certbot certonly --standalone -d stun.remoteit.com
sudo certbot certonly --standalone -d download.remoteit.com

# Setup auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

#### 2. Nginx Load Balancer
```nginx
# /etc/nginx/sites-available/remoteit
upstream auth_servers {
    server *********:3000;
    server *********:3000;
}

upstream relay_servers {
    server *********:8080;
    server *********:8080;
}

server {
    listen 443 ssl http2;
    server_name api.remoteit.com;
    
    ssl_certificate /etc/letsencrypt/live/api.remoteit.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.remoteit.com/privkey.pem;
    
    location / {
        proxy_pass http://auth_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

server {
    listen 443 ssl http2;
    server_name relay.remoteit.com;
    
    ssl_certificate /etc/letsencrypt/live/relay.remoteit.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/relay.remoteit.com/privkey.pem;
    
    location / {
        proxy_pass http://relay_servers;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🧪 Testing Internet Connectivity

### 1. Test STUN Server
```bash
# Test STUN server
npm install -g stun

stun stun.remoteit.com 3478
# Should return your public IP
```

### 2. Test API Endpoints
```bash
# Test authentication server
curl https://api.remoteit.com/health

# Test relay server
curl https://relay.remoteit.com/health
```

### 3. Test Full Connection
```bash
# On first device
node test-internet-connection.js host

# On second device (different network)
node test-internet-connection.js client DEVICE_ID
```

## 👥 User Experience Flow

### 1. Installation
1. User downloads installer from `https://download.remoteit.com`
2. Runs installer (one-click, no configuration needed)
3. App starts automatically
4. Shows unique 9-digit Device ID (e.g., `123 456 789`)

### 2. First Connection
1. User A shares their Device ID with User B
2. User B enters Device ID in "Connect to Device" field
3. User B clicks "Connect"
4. User A receives connection request notification
5. User A clicks "Accept"
6. Connection established automatically via cloud

### 3. Subsequent Connections
1. Devices remember each other (if permitted)
2. One-click connection without approval
3. Unattended access mode available

## 📊 Monitoring & Maintenance

### 1. Health Monitoring
```bash
# Setup monitoring endpoints
curl https://api.remoteit.com/health
curl https://relay.remoteit.com/health
curl https://stun.remoteit.com/health
```

### 2. Log Monitoring
```bash
# Centralized logging with ELK stack
# View logs in Kibana dashboard
# Set up alerts for errors
```

### 3. Performance Metrics
- Connection success rate
- Average connection time
- Data transfer volumes
- Active users/devices

## 💰 Cost Estimation

### Monthly Costs (AWS)
- **EC2 Instances**: $200-800/month
- **RDS Database**: $100-300/month
- **ElastiCache Redis**: $50-150/month
- **Data Transfer**: $100-500/month
- **Load Balancer**: $25/month
- **CloudFront CDN**: $50-200/month
- **Total**: $525-1975/month

### Scaling Considerations
- Start with 2 regions (US, EU)
- Add more regions as user base grows
- Auto-scaling for peak usage
- CDN for global app distribution

## 🔒 Security Considerations

### 1. Network Security
- All traffic encrypted (TLS 1.3)
- VPC with private subnets
- Security groups with minimal access
- DDoS protection via CloudFlare

### 2. Application Security
- JWT tokens with short expiration
- Device certificates for authentication
- Rate limiting on all endpoints
- Input validation and sanitization

### 3. Data Protection
- End-to-end encryption for sessions
- No session data stored on servers
- GDPR compliance for EU users
- Regular security audits

## 🚀 Go Live Checklist

- [ ] Domain configured with SSL
- [ ] All servers deployed and running
- [ ] Database initialized with schema
- [ ] STUN/TURN servers operational
- [ ] Load balancers configured
- [ ] Monitoring setup complete
- [ ] Installers built and uploaded
- [ ] Download page published
- [ ] Security testing completed
- [ ] Performance testing done
- [ ] Documentation updated
- [ ] Support system ready

## 📞 Support & Troubleshooting

### Common Issues
1. **Connection fails**: Check STUN/TURN servers
2. **Slow performance**: Check relay server load
3. **Authentication errors**: Verify API server status
4. **Firewall issues**: Ensure ports 3478, 8080, 8081 open

### Support Channels
- Documentation: `https://docs.remoteit.com`
- Support Portal: `https://support.remoteit.com`
- Community Forum: `https://community.remoteit.com`
- Emergency Contact: `<EMAIL>`

---

**RemoteIt is now ready for global internet deployment! 🌍**
