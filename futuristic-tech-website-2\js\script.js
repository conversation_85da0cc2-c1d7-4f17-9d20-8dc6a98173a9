// Futuristic Tech Website JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all interactive features
    initScrollAnimations();
    initCounterAnimations();
    initParticleEffects();
    initSmoothScrolling();
    initHoverEffects();
    initTypingEffect();
});

// Scroll-based animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe all animated elements
    const animatedElements = document.querySelectorAll('.stat-item, .service-item, .desc-card, .solution-card');
    animatedElements.forEach(el => {
        el.classList.add('animate-on-scroll');
        observer.observe(el);
    });
}

// Counter animations for statistics
function initCounterAnimations() {
    const counters = document.querySelectorAll('.stat-number, .center-stat .number, .stat-card .number');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.textContent);
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                counter.textContent = target + (counter.textContent.includes('%') ? '%' : '+');
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current) + (counter.textContent.includes('%') ? '%' : '+');
            }
        }, 16);
    };

    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !entry.target.classList.contains('animated')) {
                entry.target.classList.add('animated');
                setTimeout(() => animateCounter(entry.target), 500);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => counterObserver.observe(counter));
}

// Particle effects for the sphere
function initParticleEffects() {
    const sphereContainer = document.querySelector('.sphere-container');
    if (!sphereContainer) return;

    // Create floating particles around the sphere
    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.cssText = `
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00ff88;
            border-radius: 50%;
            opacity: 0;
            pointer-events: none;
        `;
        sphereContainer.appendChild(particle);
        
        animateParticle(particle);
    }
}

function animateParticle(particle) {
    const animate = () => {
        const angle = Math.random() * Math.PI * 2;
        const radius = 150 + Math.random() * 50;
        const duration = 3000 + Math.random() * 2000;
        
        particle.style.left = '50%';
        particle.style.top = '50%';
        particle.style.opacity = '0';
        
        particle.animate([
            {
                transform: 'translate(-50%, -50%)',
                opacity: 0
            },
            {
                transform: `translate(${Math.cos(angle) * radius - 50}%, ${Math.sin(angle) * radius - 50}%)`,
                opacity: 1,
                offset: 0.1
            },
            {
                transform: `translate(${Math.cos(angle) * radius - 50}%, ${Math.sin(angle) * radius - 50}%)`,
                opacity: 1,
                offset: 0.9
            },
            {
                transform: `translate(${Math.cos(angle) * (radius + 50) - 50}%, ${Math.sin(angle) * (radius + 50) - 50}%)`,
                opacity: 0
            }
        ], {
            duration: duration,
            easing: 'ease-out'
        }).onfinish = () => {
            setTimeout(() => animate(), Math.random() * 1000);
        };
    };
    
    setTimeout(() => animate(), Math.random() * 2000);
}

// Smooth scrolling for navigation
function initSmoothScrolling() {
    const navLinks = document.querySelectorAll('.nav-menu a');
    
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            const href = link.getAttribute('href');
            if (href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
}

// Enhanced hover effects
function initHoverEffects() {
    // Card hover effects
    const cards = document.querySelectorAll('.solution-card, .innovation-card, .software-section, .service-item, .desc-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-5px)';
            card.style.boxShadow = '0 10px 30px rgba(0, 255, 136, 0.2)';
            card.style.borderColor = 'rgba(0, 255, 136, 0.3)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0)';
            card.style.boxShadow = 'none';
            card.style.borderColor = 'rgba(255, 255, 255, 0.1)';
        });
    });

    // Sphere interaction
    const sphere = document.querySelector('.sphere');
    if (sphere) {
        sphere.addEventListener('mouseenter', () => {
            sphere.style.transform = 'scale(1.05)';
            sphere.style.boxShadow = '0 25px 50px rgba(0, 255, 136, 0.3)';
        });
        
        sphere.addEventListener('mouseleave', () => {
            sphere.style.transform = 'scale(1)';
            sphere.style.boxShadow = '0 20px 40px rgba(255, 255, 255, 0.1)';
        });
    }
}

// Typing effect for main title
function initTypingEffect() {
    const mainTitle = document.querySelector('.main-title');
    if (!mainTitle) return;
    
    const text = mainTitle.textContent;
    mainTitle.textContent = '';
    mainTitle.style.borderRight = '2px solid #00ff88';
    
    let i = 0;
    const typeWriter = () => {
        if (i < text.length) {
            mainTitle.textContent += text.charAt(i);
            i++;
            setTimeout(typeWriter, 100);
        } else {
            // Remove cursor after typing is complete
            setTimeout(() => {
                mainTitle.style.borderRight = 'none';
            }, 1000);
        }
    };
    
    // Start typing effect after a delay
    setTimeout(typeWriter, 1000);
}

// Parallax scrolling effect
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const parallaxElements = document.querySelectorAll('.sphere-container, .main-title');
    
    parallaxElements.forEach(element => {
        const speed = 0.5;
        element.style.transform = `translateY(${scrolled * speed}px)`;
    });
});

// Add CSS animations via JavaScript
const style = document.createElement('style');
style.textContent = `
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }
    
    .animate-in {
        opacity: 1;
        transform: translateY(0);
    }
    
    .particle {
        z-index: 1;
    }
    
    .solution-card, .innovation-card, .software-section, .service-item, .desc-card {
        transition: all 0.3s ease;
    }
    
    .sphere {
        transition: all 0.3s ease;
    }
    
    @keyframes glow {
        0%, 100% { box-shadow: 0 0 20px rgba(0, 255, 136, 0.3); }
        50% { box-shadow: 0 0 40px rgba(0, 255, 136, 0.6); }
    }
    
    .stat-number, .center-stat .number, .stat-card .number {
        transition: color 0.3s ease;
    }
    
    .stat-number:hover, .center-stat .number:hover, .stat-card .number:hover {
        color: #00ffaa;
        text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    }
`;
document.head.appendChild(style);

// Performance optimization: Throttle scroll events
function throttle(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply throttling to scroll events
window.addEventListener('scroll', throttle(() => {
    // Scroll-based animations can be added here
}, 16));

// Add loading animation
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
    
    // Trigger initial animations
    setTimeout(() => {
        const heroElements = document.querySelectorAll('.hero-left, .hero-center, .hero-right');
        heroElements.forEach((el, index) => {
            setTimeout(() => {
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            }, index * 200);
        });
    }, 500);
});

// Initial setup for hero elements
document.addEventListener('DOMContentLoaded', () => {
    const heroElements = document.querySelectorAll('.hero-left, .hero-center, .hero-right');
    heroElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'all 0.8s ease';
    });
});
