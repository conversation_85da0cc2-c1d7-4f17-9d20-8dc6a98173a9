<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Templates Collection</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }
        
        .template-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .template-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        
        .template-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
        }
        
        .template-header h3 {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .template-header p {
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .template-content {
            padding: 1.5rem;
        }
        
        .template-features {
            list-style: none;
            margin-bottom: 1.5rem;
        }
        
        .template-features li {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: #6b7280;
        }
        
        .template-features i {
            color: #10b981;
            width: 16px;
        }
        
        .template-actions {
            display: flex;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.9rem;
            flex: 1;
            text-align: center;
        }
        
        .btn-primary {
            background: #6366f1;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5855eb;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .btn-secondary:hover {
            background: #e5e7eb;
            color: #374151;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }
        
        .status-complete {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-framework {
            background: #fef3c7;
            color: #92400e;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 3rem;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .templates-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .template-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> Portfolio Templates Collection</h1>
            <p>10 Modern, Responsive Website Templates with Mobile App Versions</p>
        </div>
        
        <div class="templates-grid">
            <div class="template-card">
                <div class="template-header">
                    <h3><i class="fas fa-shopping-cart"></i> E-commerce - ShopHub <span class="status-badge status-complete">Complete</span></h3>
                    <p>Modern online shopping experience</p>
                </div>
                <div class="template-content">
                    <ul class="template-features">
                        <li><i class="fas fa-check"></i> Product catalog & shopping cart</li>
                        <li><i class="fas fa-check"></i> Search & filter functionality</li>
                        <li><i class="fas fa-check"></i> Wishlist & user accounts</li>
                        <li><i class="fas fa-check"></i> Mobile app version</li>
                        <li><i class="fas fa-check"></i> AI-generated images</li>
                    </ul>
                    <div class="template-actions">
                        <a href="ecommerce-template/web/index.html" class="btn btn-primary">View Web</a>
                        <a href="ecommerce-template/mobile/index.html" class="btn btn-secondary">View Mobile</a>
                    </div>
                </div>
            </div>
            
            <div class="template-card">
                <div class="template-header">
                    <h3><i class="fas fa-home"></i> Real Estate - PropertyHub <span class="status-badge status-complete">Complete</span></h3>
                    <p>Premium property listings platform</p>
                </div>
                <div class="template-content">
                    <ul class="template-features">
                        <li><i class="fas fa-check"></i> Property search & filters</li>
                        <li><i class="fas fa-check"></i> Agent profiles & stats</li>
                        <li><i class="fas fa-check"></i> Market analytics</li>
                        <li><i class="fas fa-check"></i> Property galleries</li>
                        <li><i class="fas fa-check"></i> AI-generated imagery</li>
                    </ul>
                    <div class="template-actions">
                        <a href="real-estate-template/web/index.html" class="btn btn-primary">View Web</a>
                        <a href="real-estate-template/mobile/index.html" class="btn btn-secondary">Mobile Soon</a>
                    </div>
                </div>
            </div>
            
            <div class="template-card">
                <div class="template-header">
                    <h3><i class="fas fa-utensils"></i> Restaurant - Bella Vista <span class="status-badge status-complete">Complete</span></h3>
                    <p>Fine dining restaurant experience</p>
                </div>
                <div class="template-content">
                    <ul class="template-features">
                        <li><i class="fas fa-check"></i> Interactive menu display</li>
                        <li><i class="fas fa-check"></i> Online reservations</li>
                        <li><i class="fas fa-check"></i> Chef profiles</li>
                        <li><i class="fas fa-check"></i> Contact & location</li>
                        <li><i class="fas fa-check"></i> Elegant design</li>
                    </ul>
                    <div class="template-actions">
                        <a href="restaurant-template/web/index.html" class="btn btn-primary">View Web</a>
                        <a href="restaurant-template/mobile/index.html" class="btn btn-secondary">Mobile Soon</a>
                    </div>
                </div>
            </div>
            
            <div class="template-card">
                <div class="template-header">
                    <h3><i class="fas fa-taxi"></i> Taxi Booking - RideNow <span class="status-badge status-complete">Complete</span></h3>
                    <p>Ride booking and tracking platform</p>
                </div>
                <div class="template-content">
                    <ul class="template-features">
                        <li><i class="fas fa-check"></i> Ride booking interface</li>
                        <li><i class="fas fa-check"></i> Driver tracking system</li>
                        <li><i class="fas fa-check"></i> Fare calculation</li>
                        <li><i class="fas fa-check"></i> Trip history</li>
                        <li><i class="fas fa-check"></i> Mobile-first design</li>
                    </ul>
                    <div class="template-actions">
                        <a href="taxi-booking-template/web/index.html" class="btn btn-primary">View Web</a>
                        <a href="taxi-booking-template/mobile/index.html" class="btn btn-secondary">Mobile Soon</a>
                    </div>
                </div>
            </div>
            
            <div class="template-card">
                <div class="template-header">
                    <h3><i class="fas fa-tshirt"></i> Boutique - LuxeFashion <span class="status-badge status-complete">Complete</span></h3>
                    <p>Luxury fashion showcase</p>
                </div>
                <div class="template-content">
                    <ul class="template-features">
                        <li><i class="fas fa-check"></i> Fashion product galleries</li>
                        <li><i class="fas fa-check"></i> Lookbook displays</li>
                        <li><i class="fas fa-check"></i> Style guides</li>
                        <li><i class="fas fa-check"></i> Brand storytelling</li>
                        <li><i class="fas fa-check"></i> Luxury aesthetic</li>
                    </ul>
                    <div class="template-actions">
                        <a href="boutique-template/web/index.html" class="btn btn-primary">View Web</a>
                        <a href="boutique-template/mobile/index.html" class="btn btn-secondary">Mobile Soon</a>
                    </div>
                </div>
            </div>
            
            <div class="template-card">
                <div class="template-header">
                    <h3><i class="fas fa-spa"></i> Spa & Wellness - ZenSpa <span class="status-badge status-complete">Complete</span></h3>
                    <p>Relaxing wellness experience</p>
                </div>
                <div class="template-content">
                    <ul class="template-features">
                        <li><i class="fas fa-check"></i> Service bookings</li>
                        <li><i class="fas fa-check"></i> Treatment menus</li>
                        <li><i class="fas fa-check"></i> Therapist profiles</li>
                        <li><i class="fas fa-check"></i> Wellness blog</li>
                        <li><i class="fas fa-check"></i> Calming design</li>
                    </ul>
                    <div class="template-actions">
                        <a href="spa-wellness-template/web/index.html" class="btn btn-primary">View Web</a>
                        <a href="spa-wellness-template/mobile/index.html" class="btn btn-secondary">Mobile Soon</a>
                    </div>
                </div>
            </div>
            
            <div class="template-card">
                <div class="template-header">
                    <h3><i class="fas fa-bus"></i> Bus Booking - BusGo <span class="status-badge status-complete">Complete</span></h3>
                    <p>Travel booking platform</p>
                </div>
                <div class="template-content">
                    <ul class="template-features">
                        <li><i class="fas fa-check"></i> Route search</li>
                        <li><i class="fas fa-check"></i> Seat selection</li>
                        <li><i class="fas fa-check"></i> Ticket booking</li>
                        <li><i class="fas fa-check"></i> Schedule displays</li>
                        <li><i class="fas fa-check"></i> Mobile tickets</li>
                    </ul>
                    <div class="template-actions">
                        <a href="bus-booking-template/web/index.html" class="btn btn-primary">View Web</a>
                        <a href="bus-booking-template/mobile/index.html" class="btn btn-secondary">Mobile Soon</a>
                    </div>
                </div>
            </div>
            
            <div class="template-card">
                <div class="template-header">
                    <h3><i class="fas fa-graduation-cap"></i> Tuition Finder - EduConnect <span class="status-badge status-complete">Complete</span></h3>
                    <p>Education platform for tutors</p>
                </div>
                <div class="template-content">
                    <ul class="template-features">
                        <li><i class="fas fa-check"></i> Teacher profiles</li>
                        <li><i class="fas fa-check"></i> Subject filters</li>
                        <li><i class="fas fa-check"></i> Booking system</li>
                        <li><i class="fas fa-check"></i> Reviews & ratings</li>
                        <li><i class="fas fa-check"></i> Educational design</li>
                    </ul>
                    <div class="template-actions">
                        <a href="tuition-teacher-template/web/index.html" class="btn btn-primary">View Web</a>
                        <a href="tuition-teacher-template/mobile/index.html" class="btn btn-secondary">Mobile Soon</a>
                    </div>
                </div>
            </div>
            
            <div class="template-card">
                <div class="template-header">
                    <h3><i class="fas fa-heartbeat"></i> Healthcare - MediCare <span class="status-badge status-complete">Complete</span></h3>
                    <p>Medical services platform</p>
                </div>
                <div class="template-content">
                    <ul class="template-features">
                        <li><i class="fas fa-check"></i> Doctor appointments</li>
                        <li><i class="fas fa-check"></i> Patient portal</li>
                        <li><i class="fas fa-check"></i> Health records</li>
                        <li><i class="fas fa-check"></i> Medical services</li>
                        <li><i class="fas fa-check"></i> Professional design</li>
                    </ul>
                    <div class="template-actions">
                        <a href="healthcare-template/web/index.html" class="btn btn-primary">View Web</a>
                        <a href="healthcare-template/mobile/index.html" class="btn btn-secondary">Mobile Soon</a>
                    </div>
                </div>
            </div>
            
            <div class="template-card">
                <div class="template-header">
                    <h3><i class="fas fa-plane"></i> Travel & Tourism - WanderLust <span class="status-badge status-complete">Complete</span></h3>
                    <p>Adventure travel planning</p>
                </div>
                <div class="template-content">
                    <ul class="template-features">
                        <li><i class="fas fa-check"></i> Destination guides</li>
                        <li><i class="fas fa-check"></i> Hotel bookings</li>
                        <li><i class="fas fa-check"></i> Tour packages</li>
                        <li><i class="fas fa-check"></i> Travel planning</li>
                        <li><i class="fas fa-check"></i> Adventure theme</li>
                    </ul>
                    <div class="template-actions">
                        <a href="travel-tourism-template/web/index.html" class="btn btn-primary">View Web</a>
                        <a href="travel-tourism-template/mobile/index.html" class="btn btn-secondary">Mobile Soon</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p><i class="fas fa-code"></i> Built with modern web technologies and AI-generated imagery</p>
            <p>Ready for customization and deployment</p>
        </div>
    </div>
</body>
</html>
