version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: remoteit-postgres
    environment:
      POSTGRES_DB: remoteit
      POSTGRES_USER: remoteit
      POSTGRES_PASSWORD: ${DB_PASSWORD:-remoteit123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - remoteit-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: remoteit-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - remoteit-network
    restart: unless-stopped

  # Authentication Server
  auth-server:
    build:
      context: .
      dockerfile: server/auth/Dockerfile
    container_name: remoteit-auth
    environment:
      NODE_ENV: production
      PORT: 3000
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key}
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: remoteit
      DB_USER: remoteit
      DB_PASSWORD: ${DB_PASSWORD:-remoteit123}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis123}
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - redis
    networks:
      - remoteit-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Relay Server
  relay-server:
    build:
      context: .
      dockerfile: server/relay/Dockerfile
    container_name: remoteit-relay
    environment:
      NODE_ENV: production
      PORT: 8080
      WS_PORT: 8081
      AUTH_SERVER_URL: http://auth-server:3000
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis123}
    ports:
      - "8080:8080"
      - "8081:8081"
    depends_on:
      - redis
      - auth-server
    networks:
      - remoteit-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # File Server
  file-server:
    build:
      context: .
      dockerfile: server/file/Dockerfile
    container_name: remoteit-file
    environment:
      NODE_ENV: production
      PORT: 3001
      AUTH_SERVER_URL: http://auth-server:3000
      STORAGE_TYPE: ${STORAGE_TYPE:-local}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_REGION: ${AWS_REGION:-us-east-1}
      AWS_S3_BUCKET: ${AWS_S3_BUCKET}
    ports:
      - "3001:3001"
    volumes:
      - file_storage:/app/storage
    depends_on:
      - auth-server
    networks:
      - remoteit-network
    restart: unless-stopped

  # Web Portal
  web-portal:
    build:
      context: .
      dockerfile: web/portal/Dockerfile
    container_name: remoteit-web
    environment:
      NODE_ENV: production
      PORT: 80
      API_BASE_URL: ${API_BASE_URL:-http://localhost:3000}
      RELAY_URL: ${RELAY_URL:-ws://localhost:8081}
    ports:
      - "80:80"
    depends_on:
      - auth-server
      - relay-server
    networks:
      - remoteit-network
    restart: unless-stopped

  # Load Balancer (Nginx)
  nginx:
    image: nginx:alpine
    container_name: remoteit-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    ports:
      - "443:443"
      - "8443:8443"
    depends_on:
      - auth-server
      - relay-server
      - web-portal
    networks:
      - remoteit-network
    restart: unless-stopped

  # Monitoring (Prometheus)
  prometheus:
    image: prom/prometheus:latest
    container_name: remoteit-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - remoteit-network
    restart: unless-stopped

  # Monitoring (Grafana)
  grafana:
    image: grafana/grafana:latest
    container_name: remoteit-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin123}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - remoteit-network
    restart: unless-stopped

  # Log Aggregation (ELK Stack - Elasticsearch)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: remoteit-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - remoteit-network
    restart: unless-stopped

  # Log Aggregation (Kibana)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: remoteit-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - remoteit-network
    restart: unless-stopped

  # Log Shipping (Logstash)
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: remoteit-logstash
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./monitoring/logstash/config:/usr/share/logstash/config
    ports:
      - "5044:5044"
      - "9600:9600"
    depends_on:
      - elasticsearch
    networks:
      - remoteit-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  file_storage:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  remoteit-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
