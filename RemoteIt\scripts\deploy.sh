#!/bin/bash

# RemoteIt Deployment Script
# This script handles the complete deployment of RemoteIt infrastructure

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-development}
PROJECT_NAME="remoteit"
COMPOSE_FILE="docker-compose.yml"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    log_success "All dependencies are available"
}

setup_environment() {
    log_info "Setting up environment for: $ENVIRONMENT"
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        log_info "Creating .env file..."
        cat > .env << EOF
# RemoteIt Environment Configuration
ENVIRONMENT=$ENVIRONMENT

# Database
DB_PASSWORD=remoteit_secure_password_$(openssl rand -hex 16)
REDIS_PASSWORD=redis_secure_password_$(openssl rand -hex 16)

# JWT Secret
JWT_SECRET=$(openssl rand -hex 32)

# Grafana
GRAFANA_PASSWORD=admin_$(openssl rand -hex 8)

# API URLs (adjust for production)
API_BASE_URL=http://localhost:3000
RELAY_URL=ws://localhost:8081

# Storage (for production, use S3)
STORAGE_TYPE=local
# AWS_ACCESS_KEY_ID=your_aws_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret
# AWS_REGION=us-east-1
# AWS_S3_BUCKET=remoteit-files
EOF
        log_success ".env file created with secure passwords"
    else
        log_info ".env file already exists"
    fi
}

build_images() {
    log_info "Building Docker images..."
    
    # Build all services
    docker-compose build --parallel
    
    log_success "Docker images built successfully"
}

start_infrastructure() {
    log_info "Starting infrastructure services..."
    
    # Start database and cache first
    docker-compose up -d postgres redis
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    sleep 10
    
    # Check database connection
    until docker-compose exec -T postgres pg_isready -U remoteit; do
        log_info "Waiting for database..."
        sleep 2
    done
    
    log_success "Database is ready"
}

start_services() {
    log_info "Starting application services..."
    
    # Start all services
    docker-compose up -d
    
    # Wait for services to be healthy
    log_info "Waiting for services to be healthy..."
    sleep 15
    
    # Check service health
    check_service_health
}

check_service_health() {
    log_info "Checking service health..."
    
    services=("auth-server:3000" "relay-server:8080" "web-portal:80")
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        
        if curl -f -s "http://localhost:$port/health" > /dev/null; then
            log_success "$name is healthy"
        else
            log_warning "$name health check failed"
        fi
    done
}

setup_monitoring() {
    log_info "Setting up monitoring..."
    
    # Create monitoring directories
    mkdir -p monitoring/{prometheus,grafana/{dashboards,datasources},logstash/{pipeline,config}}
    
    # Create Prometheus configuration
    cat > monitoring/prometheus.yml << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'remoteit-auth'
    static_configs:
      - targets: ['auth-server:3000']
    metrics_path: '/metrics'

  - job_name: 'remoteit-relay'
    static_configs:
      - targets: ['relay-server:8080']
    metrics_path: '/metrics'

  - job_name: 'remoteit-web'
    static_configs:
      - targets: ['web-portal:80']
    metrics_path: '/metrics'
EOF

    # Create Grafana datasource
    cat > monitoring/grafana/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    log_success "Monitoring configuration created"
}

run_database_migrations() {
    log_info "Running database migrations..."
    
    # Create database schema
    docker-compose exec -T postgres psql -U remoteit -d remoteit << EOF
-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    mfa_enabled BOOLEAN DEFAULT FALSE,
    email_verified BOOLEAN DEFAULT FALSE
);

-- Devices table
CREATE TABLE IF NOT EXISTS devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    platform VARCHAR(50) NOT NULL,
    arch VARCHAR(50),
    version VARCHAR(100),
    public_key TEXT,
    is_online BOOLEAN DEFAULT FALSE,
    last_seen TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    unattended_access BOOLEAN DEFAULT FALSE
);

-- Sessions table
CREATE TABLE IF NOT EXISTS sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    host_device_id UUID REFERENCES devices(id) ON DELETE CASCADE,
    client_device_id UUID REFERENCES devices(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    started_at TIMESTAMP DEFAULT NOW(),
    ended_at TIMESTAMP,
    duration INTEGER,
    bytes_transferred BIGINT DEFAULT 0,
    status VARCHAR(50) DEFAULT 'active'
);

-- Audit log table
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    device_id UUID REFERENCES devices(id) ON DELETE SET NULL,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_devices_user_id ON devices(user_id);
CREATE INDEX IF NOT EXISTS idx_devices_online ON devices(is_online);
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_started_at ON sessions(started_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
EOF

    log_success "Database migrations completed"
}

show_deployment_info() {
    log_success "RemoteIt deployment completed successfully!"
    echo
    log_info "Service URLs:"
    echo "  Web Portal:     http://localhost"
    echo "  Auth Server:    http://localhost:3000"
    echo "  Relay Server:   http://localhost:8080"
    echo "  WebSocket:      ws://localhost:8081"
    echo "  Grafana:        http://localhost:3001 (admin/admin123)"
    echo "  Prometheus:     http://localhost:9090"
    echo "  Kibana:         http://localhost:5601"
    echo
    log_info "Default login credentials:"
    echo "  Email:    <EMAIL>"
    echo "  Password: admin123"
    echo
    log_info "To view logs: docker-compose logs -f [service-name]"
    log_info "To stop services: docker-compose down"
    log_info "To stop and remove volumes: docker-compose down -v"
}

cleanup() {
    log_info "Cleaning up..."
    docker-compose down
    docker system prune -f
    log_success "Cleanup completed"
}

# Main deployment flow
main() {
    log_info "Starting RemoteIt deployment..."
    
    case "${1:-deploy}" in
        "deploy")
            check_dependencies
            setup_environment
            setup_monitoring
            build_images
            start_infrastructure
            run_database_migrations
            start_services
            show_deployment_info
            ;;
        "cleanup")
            cleanup
            ;;
        "restart")
            log_info "Restarting services..."
            docker-compose restart
            check_service_health
            ;;
        "logs")
            docker-compose logs -f "${2:-}"
            ;;
        "status")
            docker-compose ps
            check_service_health
            ;;
        *)
            echo "Usage: $0 {deploy|cleanup|restart|logs|status}"
            echo "  deploy  - Full deployment (default)"
            echo "  cleanup - Stop and clean up all services"
            echo "  restart - Restart all services"
            echo "  logs    - Show logs (optionally specify service)"
            echo "  status  - Show service status"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
