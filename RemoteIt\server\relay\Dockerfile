# RemoteIt Relay Server Dockerfile
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY server/relay/ ./server/relay/

# Production stage
FROM node:18-alpine AS production

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S remoteit -u 1001

# Set working directory
WORKDIR /app

# Copy built application
COPY --from=builder --chown=remoteit:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=remoteit:nodejs /app/server/relay ./server/relay

# Create logs directory
RUN mkdir -p /app/logs && chown remoteit:nodejs /app/logs

# Switch to non-root user
USER remoteit

# Expose ports
EXPOSE 8080 8081

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Start application with dumb-init
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server/relay/RelayServer.js"]
