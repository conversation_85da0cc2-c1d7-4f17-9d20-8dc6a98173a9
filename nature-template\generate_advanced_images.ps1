# 🌿 Green Nature Tech - Advanced Image Generation with New Features
# Utilizes all new capabilities: dimensions, seeds, timeouts, priorities

param(
    [string]$OutputDir = "images",
    [switch]$UseWaitEndpoint = $false,
    [switch]$Force = $false,
    [int]$BaseSeed = $null
)

Write-Host "🌿 ADVANCED IMAGE GENERATION - NEW FEATURES" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Advanced image configuration with new parameters
$ImageRequests = @(
    @{
        Name = "hero-background-hd"
        Filename = "hero-background-hd.jpg"
        Prompt = "Dark mystical forest landscape with subtle green technology elements, misty atmosphere, tall ancient trees, green glowing accents, professional nature photography"
        Width = 1920
        Height = 1080
        Priority = "high"
        Timeout = 300
        Seed = if ($BaseSeed) { $BaseSeed + 1 } else { $null }
    },
    @{
        Name = "green-innovation-card"
        Filename = "green-innovation-card.jpg"
        Prompt = "Modern sustainable technology laboratory, eco-friendly innovation, green energy solutions, clean modern design, professional photography"
        Width = 800
        Height = 600
        Priority = "high"
        Timeout = 240
        Seed = if ($BaseSeed) { $BaseSeed + 2 } else { $null }
    },
    @{
        Name = "nature-tech-hero"
        Filename = "nature-tech-hero.jpg"
        Prompt = "Technology and nature perfect harmony, biomimicry innovation, sustainable future vision, green tech integration, professional concept art"
        Width = 1024
        Height = 768
        Priority = "high"
        Timeout = 240
        Seed = if ($BaseSeed) { $BaseSeed + 3 } else { $null }
    },
    @{
        Name = "eco-solutions-square"
        Filename = "eco-solutions-square.jpg"
        Prompt = "Eco-friendly technology solutions, renewable energy concepts, sustainable design, environmental innovation, clean aesthetic"
        Width = 800
        Height = 800
        Priority = "normal"
        Timeout = 180
        Seed = if ($BaseSeed) { $BaseSeed + 4 } else { $null }
    },
    @{
        Name = "forest-tech-wide"
        Filename = "forest-tech-wide.jpg"
        Prompt = "Dense green forest with integrated technology, sustainable innovation, nature-tech balance, environmental harmony"
        Width = 1200
        Height = 600
        Priority = "normal"
        Timeout = 180
        Seed = if ($BaseSeed) { $BaseSeed + 5 } else { $null }
    },
    @{
        Name = "sustainable-future-portrait"
        Filename = "sustainable-future-portrait.jpg"
        Prompt = "Sustainable future cityscape, green architecture, renewable energy, eco-friendly urban planning, vertical gardens"
        Width = 600
        Height = 900
        Priority = "normal"
        Timeout = 200
        Seed = if ($BaseSeed) { $BaseSeed + 6 } else { $null }
    }
)

# Ensure output directory exists
if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

function Test-ServiceHealth {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:7777/health" -TimeoutSec 5
        return $response.status -eq "healthy"
    }
    catch {
        return $false
    }
}

function Submit-AdvancedImageRequest {
    param([hashtable]$ImageConfig, [bool]$UseWait = $false)
    
    $endpoint = if ($UseWait) { "/api/generate-and-wait" } else { "/api/generate" }
    
    try {
        $body = @{
            type = "image"
            prompt = $ImageConfig.Prompt
            width = $ImageConfig.Width
            height = $ImageConfig.Height
            timeout = $ImageConfig.Timeout
            priority = $ImageConfig.Priority
        }
        
        # Add seed if specified
        if ($ImageConfig.Seed -ne $null) {
            $body.seed = $ImageConfig.Seed
        }
        
        $jsonBody = $body | ConvertTo-Json
        
        Write-Host "📝 Request Body:" -ForegroundColor Cyan
        Write-Host $jsonBody -ForegroundColor Gray
        
        $response = Invoke-RestMethod -Uri "http://localhost:7777$endpoint" -Method POST -Body $jsonBody -ContentType "application/json"
        
        if ($response.success) {
            return @{
                Success = $true
                RequestId = $response.requestId
                Response = $response
            }
        }
        else {
            return @{ Success = $false; Error = "API returned success=false" }
        }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

function Wait-ForAdvancedCompletion {
    param([string]$RequestId, [string]$ImageName, [int]$TimeoutSeconds)
    
    $startTime = Get-Date
    $timeout = $startTime.AddSeconds($TimeoutSeconds + 30) # Add buffer
    
    Write-Host "⏳ Waiting for '$ImageName' (ID: $RequestId, Timeout: ${TimeoutSeconds}s)..." -ForegroundColor Yellow
    
    while ((Get-Date) -lt $timeout) {
        try {
            $status = Invoke-RestMethod -Uri "http://localhost:7777/api/status/$RequestId"
            
            switch ($status.status) {
                "completed" {
                    Write-Host "✅ '$ImageName' completed successfully!" -ForegroundColor Green
                    Write-Host "📊 Generation Details:" -ForegroundColor Cyan
                    if ($status.imageParameters) {
                        Write-Host "  🎨 Dimensions: $($status.imageParameters.width)x$($status.imageParameters.height)" -ForegroundColor White
                        Write-Host "  🎲 Seed: $($status.imageParameters.seed)" -ForegroundColor White
                    }
                    if ($status.generationParameters) {
                        Write-Host "  ⏱️ Timeout: $($status.generationParameters.timeout)s" -ForegroundColor White
                        Write-Host "  🚀 Priority: $($status.generationParameters.priority)" -ForegroundColor White
                    }
                    return @{ Success = $true; Status = $status }
                }
                "failed" {
                    Write-Host "❌ '$ImageName' generation failed!" -ForegroundColor Red
                    return @{ Success = $false; Error = "Generation failed" }
                }
                "processing" {
                    Write-Host "🔄 '$ImageName' is processing..." -ForegroundColor Yellow
                }
                "pending" {
                    Write-Host "⏸️ '$ImageName' is queued..." -ForegroundColor Magenta
                }
                default {
                    Write-Host "❓ '$ImageName' status: $($status.status)" -ForegroundColor Yellow
                }
            }
        }
        catch {
            Write-Host "⚠️ Error checking status: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Start-Sleep -Seconds 15
    }
    
    Write-Host "⏰ Timeout waiting for '$ImageName'" -ForegroundColor Red
    return @{ Success = $false; Error = "Timeout" }
}

function Download-AdvancedImage {
    param([string]$RequestId, [string]$OutputPath, [string]$ImageName)
    
    try {
        Write-Host "📥 Downloading '$ImageName'..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri "http://localhost:7777/api/image/$RequestId" -OutFile $OutputPath
        
        if (Test-Path $OutputPath) {
            $fileSize = (Get-Item $OutputPath).Length
            Write-Host "✅ '$ImageName' downloaded! Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "❌ Failed to download '$ImageName'" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error downloading '$ImageName': $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Check service health
Write-Host "🏥 Checking advanced image generation service..." -ForegroundColor Cyan
if (!(Test-ServiceHealth)) {
    Write-Host "❌ Service not available at http://localhost:7777" -ForegroundColor Red
    exit 1
}
Write-Host "✅ Service is healthy and ready!" -ForegroundColor Green

# Display configuration
Write-Host "`n🔧 Configuration:" -ForegroundColor Cyan
Write-Host "  📁 Output Directory: $OutputDir" -ForegroundColor White
Write-Host "  ⚡ Use Wait Endpoint: $UseWaitEndpoint" -ForegroundColor White
Write-Host "  🔄 Force Regenerate: $Force" -ForegroundColor White
Write-Host "  🎲 Base Seed: $(if ($BaseSeed) { $BaseSeed } else { 'Auto-generated' })" -ForegroundColor White

$results = @()
$totalImages = $ImageRequests.Count
$processedCount = 0

foreach ($imageConfig in $ImageRequests) {
    $processedCount++
    Write-Host "`n[$processedCount/$totalImages] 🎯 Processing: $($imageConfig.Name)" -ForegroundColor Magenta
    Write-Host "📐 Dimensions: $($imageConfig.Width)x$($imageConfig.Height)" -ForegroundColor Yellow
    Write-Host "🚀 Priority: $($imageConfig.Priority)" -ForegroundColor Yellow
    Write-Host "⏱️ Timeout: $($imageConfig.Timeout)s" -ForegroundColor Yellow
    Write-Host "🎲 Seed: $(if ($imageConfig.Seed) { $imageConfig.Seed } else { 'Auto-generated' })" -ForegroundColor Yellow
    
    # Check if already exists
    $outputPath = Join-Path $OutputDir $imageConfig.Filename
    if ((Test-Path $outputPath) -and !$Force) {
        Write-Host "⏭️ Already exists, skipping..." -ForegroundColor Yellow
        $results += @{ ImageName = $imageConfig.Name; Status = "Skipped" }
        continue
    }
    
    # Submit request
    $submitResult = Submit-AdvancedImageRequest -ImageConfig $imageConfig -UseWait $UseWaitEndpoint
    if (!$submitResult.Success) {
        Write-Host "❌ Failed to submit: $($submitResult.Error)" -ForegroundColor Red
        $results += @{ ImageName = $imageConfig.Name; Status = "Submit Failed"; Error = $submitResult.Error }
        continue
    }
    
    Write-Host "✅ Submitted! Request ID: $($submitResult.RequestId)" -ForegroundColor Green
    
    if ($UseWaitEndpoint) {
        # Using generate-and-wait endpoint
        Write-Host "⏳ Using generate-and-wait endpoint..." -ForegroundColor Cyan
        $downloadSuccess = Download-AdvancedImage -RequestId $submitResult.RequestId -OutputPath $outputPath -ImageName $imageConfig.Name
        $status = if ($downloadSuccess) { "Success" } else { "Download Failed" }
        $results += @{ ImageName = $imageConfig.Name; Status = $status; RequestId = $submitResult.RequestId }
    }
    else {
        # Using standard polling
        $waitResult = Wait-ForAdvancedCompletion -RequestId $submitResult.RequestId -ImageName $imageConfig.Name -TimeoutSeconds $imageConfig.Timeout
        
        if ($waitResult.Success) {
            $downloadSuccess = Download-AdvancedImage -RequestId $submitResult.RequestId -OutputPath $outputPath -ImageName $imageConfig.Name
            $status = if ($downloadSuccess) { "Success" } else { "Download Failed" }
            $results += @{ ImageName = $imageConfig.Name; Status = $status; RequestId = $submitResult.RequestId }
        }
        else {
            $results += @{ ImageName = $imageConfig.Name; Status = "Failed"; Error = $waitResult.Error }
        }
    }
    
    # Rate limiting - wait between requests
    if ($processedCount -lt $totalImages) {
        Write-Host "⏸️ Rate limiting: waiting 12 seconds..." -ForegroundColor Cyan
        Start-Sleep -Seconds 12
    }
}

# Generate summary
Write-Host "`n📊 ADVANCED GENERATION SUMMARY" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

$successful = $results | Where-Object { $_.Status -eq "Success" }
$skipped = $results | Where-Object { $_.Status -eq "Skipped" }
$failed = $results | Where-Object { $_.Status -ne "Success" -and $_.Status -ne "Skipped" }

Write-Host "✅ Successfully Generated: $($successful.Count)" -ForegroundColor Green
Write-Host "⏭️ Skipped: $($skipped.Count)" -ForegroundColor Yellow
Write-Host "❌ Failed: $($failed.Count)" -ForegroundColor Red

if ($successful.Count -gt 0) {
    Write-Host "`n🎉 Successfully Generated Images:" -ForegroundColor Green
    foreach ($result in $successful) {
        Write-Host "  ✅ $($result.ImageName)" -ForegroundColor Green
    }
}

if ($failed.Count -gt 0) {
    Write-Host "`n❌ Failed Images:" -ForegroundColor Red
    foreach ($result in $failed) {
        Write-Host "  ❌ $($result.ImageName): $($result.Status)" -ForegroundColor Red
    }
}

# List final directory contents
Write-Host "`n📂 Generated Images Directory:" -ForegroundColor Cyan
Get-ChildItem -Path $OutputDir -Filter "*.jpg" | ForEach-Object {
    $size = [math]::Round($_.Length / 1KB, 2)
    Write-Host "  📸 $($_.Name) - $size KB" -ForegroundColor White
}

Write-Host "`n🌿 Advanced image generation completed!" -ForegroundColor Green
