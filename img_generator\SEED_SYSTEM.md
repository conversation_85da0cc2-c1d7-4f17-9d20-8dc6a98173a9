# Seed Management System

## 🌱 Overview

The Content Generator API includes a sophisticated seed management system that allows for exact reproduction of generated images. Seeds are automatically captured from Together.ai responses and stored for future use.

## 🔄 How Seeds Work

### Automatic Seed Capture
When generating images without specifying a seed:
1. Request sent to Together.ai without seed parameter
2. Together.ai generates a random seed internally
3. Seed is returned in the API response
4. Seed is automatically stored in the database
5. Seed can be used later for exact reproduction

### Seed Reproduction
When generating images with a specific seed:
1. Seed must be from a previous generation
2. Prompt, model, and dimensions must match exactly
3. Together.ai uses the seed to generate identical image
4. Result is pixel-perfect reproduction

## 📊 Database Schema

Seeds are stored in the `requests` table:
```sql
CREATE TABLE requests (
  request_id VARCHAR(20) PRIMARY KEY,
  type VARCHAR(10) NOT NULL,
  prompt TEXT NOT NULL,
  model VARCHAR(100),
  status VARCHAR(20) DEFAULT 'pending',
  seed BIGINT DEFAULT NULL,
  image_params JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP
);
```

## 🎯 Usage Examples

### Generate New Image (Random Seed)
```bash
curl -X POST http://localhost:7777/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "type": "image",
    "prompt": "A red apple on white background",
    "width": 640,
    "height": 800
  }'
```

**Response:**
```json
{
  "success": true,
  "requestId": "1753475448770",
  "message": "Request queued for processing"
}
```

**After Completion:**
```json
{
  "requestId": "1753475448770",
  "status": "completed",
  "seedUsed": 1847392847,
  "imageUrl": "/api/image/1753475448770"
}
```

### Reproduce Exact Image (Using Seed)
```bash
curl -X POST http://localhost:7777/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "type": "image",
    "prompt": "A red apple on white background",
    "width": 640,
    "height": 800,
    "seed": 1847392847
  }'
```

This will generate an identical image to the first request.

## ✅ Seed Validation Rules

### Valid Seeds
- ✅ Seeds from previous Together.ai generations
- ✅ Numeric values (integers)
- ✅ Seeds matching exact prompt and dimensions

### Invalid Seeds
- ❌ Random numbers not from Together.ai
- ❌ Seeds with different prompts
- ❌ Seeds with different dimensions
- ❌ Non-numeric values

## 🔍 Seed Lookup

### Find Available Seeds
Query the database to find seeds for specific parameters:

```sql
SELECT seed, request_id, created_at 
FROM requests 
WHERE type = 'image' 
  AND prompt = 'A red apple on white background'
  AND image_params->>'width' = '640'
  AND image_params->>'height' = '800'
  AND seed IS NOT NULL
  AND status = 'completed'
ORDER BY created_at DESC;
```

### Programmatic Seed Retrieval
```javascript
const { pool } = require('./config/database');

async function findSeedsForPrompt(prompt, width, height) {
  const client = await pool.connect();
  const result = await client.query(`
    SELECT seed, request_id, created_at
    FROM requests 
    WHERE type = 'image' 
      AND prompt = $1
      AND image_params->>'width' = $2
      AND image_params->>'height' = $3
      AND seed IS NOT NULL
      AND status = 'completed'
    ORDER BY created_at DESC
  `, [prompt, width.toString(), height.toString()]);
  
  client.release();
  return result.rows;
}
```

## 📈 Seed Statistics

### Track Seed Usage
Monitor which seeds are most popular:

```sql
-- Most used seeds
SELECT seed, COUNT(*) as usage_count
FROM requests 
WHERE type = 'image' AND seed IS NOT NULL
GROUP BY seed 
ORDER BY usage_count DESC
LIMIT 10;

-- Recent seeds
SELECT seed, prompt, created_at
FROM requests 
WHERE type = 'image' AND seed IS NOT NULL
ORDER BY created_at DESC
LIMIT 20;
```

## 🛠️ Implementation Details

### Seed Storage Process
1. Image generation completes successfully
2. Together.ai response contains seed value
3. Seed extracted from response data
4. Database updated with seed value
5. Seed included in status response

### Code Example
```javascript
// Extract seed from Together.ai response
if (imageData.seed !== undefined && imageData.seed !== null) {
  actualSeed = imageData.seed;
  console.log(`✅ Seed received from Together.ai: ${actualSeed}`);
} else {
  console.log('⚠️ No seed returned by Together.ai');
}

// Update database with seed
await queueManager.updateRequestStatus(
  requestId,
  'completed',
  response.data,
  filePath,
  imageParameters,
  null, // error message
  actualSeed // seed used
);
```

## 🔒 Security Considerations

### Seed Validation
- Only seeds from legitimate Together.ai responses are stored
- Seeds are validated against exact prompt and dimensions
- No arbitrary seed values are accepted

### Data Integrity
- Seeds are stored as BIGINT for proper handling
- Database constraints ensure data consistency
- Proper indexing for efficient seed lookups

## 🧪 Testing Seed System

### Test Seed Capture
```bash
# Generate image and capture seed
node -e "
const axios = require('axios');
axios.post('http://localhost:7777/api/generate', {
  type: 'image',
  prompt: 'Test seed capture',
  width: 512,
  height: 512
}).then(r => console.log('Request ID:', r.data.requestId));
"
```

### Test Seed Reproduction
```bash
# Use captured seed to reproduce image
node -e "
const axios = require('axios');
axios.post('http://localhost:7777/api/generate', {
  type: 'image',
  prompt: 'Test seed capture',
  width: 512,
  height: 512,
  seed: 1847392847
}).then(r => console.log('Reproduction ID:', r.data.requestId));
"
```

## 📋 Best Practices

### For Developers
1. Always check for seed in completed responses
2. Store seeds with their exact parameters
3. Validate seed compatibility before use
4. Handle cases where no seed is returned

### For Users
1. Save seeds from successful generations
2. Use exact same prompt and dimensions
3. Understand that seeds are model-specific
4. Keep seed records for important images

## 🔧 Troubleshooting

### Common Issues

**Seed Not Captured**
- Check if Together.ai returned seed in response
- Verify database connection and permissions
- Check for errors in seed extraction logic

**Reproduction Failed**
- Ensure exact prompt match
- Verify dimensions are identical
- Check that seed is valid numeric value
- Confirm seed exists in database

**Invalid Seed Error**
- Seed must be from previous generation
- Cannot use arbitrary numbers
- Prompt and dimensions must match exactly

---

**Last Updated**: July 26, 2025  
**System Status**: ✅ Fully Operational  
**Test Results**: All seed operations verified working
