// RemoteIt Configuration for Testing
// Update the SERVER_IP with your actual server laptop's IP address

const config = {
  // Server Configuration (update with your server laptop's IP)
  SERVER_IP: 'localhost', // Change this to your server laptop's IP (e.g., '*************')
  
  // API Endpoints
  AUTH_SERVER_URL: 'http://localhost:3000',
  RELAY_SERVER_URL: 'http://localhost:8080',
  RELAY_WEBSOCKET_URL: 'ws://localhost:8081',
  
  // Ports
  AUTH_PORT: 3000,
  RELAY_PORT: 8080,
  WEBSOCKET_PORT: 8081,
  
  // Security
  JWT_SECRET: 'test-secret-key-for-demo',
  
  // Application Settings
  APP_NAME: 'RemoteIt',
  APP_VERSION: '1.0.0',
  
  // Connection Settings
  CONNECTION_TIMEOUT: 30000, // 30 seconds
  HEARTBEAT_INTERVAL: 5000,  // 5 seconds
  
  // Screen Capture Settings
  DEFAULT_QUALITY: 80,
  DEFAULT_FRAME_RATE: 30,
  MAX_FRAME_SIZE: 1920 * 1080,
  
  // File Transfer Settings
  CHUNK_SIZE: 1024 * 1024, // 1MB
  MAX_FILE_SIZE: 10 * 1024 * 1024 * 1024, // 10GB
  
  // Development Settings
  DEBUG: true,
  LOG_LEVEL: 'debug'
};

// Update URLs based on SERVER_IP
if (config.SERVER_IP !== 'localhost') {
  config.AUTH_SERVER_URL = `http://${config.SERVER_IP}:${config.AUTH_PORT}`;
  config.RELAY_SERVER_URL = `http://${config.SERVER_IP}:${config.RELAY_PORT}`;
  config.RELAY_WEBSOCKET_URL = `ws://${config.SERVER_IP}:${config.WEBSOCKET_PORT}`;
}

module.exports = config;
