// RemoteIt Configuration for Testing
// Update the SERVER_IP with your actual server laptop's IP address

const config = {
  // Server Configuration (update with your server laptop's IP)
  SERVER_IP: 'localhost', // Change this to your server laptop's IP (e.g., '*************')

  // Mode: 'local' for same-machine testing, 'network' for LAN testing, 'internet' for cloud
  MODE: process.env.REMOTEIT_MODE || 'local',

  // API Endpoints
  AUTH_SERVER_URL: 'http://localhost:3000',
  RELAY_SERVER_URL: 'http://localhost:8080',
  RELAY_WEBSOCKET_URL: 'ws://localhost:8081',
  
  // Ports
  AUTH_PORT: 3000,
  RELAY_PORT: 8080,
  WEBSOCKET_PORT: 8081,
  
  // Security
  JWT_SECRET: 'test-secret-key-for-demo',
  
  // Application Settings
  APP_NAME: 'RemoteIt',
  APP_VERSION: '1.0.0',
  
  // Connection Settings
  CONNECTION_TIMEOUT: 30000, // 30 seconds
  HEARTBEAT_INTERVAL: 5000,  // 5 seconds
  
  // Screen Capture Settings
  DEFAULT_QUALITY: 80,
  DEFAULT_FRAME_RATE: 30,
  MAX_FRAME_SIZE: 1920 * 1080,
  
  // File Transfer Settings
  CHUNK_SIZE: 1024 * 1024, // 1MB
  MAX_FILE_SIZE: 10 * 1024 * 1024 * 1024, // 10GB
  
  // Development Settings
  DEBUG: true,
  LOG_LEVEL: 'debug'
};

// Update URLs based on MODE and SERVER_IP
if (config.MODE === 'network' || config.SERVER_IP !== 'localhost') {
  const serverIP = config.SERVER_IP !== 'localhost' ? config.SERVER_IP : getLocalIP();
  config.AUTH_SERVER_URL = `http://${serverIP}:${config.AUTH_PORT}`;
  config.RELAY_SERVER_URL = `http://${serverIP}:${config.RELAY_PORT}`;
  config.RELAY_WEBSOCKET_URL = `ws://${serverIP}:${config.WEBSOCKET_PORT}`;
} else if (config.MODE === 'internet') {
  // For internet mode, use cloud endpoints (would need to be deployed)
  config.AUTH_SERVER_URL = 'https://api.remoteit.com';
  config.RELAY_SERVER_URL = 'https://relay.remoteit.com';
  config.RELAY_WEBSOCKET_URL = 'wss://relay.remoteit.com';
}

// Helper function to get local IP address
function getLocalIP() {
  const os = require('os');
  const interfaces = os.networkInterfaces();

  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return 'localhost';
}

module.exports = config;
