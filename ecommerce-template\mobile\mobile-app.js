// Mobile E-commerce App JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initMobileApp();
});

function initMobileApp() {
    initBannerSlider();
    initBottomNavigation();
    initCartDrawer();
    initProductActions();
    initSearch();
    initCountdown();
    initTouchGestures();
}

// Banner Slider
function initBannerSlider() {
    const slides = document.querySelectorAll('.banner-slide');
    const indicators = document.querySelectorAll('.indicator');
    let currentSlide = 0;
    const totalSlides = slides.length;

    function showSlide(index) {
        slides.forEach(slide => slide.classList.remove('active'));
        indicators.forEach(indicator => indicator.classList.remove('active'));
        
        slides[index].classList.add('active');
        indicators[index].classList.add('active');
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % totalSlides;
        showSlide(currentSlide);
    }

    // Auto-slide every 5 seconds
    setInterval(nextSlide, 5000);

    // Manual navigation
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            currentSlide = index;
            showSlide(currentSlide);
        });
    });

    // Touch swipe for banner
    let startX = 0;
    let endX = 0;
    const banner = document.querySelector('.hero-banner');

    banner.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
    });

    banner.addEventListener('touchend', (e) => {
        endX = e.changedTouches[0].clientX;
        handleSwipe();
    });

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = startX - endX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                // Swipe left - next slide
                nextSlide();
            } else {
                // Swipe right - previous slide
                currentSlide = currentSlide === 0 ? totalSlides - 1 : currentSlide - 1;
                showSlide(currentSlide);
            }
        }
    }
}

// Bottom Navigation
function initBottomNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            
            // Remove active class from all items
            navItems.forEach(nav => nav.classList.remove('active'));
            
            // Add active class to clicked item
            item.classList.add('active');
            
            // Handle navigation
            const text = item.querySelector('span').textContent.toLowerCase();
            handleNavigation(text);
        });
    });
}

function handleNavigation(page) {
    switch(page) {
        case 'home':
            scrollToTop();
            break;
        case 'search':
            focusSearch();
            break;
        case 'cart':
            openCartDrawer();
            break;
        case 'wishlist':
            showWishlist();
            break;
        case 'profile':
            showProfile();
            break;
    }
}

// Cart Drawer
function initCartDrawer() {
    const cartBtn = document.querySelector('.nav-item:nth-child(3)');
    const cartDrawer = document.querySelector('.cart-drawer');
    const closeDrawer = document.querySelector('.close-drawer');
    const overlay = document.querySelector('.overlay');
    const checkoutBtn = document.querySelector('.checkout-btn');

    function openCartDrawer() {
        cartDrawer.classList.add('active');
        overlay.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    function closeCartDrawer() {
        cartDrawer.classList.remove('active');
        overlay.classList.remove('active');
        document.body.style.overflow = '';
    }

    cartBtn?.addEventListener('click', (e) => {
        e.preventDefault();
        openCartDrawer();
    });

    closeDrawer?.addEventListener('click', closeCartDrawer);
    overlay?.addEventListener('click', closeCartDrawer);

    checkoutBtn?.addEventListener('click', () => {
        showToast('Proceeding to checkout...', 'success');
        closeCartDrawer();
    });

    // Quantity controls
    initQuantityControls();
    
    // Remove item buttons
    const removeButtons = document.querySelectorAll('.remove-item');
    removeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            removeCartItem(btn);
        });
    });

    // Make openCartDrawer globally accessible
    window.openCartDrawer = openCartDrawer;
}

// Product Actions
function initProductActions() {
    // Wishlist buttons
    const wishlistBtns = document.querySelectorAll('.wishlist-btn');
    wishlistBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            toggleWishlist(btn);
        });
    });

    // Add to cart buttons
    const addToCartBtns = document.querySelectorAll('.add-to-cart');
    addToCartBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            addToCart(btn);
        });
    });

    // Category items
    const categoryItems = document.querySelectorAll('.category-item');
    categoryItems.forEach(item => {
        item.addEventListener('click', () => {
            const category = item.querySelector('span').textContent;
            showToast(`Browsing ${category}...`, 'info');
        });
    });
}

// Search Functionality
function initSearch() {
    const searchInput = document.querySelector('.search-bar input');
    const filterBtn = document.querySelector('.filter-btn');

    searchInput?.addEventListener('input', debounce(handleSearch, 300));
    
    searchInput?.addEventListener('focus', () => {
        searchInput.parentElement.style.borderColor = '#6366f1';
    });

    searchInput?.addEventListener('blur', () => {
        searchInput.parentElement.style.borderColor = 'transparent';
    });

    filterBtn?.addEventListener('click', () => {
        showToast('Filter options coming soon!', 'info');
    });
}

function focusSearch() {
    const searchInput = document.querySelector('.search-bar input');
    searchInput?.focus();
    scrollToTop();
}

// Countdown Timer
function initCountdown() {
    const timeUnits = document.querySelectorAll('.time-unit');
    if (timeUnits.length === 0) return;

    let hours = 2;
    let minutes = 45;
    let seconds = 30;

    function updateCountdown() {
        if (seconds > 0) {
            seconds--;
        } else if (minutes > 0) {
            minutes--;
            seconds = 59;
        } else if (hours > 0) {
            hours--;
            minutes = 59;
            seconds = 59;
        } else {
            // Timer ended
            showToast('Flash sale ended!', 'info');
            return;
        }

        timeUnits[0].textContent = hours.toString().padStart(2, '0');
        timeUnits[1].textContent = minutes.toString().padStart(2, '0');
        timeUnits[2].textContent = seconds.toString().padStart(2, '0');
    }

    // Update every second
    setInterval(updateCountdown, 1000);
}

// Touch Gestures
function initTouchGestures() {
    // Pull to refresh
    let startY = 0;
    let currentY = 0;
    let pullDistance = 0;
    const pullThreshold = 100;

    document.addEventListener('touchstart', (e) => {
        if (window.scrollY === 0) {
            startY = e.touches[0].clientY;
        }
    });

    document.addEventListener('touchmove', (e) => {
        if (window.scrollY === 0 && startY > 0) {
            currentY = e.touches[0].clientY;
            pullDistance = currentY - startY;
            
            if (pullDistance > 0 && pullDistance < pullThreshold) {
                // Visual feedback for pull to refresh
                document.body.style.transform = `translateY(${pullDistance * 0.5}px)`;
            }
        }
    });

    document.addEventListener('touchend', () => {
        if (pullDistance > pullThreshold) {
            showToast('Refreshing...', 'info');
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
        
        document.body.style.transform = '';
        startY = 0;
        pullDistance = 0;
    });
}

// Helper Functions
function toggleWishlist(btn) {
    const icon = btn.querySelector('i');
    const isActive = btn.classList.contains('active');
    
    if (isActive) {
        btn.classList.remove('active');
        icon.classList.remove('fas');
        icon.classList.add('far');
        showToast('Removed from wishlist', 'info');
        updateWishlistBadge(-1);
    } else {
        btn.classList.add('active');
        icon.classList.remove('far');
        icon.classList.add('fas');
        showToast('Added to wishlist', 'success');
        updateWishlistBadge(1);
    }
}

function addToCart(btn) {
    // Animation
    btn.style.transform = 'scale(0.9)';
    setTimeout(() => {
        btn.style.transform = 'scale(1)';
    }, 150);

    showToast('Added to cart!', 'success');
    updateCartBadge(1);
}

function removeCartItem(btn) {
    const cartItem = btn.closest('.cart-item');
    cartItem.style.animation = 'slideOut 0.3s ease';
    
    setTimeout(() => {
        cartItem.remove();
        updateCartTotal();
        updateCartBadge(-1);
        showToast('Item removed', 'info');
    }, 300);
}

function initQuantityControls() {
    const quantityControls = document.querySelectorAll('.quantity-controls');
    
    quantityControls.forEach(control => {
        const minusBtn = control.querySelector('.qty-btn:first-child');
        const plusBtn = control.querySelector('.qty-btn:last-child');
        const quantitySpan = control.querySelector('.quantity');

        minusBtn?.addEventListener('click', () => {
            let quantity = parseInt(quantitySpan.textContent);
            if (quantity > 1) {
                quantitySpan.textContent = quantity - 1;
                updateCartTotal();
            }
        });

        plusBtn?.addEventListener('click', () => {
            let quantity = parseInt(quantitySpan.textContent);
            quantitySpan.textContent = quantity + 1;
            updateCartTotal();
        });
    });
}

function updateCartTotal() {
    const cartItems = document.querySelectorAll('.cart-item');
    let total = 0;

    cartItems.forEach(item => {
        const priceText = item.querySelector('.item-price').textContent;
        const price = parseFloat(priceText.replace('$', ''));
        const quantity = parseInt(item.querySelector('.quantity').textContent);
        total += price * quantity;
    });

    const totalElement = document.querySelector('.summary-row.total span:last-child');
    if (totalElement) {
        totalElement.textContent = `$${total.toFixed(2)}`;
    }
}

function updateCartBadge(change) {
    const cartBadge = document.querySelector('.nav-item:nth-child(3) .nav-badge');
    if (cartBadge) {
        let count = parseInt(cartBadge.textContent) || 0;
        count = Math.max(0, count + change);
        cartBadge.textContent = count;
        cartBadge.style.display = count > 0 ? 'flex' : 'none';
    }
}

function updateWishlistBadge(change) {
    const wishlistBadge = document.querySelector('.nav-item:nth-child(4) .nav-badge');
    if (wishlistBadge) {
        let count = parseInt(wishlistBadge.textContent) || 0;
        count = Math.max(0, count + change);
        wishlistBadge.textContent = count;
        wishlistBadge.style.display = count > 0 ? 'flex' : 'none';
    }
}

function handleSearch(query) {
    if (query.length < 2) return;
    showToast(`Searching for "${query}"...`, 'info');
}

function showWishlist() {
    showToast('Wishlist feature coming soon!', 'info');
}

function showProfile() {
    showToast('Profile page coming soon!', 'info');
}

function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // Style the toast
    Object.assign(toast.style, {
        position: 'fixed',
        top: '100px',
        left: '50%',
        transform: 'translateX(-50%)',
        background: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#6366f1',
        color: 'white',
        padding: '12px 20px',
        borderRadius: '25px',
        fontSize: '14px',
        fontWeight: '500',
        zIndex: '9999',
        opacity: '0',
        transition: 'opacity 0.3s ease'
    });

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.opacity = '1';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add CSS for slide out animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOut {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }
`;
document.head.appendChild(style);
