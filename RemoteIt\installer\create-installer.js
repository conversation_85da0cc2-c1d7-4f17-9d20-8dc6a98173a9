#!/usr/bin/env node

// RemoteIt Installer Creator
// Creates simple one-click installers for all platforms

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 RemoteIt Installer Creator\n');

const platforms = {
  windows: {
    name: 'Windows',
    extension: '.exe',
    icon: 'assets/icon.ico',
    target: 'nsis'
  },
  macos: {
    name: 'macOS',
    extension: '.dmg',
    icon: 'assets/icon.icns',
    target: 'dmg'
  },
  linux: {
    name: 'Linux',
    extension: '.AppImage',
    icon: 'assets/icon.png',
    target: 'AppImage'
  }
};

// Enhanced electron-builder configuration
const builderConfig = {
  appId: 'com.remoteit.app',
  productName: 'RemoteIt',
  copyright: 'Copyright © 2024 RemoteIt',
  
  directories: {
    output: 'dist',
    buildResources: 'build'
  },
  
  files: [
    'src/**/*',
    'node_modules/**/*',
    'package.json',
    'config.js'
  ],
  
  extraResources: [
    {
      from: 'server',
      to: 'server',
      filter: ['**/*']
    }
  ],
  
  // Windows configuration
  win: {
    target: [
      {
        target: 'nsis',
        arch: ['x64', 'ia32']
      },
      {
        target: 'portable',
        arch: ['x64']
      }
    ],
    icon: 'build/icon.ico',
    publisherName: 'RemoteIt Inc.',
    verifyUpdateCodeSignature: false
  },
  
  nsis: {
    oneClick: false,
    allowToChangeInstallationDirectory: true,
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: 'RemoteIt',
    include: 'installer/windows/installer.nsh',
    installerIcon: 'build/icon.ico',
    uninstallerIcon: 'build/icon.ico',
    installerHeaderIcon: 'build/icon.ico',
    deleteAppDataOnUninstall: true,
    runAfterFinish: true,
    menuCategory: 'RemoteIt',
    allowElevation: true
  },
  
  // macOS configuration
  mac: {
    target: [
      {
        target: 'dmg',
        arch: ['x64', 'arm64']
      }
    ],
    icon: 'build/icon.icns',
    category: 'public.app-category.productivity',
    hardenedRuntime: true,
    gatekeeperAssess: false,
    entitlements: 'build/entitlements.mac.plist',
    entitlementsInherit: 'build/entitlements.mac.plist'
  },
  
  dmg: {
    title: 'RemoteIt ${version}',
    icon: 'build/icon.icns',
    background: 'build/dmg-background.png',
    contents: [
      {
        x: 130,
        y: 220
      },
      {
        x: 410,
        y: 220,
        type: 'link',
        path: '/Applications'
      }
    ],
    window: {
      width: 540,
      height: 380
    }
  },
  
  // Linux configuration
  linux: {
    target: [
      {
        target: 'AppImage',
        arch: ['x64']
      },
      {
        target: 'deb',
        arch: ['x64']
      },
      {
        target: 'rpm',
        arch: ['x64']
      }
    ],
    icon: 'build/icon.png',
    category: 'Network',
    desktop: {
      Name: 'RemoteIt',
      Comment: 'Secure Remote Desktop Access',
      Keywords: 'remote;desktop;vnc;teamviewer;',
      StartupWMClass: 'RemoteIt'
    }
  },
  
  // Auto-updater configuration
  publish: {
    provider: 'github',
    owner: 'remoteit',
    repo: 'remoteit-desktop'
  },
  
  // Compression
  compression: 'maximum',
  
  // Code signing (for production)
  afterSign: 'scripts/notarize.js'
};

// Create installer assets
function createInstallerAssets() {
  console.log('📁 Creating installer assets...');
  
  const buildDir = path.join(__dirname, '..', 'build');
  
  if (!fs.existsSync(buildDir)) {
    fs.mkdirSync(buildDir, { recursive: true });
  }
  
  // Create Windows installer script
  const windowsInstallerScript = `
; RemoteIt Windows Installer Script

!define PRODUCT_NAME "RemoteIt"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "RemoteIt Inc."
!define PRODUCT_WEB_SITE "https://remoteit.com"

; Modern UI
!include "MUI2.nsh"

; Request application privileges
RequestExecutionLevel admin

; Installer pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; Languages
!insertmacro MUI_LANGUAGE "English"

; Installer sections
Section "Main Application" SecMain
  SetOutPath "$INSTDIR"
  
  ; Install files
  File /r "\${BUILD_RESOURCES_DIR}\\*"
  
  ; Create shortcuts
  CreateDirectory "$SMPROGRAMS\\RemoteIt"
  CreateShortCut "$SMPROGRAMS\\RemoteIt\\RemoteIt.lnk" "$INSTDIR\\RemoteIt.exe"
  CreateShortCut "$DESKTOP\\RemoteIt.lnk" "$INSTDIR\\RemoteIt.exe"
  
  ; Register uninstaller
  WriteUninstaller "$INSTDIR\\Uninstall.exe"
  
  ; Add to Add/Remove Programs
  WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\RemoteIt" "DisplayName" "RemoteIt"
  WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\RemoteIt" "UninstallString" "$INSTDIR\\Uninstall.exe"
  WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\RemoteIt" "Publisher" "RemoteIt Inc."
  WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\RemoteIt" "DisplayVersion" "1.0.0"
  
  ; Start service
  ExecWait '"$INSTDIR\\RemoteIt.exe" --install-service'
SectionEnd

; Uninstaller section
Section "Uninstall"
  ; Stop service
  ExecWait '"$INSTDIR\\RemoteIt.exe" --uninstall-service'
  
  ; Remove files
  RMDir /r "$INSTDIR"
  
  ; Remove shortcuts
  Delete "$DESKTOP\\RemoteIt.lnk"
  RMDir /r "$SMPROGRAMS\\RemoteIt"
  
  ; Remove registry entries
  DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\RemoteIt"
SectionEnd
`;
  
  fs.writeFileSync(path.join(buildDir, 'installer.nsh'), windowsInstallerScript);
  
  // Create macOS entitlements
  const macEntitlements = `
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>com.apple.security.cs.allow-jit</key>
  <true/>
  <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
  <true/>
  <key>com.apple.security.cs.disable-library-validation</key>
  <true/>
  <key>com.apple.security.device.audio-input</key>
  <true/>
  <key>com.apple.security.device.camera</key>
  <true/>
  <key>com.apple.security.network.client</key>
  <true/>
  <key>com.apple.security.network.server</key>
  <true/>
  <key>com.apple.security.files.user-selected.read-write</key>
  <true/>
  <key>com.apple.security.automation.apple-events</key>
  <true/>
</dict>
</plist>
`;
  
  fs.writeFileSync(path.join(buildDir, 'entitlements.mac.plist'), macEntitlements);
  
  console.log('✅ Installer assets created');
}

// Build for specific platform
async function buildPlatform(platform) {
  console.log(`🔨 Building for ${platforms[platform].name}...`);
  
  try {
    const command = `npx electron-builder --${platform} --publish=never`;
    execSync(command, { stdio: 'inherit' });
    
    console.log(`✅ ${platforms[platform].name} build completed`);
    
  } catch (error) {
    console.error(`❌ ${platforms[platform].name} build failed:`, error.message);
    throw error;
  }
}

// Create portable version
function createPortableVersion() {
  console.log('📦 Creating portable version...');
  
  const portableScript = `
@echo off
title RemoteIt Portable
echo Starting RemoteIt in portable mode...

REM Set portable mode
set REMOTEIT_PORTABLE=1
set REMOTEIT_DATA_DIR=%~dp0data

REM Create data directory
if not exist "%REMOTEIT_DATA_DIR%" mkdir "%REMOTEIT_DATA_DIR%"

REM Start application
start "" "%~dp0RemoteIt.exe" --portable

echo RemoteIt started successfully!
pause
`;
  
  const distDir = path.join(__dirname, '..', 'dist');
  fs.writeFileSync(path.join(distDir, 'RemoteIt-Portable.bat'), portableScript);
  
  console.log('✅ Portable version created');
}

// Main build process
async function main() {
  const args = process.argv.slice(2);
  const targetPlatform = args[0];
  
  try {
    // Update package.json with builder config
    const packagePath = path.join(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    packageJson.build = builderConfig;
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
    
    // Create installer assets
    createInstallerAssets();
    
    if (targetPlatform && platforms[targetPlatform]) {
      // Build specific platform
      await buildPlatform(targetPlatform);
    } else if (targetPlatform === 'all') {
      // Build all platforms
      for (const platform of Object.keys(platforms)) {
        await buildPlatform(platform);
      }
    } else {
      // Build current platform
      const currentPlatform = process.platform === 'win32' ? 'windows' : 
                             process.platform === 'darwin' ? 'macos' : 'linux';
      await buildPlatform(currentPlatform);
    }
    
    // Create portable version for Windows
    if (!targetPlatform || targetPlatform === 'windows' || targetPlatform === 'all') {
      createPortableVersion();
    }
    
    console.log('\n🎉 Installer creation completed!');
    console.log('\n📁 Output files:');
    
    const distDir = path.join(__dirname, '..', 'dist');
    if (fs.existsSync(distDir)) {
      const files = fs.readdirSync(distDir);
      files.forEach(file => {
        const filePath = path.join(distDir, file);
        const stats = fs.statSync(filePath);
        const size = (stats.size / 1024 / 1024).toFixed(2);
        console.log(`   ${file} (${size} MB)`);
      });
    }
    
    console.log('\n💡 Usage Instructions:');
    console.log('   1. Distribute the installer files to users');
    console.log('   2. Users run the installer (no admin rights needed for portable)');
    console.log('   3. Application starts automatically after installation');
    console.log('   4. Users see their unique Device ID and can connect');
    
  } catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
  }
}

// Show usage if no arguments
if (process.argv.length === 2) {
  console.log('Usage: node create-installer.js [platform]');
  console.log('');
  console.log('Platforms:');
  console.log('  windows  - Build Windows installer (.exe)');
  console.log('  macos    - Build macOS installer (.dmg)');
  console.log('  linux    - Build Linux installer (.AppImage)');
  console.log('  all      - Build for all platforms');
  console.log('');
  console.log('Examples:');
  console.log('  node create-installer.js windows');
  console.log('  node create-installer.js all');
  console.log('');
  process.exit(0);
}

main();
