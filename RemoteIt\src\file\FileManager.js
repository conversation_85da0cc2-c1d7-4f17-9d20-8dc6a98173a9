const { EventEmitter } = require('events');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const archiver = require('archiver');
const unzipper = require('unzipper');
const mime = require('mime-types');
const Logger = require('../utils/Logger');

class FileManager extends EventEmitter {
  constructor() {
    super();
    this.logger = new Logger('file-manager');
    this.activeTransfers = new Map();
    this.transferQueue = [];
    this.maxConcurrentTransfers = 3;
    this.chunkSize = 1024 * 1024; // 1MB chunks
    this.maxFileSize = 10 * 1024 * 1024 * 1024; // 10GB limit
    
    // Transfer statistics
    this.stats = {
      totalTransfers: 0,
      successfulTransfers: 0,
      failedTransfers: 0,
      bytesTransferred: 0,
      averageSpeed: 0
    };
  }

  async initialize() {
    this.logger.info('Initializing file manager...');
    
    try {
      // Create temp directory for file transfers
      this.tempDir = path.join(require('os').tmpdir(), 'remoteit-transfers');
      await this.ensureDirectory(this.tempDir);
      
      // Start transfer processor
      this.startTransferProcessor();
      
      this.logger.info('File manager initialized successfully');
      
    } catch (error) {
      this.logger.error('Failed to initialize file manager:', error.message);
      throw error;
    }
  }

  async browse(remotePath = '') {
    try {
      this.logger.debug('Browsing path:', remotePath);
      
      const targetPath = remotePath || require('os').homedir();
      const stats = await fs.stat(targetPath);
      
      if (stats.isDirectory()) {
        const items = await fs.readdir(targetPath, { withFileTypes: true });
        
        const result = {
          path: targetPath,
          parent: path.dirname(targetPath),
          items: await Promise.all(items.map(async (item) => {
            const itemPath = path.join(targetPath, item.name);
            const itemStats = await fs.stat(itemPath).catch(() => null);
            
            return {
              name: item.name,
              path: itemPath,
              type: item.isDirectory() ? 'directory' : 'file',
              size: itemStats ? itemStats.size : 0,
              modified: itemStats ? itemStats.mtime : null,
              permissions: itemStats ? this.getPermissions(itemStats) : null,
              mimeType: item.isFile() ? mime.lookup(item.name) || 'application/octet-stream' : null
            };
          }))
        };
        
        return result;
        
      } else {
        throw new Error('Path is not a directory');
      }
      
    } catch (error) {
      this.logger.error('Browse failed:', error.message);
      throw error;
    }
  }

  async transfer(transferInfo) {
    try {
      const transferId = crypto.randomUUID();
      
      const transfer = {
        id: transferId,
        type: transferInfo.type, // 'upload' or 'download'
        source: transferInfo.source,
        destination: transferInfo.destination,
        files: transferInfo.files || [],
        sessionId: transferInfo.sessionId,
        priority: transferInfo.priority || 'normal',
        compress: transferInfo.compress || false,
        encrypt: transferInfo.encrypt !== false, // Default to true
        status: 'queued',
        progress: 0,
        bytesTransferred: 0,
        totalBytes: 0,
        speed: 0,
        startTime: null,
        endTime: null,
        error: null
      };

      // Calculate total size
      transfer.totalBytes = await this.calculateTotalSize(transfer.files);
      
      // Validate file size
      if (transfer.totalBytes > this.maxFileSize) {
        throw new Error(`Transfer size exceeds maximum limit of ${this.formatBytes(this.maxFileSize)}`);
      }

      this.activeTransfers.set(transferId, transfer);
      this.transferQueue.push(transfer);
      
      this.logger.info('Transfer queued:', transferId);
      this.emit('transferQueued', transfer);
      
      return transferId;
      
    } catch (error) {
      this.logger.error('Transfer setup failed:', error.message);
      throw error;
    }
  }

  async cancelTransfer(transferId) {
    const transfer = this.activeTransfers.get(transferId);
    
    if (transfer) {
      transfer.status = 'cancelled';
      this.activeTransfers.delete(transferId);
      
      // Remove from queue if not started
      const queueIndex = this.transferQueue.findIndex(t => t.id === transferId);
      if (queueIndex !== -1) {
        this.transferQueue.splice(queueIndex, 1);
      }
      
      this.logger.info('Transfer cancelled:', transferId);
      this.emit('transferCancelled', transfer);
      
      return true;
    }
    
    return false;
  }

  async pauseTransfer(transferId) {
    const transfer = this.activeTransfers.get(transferId);
    
    if (transfer && transfer.status === 'active') {
      transfer.status = 'paused';
      this.logger.info('Transfer paused:', transferId);
      this.emit('transferPaused', transfer);
      return true;
    }
    
    return false;
  }

  async resumeTransfer(transferId) {
    const transfer = this.activeTransfers.get(transferId);
    
    if (transfer && transfer.status === 'paused') {
      transfer.status = 'queued';
      this.transferQueue.push(transfer);
      this.logger.info('Transfer resumed:', transferId);
      this.emit('transferResumed', transfer);
      return true;
    }
    
    return false;
  }

  startTransferProcessor() {
    setInterval(() => {
      this.processTransferQueue();
    }, 1000);
  }

  async processTransferQueue() {
    const activeCount = Array.from(this.activeTransfers.values())
      .filter(t => t.status === 'active').length;
    
    if (activeCount >= this.maxConcurrentTransfers || this.transferQueue.length === 0) {
      return;
    }

    const transfer = this.transferQueue.shift();
    if (transfer && transfer.status === 'queued') {
      this.executeTransfer(transfer);
    }
  }

  async executeTransfer(transfer) {
    try {
      transfer.status = 'active';
      transfer.startTime = Date.now();
      
      this.logger.info('Starting transfer:', transfer.id);
      this.emit('transferStarted', transfer);

      if (transfer.type === 'upload') {
        await this.executeUpload(transfer);
      } else {
        await this.executeDownload(transfer);
      }

      transfer.status = 'completed';
      transfer.endTime = Date.now();
      transfer.progress = 100;
      
      this.stats.successfulTransfers++;
      this.stats.bytesTransferred += transfer.bytesTransferred;
      
      this.logger.info('Transfer completed:', transfer.id);
      this.emit('transferCompleted', transfer);
      
    } catch (error) {
      transfer.status = 'failed';
      transfer.error = error.message;
      transfer.endTime = Date.now();
      
      this.stats.failedTransfers++;
      
      this.logger.error('Transfer failed:', transfer.id, error.message);
      this.emit('transferFailed', transfer);
    }

    this.stats.totalTransfers++;
  }

  async executeUpload(transfer) {
    const { files, destination, compress, encrypt } = transfer;
    
    for (const file of files) {
      if (transfer.status !== 'active') break;
      
      await this.uploadFile(transfer, file, destination, { compress, encrypt });
    }
  }

  async executeDownload(transfer) {
    const { files, destination, compress, encrypt } = transfer;
    
    for (const file of files) {
      if (transfer.status !== 'active') break;
      
      await this.downloadFile(transfer, file, destination, { compress, encrypt });
    }
  }

  async uploadFile(transfer, filePath, destination, options = {}) {
    const fileName = path.basename(filePath);
    const fileStats = await fs.stat(filePath);
    const fileSize = fileStats.size;
    
    this.logger.debug('Uploading file:', fileName, 'Size:', this.formatBytes(fileSize));
    
    // Read file in chunks
    const fileHandle = await fs.open(filePath, 'r');
    let bytesRead = 0;
    
    try {
      while (bytesRead < fileSize && transfer.status === 'active') {
        const chunkSize = Math.min(this.chunkSize, fileSize - bytesRead);
        const buffer = Buffer.alloc(chunkSize);
        
        const { bytesRead: chunkBytesRead } = await fileHandle.read(buffer, 0, chunkSize, bytesRead);
        
        if (chunkBytesRead === 0) break;
        
        // Encrypt chunk if required
        let chunkData = buffer.slice(0, chunkBytesRead);
        if (options.encrypt) {
          chunkData = this.encryptChunk(chunkData, transfer.id);
        }
        
        // Send chunk via connection manager
        await this.sendFileChunk(transfer.sessionId, {
          transferId: transfer.id,
          fileName: fileName,
          chunkIndex: Math.floor(bytesRead / this.chunkSize),
          data: chunkData,
          isLast: bytesRead + chunkBytesRead >= fileSize
        });
        
        bytesRead += chunkBytesRead;
        transfer.bytesTransferred += chunkBytesRead;
        transfer.progress = (transfer.bytesTransferred / transfer.totalBytes) * 100;
        transfer.speed = this.calculateSpeed(transfer);
        
        this.emit('transferProgress', transfer);
        
        // Small delay to prevent overwhelming the connection
        await this.sleep(10);
      }
      
    } finally {
      await fileHandle.close();
    }
  }

  async downloadFile(transfer, fileInfo, destination, options = {}) {
    const fileName = fileInfo.name;
    const filePath = path.join(destination, fileName);
    
    this.logger.debug('Downloading file:', fileName);
    
    // Request file from remote
    await this.requestFile(transfer.sessionId, {
      transferId: transfer.id,
      filePath: fileInfo.path,
      encrypt: options.encrypt
    });
    
    // File chunks will be received via connection manager events
    // This is handled in the connection manager's peer data handler
  }

  async receiveFileChunk(transferId, chunkData) {
    const transfer = this.activeTransfers.get(transferId);
    if (!transfer) return;

    try {
      const { fileName, chunkIndex, data, isLast } = chunkData;
      
      // Decrypt chunk if encrypted
      let chunkBuffer = data;
      if (transfer.encrypt) {
        chunkBuffer = this.decryptChunk(data, transferId);
      }
      
      // Write chunk to temporary file
      const tempFilePath = path.join(this.tempDir, `${transferId}_${fileName}`);
      await fs.appendFile(tempFilePath, chunkBuffer);
      
      transfer.bytesTransferred += chunkBuffer.length;
      transfer.progress = (transfer.bytesTransferred / transfer.totalBytes) * 100;
      transfer.speed = this.calculateSpeed(transfer);
      
      this.emit('transferProgress', transfer);
      
      if (isLast) {
        // Move file to final destination
        const finalPath = path.join(transfer.destination, fileName);
        await fs.rename(tempFilePath, finalPath);
        
        this.logger.debug('File download completed:', fileName);
      }
      
    } catch (error) {
      this.logger.error('Failed to receive file chunk:', error.message);
      throw error;
    }
  }

  async sendFileChunk(sessionId, chunkData) {
    // This would be implemented by the connection manager
    const connectionManager = require('../connection/ConnectionManager');
    await connectionManager.sendPeerData(sessionId, {
      type: 'file-chunk',
      data: chunkData
    });
  }

  async requestFile(sessionId, fileRequest) {
    // This would be implemented by the connection manager
    const connectionManager = require('../connection/ConnectionManager');
    await connectionManager.sendPeerMessage(sessionId, {
      type: 'file-request',
      data: fileRequest
    });
  }

  encryptChunk(data, transferId) {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(transferId, 'salt', 32);
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipher(algorithm, key);
    cipher.setAAD(Buffer.from(transferId));
    
    let encrypted = cipher.update(data);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    
    const authTag = cipher.getAuthTag();
    
    return {
      iv: iv.toString('hex'),
      encrypted: encrypted.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }

  decryptChunk(encryptedData, transferId) {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(transferId, 'salt', 32);
    
    const decipher = crypto.createDecipher(algorithm, key);
    decipher.setAAD(Buffer.from(transferId));
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    
    let decrypted = decipher.update(Buffer.from(encryptedData.encrypted, 'hex'));
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    
    return decrypted;
  }

  async calculateTotalSize(files) {
    let totalSize = 0;
    
    for (const file of files) {
      try {
        const stats = await fs.stat(file);
        if (stats.isFile()) {
          totalSize += stats.size;
        } else if (stats.isDirectory()) {
          totalSize += await this.getDirectorySize(file);
        }
      } catch (error) {
        this.logger.warn('Failed to get file size:', file, error.message);
      }
    }
    
    return totalSize;
  }

  async getDirectorySize(dirPath) {
    let totalSize = 0;
    
    try {
      const items = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item.name);
        
        if (item.isFile()) {
          const stats = await fs.stat(itemPath);
          totalSize += stats.size;
        } else if (item.isDirectory()) {
          totalSize += await this.getDirectorySize(itemPath);
        }
      }
    } catch (error) {
      this.logger.warn('Failed to calculate directory size:', dirPath, error.message);
    }
    
    return totalSize;
  }

  calculateSpeed(transfer) {
    if (!transfer.startTime) return 0;
    
    const elapsed = (Date.now() - transfer.startTime) / 1000; // seconds
    return transfer.bytesTransferred / elapsed; // bytes per second
  }

  getPermissions(stats) {
    const mode = stats.mode;
    const permissions = {
      readable: !!(mode & parseInt('400', 8)),
      writable: !!(mode & parseInt('200', 8)),
      executable: !!(mode & parseInt('100', 8))
    };
    
    return permissions;
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async ensureDirectory(dirPath) {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getTransfer(transferId) {
    return this.activeTransfers.get(transferId);
  }

  getAllTransfers() {
    return Array.from(this.activeTransfers.values());
  }

  getStats() {
    return {
      ...this.stats,
      activeTransfers: this.activeTransfers.size,
      queuedTransfers: this.transferQueue.length,
      averageSpeed: this.stats.bytesTransferred / Math.max(this.stats.totalTransfers, 1)
    };
  }

  cleanup() {
    this.logger.info('Cleaning up file manager...');
    
    // Cancel all active transfers
    for (const [transferId] of this.activeTransfers) {
      this.cancelTransfer(transferId);
    }
    
    // Clear temp directory
    this.cleanupTempFiles();
    
    this.removeAllListeners();
    this.logger.info('File manager cleanup complete');
  }

  async cleanupTempFiles() {
    try {
      const files = await fs.readdir(this.tempDir);
      
      for (const file of files) {
        const filePath = path.join(this.tempDir, file);
        await fs.unlink(filePath);
      }
      
      this.logger.info('Temporary files cleaned up');
      
    } catch (error) {
      this.logger.warn('Failed to cleanup temp files:', error.message);
    }
  }
}

module.exports = FileManager;
