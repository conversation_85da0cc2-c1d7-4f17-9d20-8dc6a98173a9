# 🚀 Generate Single Image with ALL New Features
param(
    [string]$ImageName = "test-image",
    [string]$Filename = "test-image.jpg",
    [string]$Prompt = "Modern green technology innovation",
    [int]$Width = 1024,
    [int]$Height = 768,
    [int]$Seed = $null,
    [int]$Timeout = 240,
    [string]$Priority = "high",
    [switch]$UseWaitEndpoint = $false
)

Write-Host "🚀 ADVANCED IMAGE GENERATION - NEW FEATURES DEMO" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

Write-Host "🎯 Configuration:" -ForegroundColor Cyan
Write-Host "  📝 Image Name: $ImageName" -ForegroundColor White
Write-Host "  📁 Filename: $Filename" -ForegroundColor White
Write-Host "  📐 Dimensions: ${Width}x${Height}" -ForegroundColor White
Write-Host "  🎲 Seed: $(if ($Seed) { $Seed } else { 'Auto-generated' })" -ForegroundColor White
Write-Host "  ⏱️ Timeout: ${Timeout}s" -ForegroundColor White
Write-Host "  🚀 Priority: $Priority" -ForegroundColor White
Write-Host "  ⚡ Endpoint: $(if ($UseWaitEndpoint) { 'generate-and-wait' } else { 'generate + polling' })" -ForegroundColor White
Write-Host "  📝 Prompt: $Prompt" -ForegroundColor Gray

# Check service health
Write-Host "`n🏥 Checking service health..." -ForegroundColor Cyan
try {
    $health = Invoke-RestMethod -Uri "http://localhost:7777/health" -TimeoutSec 5
    if ($health.status -eq "healthy") {
        Write-Host "✅ Service is healthy!" -ForegroundColor Green
        Write-Host "  📊 Database: $($health.database)" -ForegroundColor White
        Write-Host "  🔧 Services: $($health.services)" -ForegroundColor White
    } else {
        Write-Host "❌ Service is not healthy" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Cannot connect to service" -ForegroundColor Red
    exit 1
}

# Prepare request body with all new parameters
$body = @{
    type = "image"
    prompt = $Prompt
    width = $Width
    height = $Height
    timeout = $Timeout
    priority = $Priority
}

# Add seed if specified
if ($Seed -ne $null) {
    $body.seed = $Seed
}

$jsonBody = $body | ConvertTo-Json -Depth 3

Write-Host "`n📤 Request Body:" -ForegroundColor Cyan
Write-Host $jsonBody -ForegroundColor Gray

# Choose endpoint
$endpoint = if ($UseWaitEndpoint) { "/api/generate-and-wait" } else { "/api/generate" }
Write-Host "`n🚀 Using endpoint: $endpoint" -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri "http://localhost:7777$endpoint" -Method POST -Body $jsonBody -ContentType "application/json"
    
    if ($response.success) {
        Write-Host "✅ Request submitted successfully!" -ForegroundColor Green
        Write-Host "  🆔 Request ID: $($response.requestId)" -ForegroundColor White
        Write-Host "  🤖 Model: $($response.model)" -ForegroundColor White
        Write-Host "  ⏱️ Estimated Time: $($response.estimatedTime)" -ForegroundColor White
        
        # Display image parameters if available
        if ($response.imageParameters) {
            Write-Host "  🎨 Image Parameters:" -ForegroundColor Cyan
            Write-Host "    📐 Width: $($response.imageParameters.width)" -ForegroundColor White
            Write-Host "    📐 Height: $($response.imageParameters.height)" -ForegroundColor White
            Write-Host "    🎲 Seed: $($response.imageParameters.seed)" -ForegroundColor White
        }
        
        # Display generation parameters if available
        if ($response.generationParameters) {
            Write-Host "  ⚙️ Generation Parameters:" -ForegroundColor Cyan
            Write-Host "    ⏱️ Timeout: $($response.generationParameters.timeout)s" -ForegroundColor White
            Write-Host "    🚀 Priority: $($response.generationParameters.priority)" -ForegroundColor White
            Write-Host "    ⏰ Max Wait: $($response.generationParameters.maxWaitTime)" -ForegroundColor White
        }
        
        # Display rate limit info if available
        if ($response.rateLimitInfo) {
            Write-Host "  📊 Rate Limit Info:" -ForegroundColor Yellow
            Write-Host "    🔢 Current: $($response.rateLimitInfo.currentRequests)/$($response.rateLimitInfo.maxRequests)" -ForegroundColor White
            Write-Host "    🔄 Reset: $($response.rateLimitInfo.resetTime)" -ForegroundColor White
            Write-Host "    ⏸️ Wait: $($response.rateLimitInfo.waitTimeMs)ms" -ForegroundColor White
        }
        
        $requestId = $response.requestId
        
        if ($UseWaitEndpoint) {
            # generate-and-wait returns completed result
            Write-Host "`n🎉 Generation completed via generate-and-wait!" -ForegroundColor Green
            
            if ($response.status -eq "completed") {
                Write-Host "  📁 File Path: $($response.filePath)" -ForegroundColor White
                Write-Host "  🌐 Image URL: $($response.imageUrl)" -ForegroundColor White
                Write-Host "  📅 Created: $($response.createdAt)" -ForegroundColor White
                Write-Host "  ✅ Completed: $($response.completedAt)" -ForegroundColor White
                
                # Download image
                $outputPath = "images\$Filename"
                Write-Host "`n📥 Downloading image..." -ForegroundColor Cyan
                Invoke-WebRequest -Uri "http://localhost:7777/api/image/$requestId" -OutFile $outputPath
                
                if (Test-Path $outputPath) {
                    $fileSize = (Get-Item $outputPath).Length
                    Write-Host "✅ Downloaded successfully! Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
                } else {
                    Write-Host "❌ Download failed!" -ForegroundColor Red
                }
            }
        } else {
            # Standard polling approach
            Write-Host "`n⏳ Polling for completion..." -ForegroundColor Yellow
            
            $maxAttempts = [math]::Ceiling($Timeout / 15) + 5  # Based on timeout + buffer
            $attempt = 0
            $completed = $false
            
            while ($attempt -lt $maxAttempts -and !$completed) {
                $attempt++
                Write-Host "🔍 Status check $attempt/$maxAttempts..." -ForegroundColor Cyan
                
                try {
                    $status = Invoke-RestMethod -Uri "http://localhost:7777/api/status/$requestId"
                    
                    Write-Host "  📊 Status: $($status.status)" -ForegroundColor White
                    
                    if ($status.status -eq "completed") {
                        Write-Host "✅ Generation completed!" -ForegroundColor Green
                        Write-Host "  📁 File Path: $($status.filePath)" -ForegroundColor White
                        Write-Host "  🌐 Image URL: $($status.imageUrl)" -ForegroundColor White
                        Write-Host "  📅 Created: $($status.createdAt)" -ForegroundColor White
                        Write-Host "  ✅ Completed: $($status.completedAt)" -ForegroundColor White

                        # Display final parameters
                        if ($status.imageParameters) {
                            Write-Host "  🎨 Final Image Parameters:" -ForegroundColor Cyan
                            Write-Host "    📐 Dimensions: $($status.imageParameters.width)x$($status.imageParameters.height)" -ForegroundColor White
                            Write-Host "    🎲 Seed Used: $($status.imageParameters.seed)" -ForegroundColor White
                        }

                        $completed = $true

                        # Download image
                        $outputPath = "images\$Filename"
                        Write-Host "`n📥 Downloading image..." -ForegroundColor Cyan
                        Invoke-WebRequest -Uri "http://localhost:7777/api/image/$requestId" -OutFile $outputPath

                        if (Test-Path $outputPath) {
                            $fileSize = (Get-Item $outputPath).Length
                            Write-Host "✅ Downloaded successfully! Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
                        } else {
                            Write-Host "❌ Download failed!" -ForegroundColor Red
                        }
                    } elseif ($status.status -eq "failed") {
                        Write-Host "❌ Generation failed!" -ForegroundColor Red
                        if ($status.error) {
                            Write-Host "  🔍 Error: $($status.error)" -ForegroundColor Red
                        }
                        $completed = $true
                        exit 1
                    } elseif ($status.status -eq "processing") {
                        Write-Host "  🔄 Still processing..." -ForegroundColor Yellow
                    } elseif ($status.status -eq "pending") {
                        Write-Host "  ⏸️ Queued for processing..." -ForegroundColor Magenta
                    } else {
                        Write-Host "  ❓ Unknown status: $($status.status)" -ForegroundColor Yellow
                    }
                } catch {
                    Write-Host "  ⚠️ Error checking status: $($_.Exception.Message)" -ForegroundColor Red
                }
                
                if (!$completed) {
                    Write-Host "  ⏸️ Waiting 15 seconds..." -ForegroundColor Gray
                    Start-Sleep -Seconds 15
                }
            }
            
            if (!$completed) {
                Write-Host "⏰ Timeout reached after $($attempt * 15) seconds" -ForegroundColor Red
                exit 1
            }
        }
        
        Write-Host "`n🎉 Image generation completed successfully!" -ForegroundColor Green
        Write-Host "📁 Check the 'images' folder for your generated image." -ForegroundColor Cyan
        
    } else {
        Write-Host "❌ Request failed: API returned success=false" -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
