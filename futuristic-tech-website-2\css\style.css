/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    overflow-x: hidden;
    line-height: 1.6;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 1.2rem;
}

.nav-brand i {
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 400;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #00ff88;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icons a {
    color: #ffffff;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.social-icons a:hover {
    color: #00ff88;
}

/* Main Content */
.main-content {
    margin-top: 80px;
    min-height: 100vh;
}

/* Hero Section */
.hero {
    padding: 4rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 3rem;
    align-items: start;
    min-height: 80vh;
}

/* Hero Left */
.hero-left {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.hero-title {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.title-small {
    font-size: 1.2rem;
    font-weight: 300;
    color: #cccccc;
}

.vision-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.vision-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ffffff;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #00ff88;
}

.stat-label {
    font-size: 0.8rem;
    color: #cccccc;
}

.solution-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.solution-card h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.solution-card p {
    font-size: 0.9rem;
    color: #cccccc;
    margin-bottom: 1rem;
}

.card-stats {
    display: flex;
    gap: 1rem;
}

.card-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.card-stat .number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #00ff88;
}

.card-stat .label {
    font-size: 0.8rem;
    color: #cccccc;
}

/* Hero Center */
.hero-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
}

.main-title {
    font-size: 4rem;
    font-weight: 700;
    letter-spacing: 0.2em;
    color: #333333;
    margin-bottom: 2rem;
    text-transform: uppercase;
}

.sphere-container {
    position: relative;
    width: 300px;
    height: 300px;
    margin: 2rem 0;
}

.sphere {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
    border-radius: 50%;
    position: relative;
    box-shadow: 
        0 20px 40px rgba(255, 255, 255, 0.1),
        inset 0 10px 20px rgba(0, 0, 0, 0.2);
    animation: float 6s ease-in-out infinite;
}

.sphere::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 20%;
    width: 60%;
    height: 60%;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    border-radius: 50%;
    transform: rotate(-45deg);
}

.sphere-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle, rgba(0, 255, 136, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 4s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

.center-stats {
    margin-top: 2rem;
}

.center-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.center-stat .number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #00ff88;
}

.center-stat .label {
    font-size: 0.9rem;
    color: #cccccc;
}

/* Hero Right */
.hero-right {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.right-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-card .number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #00ff88;
}

.stat-card .label {
    font-size: 0.8rem;
    color: #cccccc;
}

.innovation-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.innovation-card h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.geometric-pattern {
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, #00ff88 0%, transparent 50%);
    opacity: 0.1;
    clip-path: polygon(0 0, 100% 0, 100% 100%);
}

.software-section {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.software-section h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.software-visual {
    width: 100%;
    height: 80px;
    background: linear-gradient(90deg, #333333 0%, #555555 50%, #333333 100%);
    border-radius: 8px;
    margin: 1rem 0;
    position: relative;
    overflow: hidden;
}

.software-visual::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(0, 255, 136, 0.3) 50%, transparent 100%);
    animation: scan 3s linear infinite;
}

@keyframes scan {
    0% { left: -100%; }
    100% { left: 100%; }
}

.software-section p {
    font-size: 0.9rem;
    color: #cccccc;
}

/* Services Section */
.services {
    display: flex;
    justify-content: center;
    gap: 2rem;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.service-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    flex: 1;
    max-width: 300px;
}

.service-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #00ff88;
}

.service-item p {
    font-size: 0.9rem;
    color: #cccccc;
}

/* Bottom Section */
.bottom-section {
    padding: 4rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.vision-bottom {
    text-align: center;
}

.vision-bottom h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: #ffffff;
}

.description-cards {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.desc-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 500px;
}

.desc-card h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #00ff88;
}

.desc-card p {
    font-size: 1rem;
    color: #cccccc;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .main-title {
        font-size: 3rem;
    }
    
    .sphere-container {
        width: 250px;
        height: 250px;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .services {
        flex-direction: column;
        align-items: center;
    }
    
    .description-cards {
        flex-direction: column;
        align-items: center;
    }
    
    .main-title {
        font-size: 2.5rem;
    }
    
    .sphere-container {
        width: 200px;
        height: 200px;
    }
}
