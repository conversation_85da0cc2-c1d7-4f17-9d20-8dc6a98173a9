// Spa Wellness Template JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initBookingForm();
    initScrollAnimations();
    initMobileMenu();
    initPackageCards();
});

// Booking Form Functionality
function initBookingForm() {
    const bookingForm = document.querySelector('.booking-form');
    
    bookingForm?.addEventListener('submit', (e) => {
        e.preventDefault();
        
        const formData = new FormData(bookingForm);
        const name = bookingForm.querySelector('input[type="text"]').value;
        const email = bookingForm.querySelector('input[type="email"]').value;
        const phone = bookingForm.querySelector('input[type="tel"]').value;
        const service = bookingForm.querySelector('select').value;
        const date = bookingForm.querySelector('input[type="date"]').value;
        const time = bookingForm.querySelectorAll('select')[1].value;

        if (!name || !email || !phone || !service || !date || !time) {
            showNotification('Please fill in all required fields.', 'error');
            return;
        }

        // Validate date is not in the past
        const selectedDate = new Date(date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (selectedDate < today) {
            showNotification('Please select a future date.', 'error');
            return;
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            showNotification('Please enter a valid email address.', 'error');
            return;
        }

        // Simulate form submission
        const submitBtn = bookingForm.querySelector('.btn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Booking...';
        submitBtn.disabled = true;

        setTimeout(() => {
            showNotification(`Appointment booked successfully for ${formatDate(date)} at ${formatTime(time)}! We'll send you a confirmation email shortly.`, 'success');
            bookingForm.reset();
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });

    // Set minimum date to today
    const dateInput = bookingForm?.querySelector('input[type="date"]');
    if (dateInput) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.setAttribute('min', today);
    }
}

// Package Cards Interaction
function initPackageCards() {
    const packageCards = document.querySelectorAll('.package-card');
    const packageButtons = document.querySelectorAll('.package-card .btn');

    packageButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const packageCard = btn.closest('.package-card');
            const packageName = packageCard.querySelector('h3').textContent;
            const packagePrice = packageCard.querySelector('.package-price').textContent;
            
            showPackageModal(packageName, packagePrice);
        });
    });
}

function showPackageModal(packageName, packagePrice) {
    const modalHtml = `
        <div class="package-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Book ${packageName}</h3>
                    <button class="close-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="package-summary">
                        <h4>${packageName}</h4>
                        <div class="price">${packagePrice}</div>
                    </div>
                    <form class="package-booking-form">
                        <div class="form-group">
                            <input type="text" placeholder="Full Name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" placeholder="Email Address" required>
                        </div>
                        <div class="form-group">
                            <input type="tel" placeholder="Phone Number" required>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <input type="date" required>
                            </div>
                            <div class="form-group">
                                <select required>
                                    <option value="">Preferred Time</option>
                                    <option value="9:00">9:00 AM</option>
                                    <option value="10:00">10:00 AM</option>
                                    <option value="11:00">11:00 AM</option>
                                    <option value="12:00">12:00 PM</option>
                                    <option value="13:00">1:00 PM</option>
                                    <option value="14:00">2:00 PM</option>
                                    <option value="15:00">3:00 PM</option>
                                    <option value="16:00">4:00 PM</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <textarea placeholder="Special Requests (Optional)" rows="3"></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Book Package</button>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = document.querySelector('.package-modal');
    const closeBtn = modal.querySelector('.close-modal');
    const form = modal.querySelector('.package-booking-form');

    // Set minimum date to today
    const dateInput = form.querySelector('input[type="date"]');
    const today = new Date().toISOString().split('T')[0];
    dateInput.setAttribute('min', today);

    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });

    form.addEventListener('submit', (e) => {
        e.preventDefault();
        
        const name = form.querySelector('input[type="text"]').value;
        const email = form.querySelector('input[type="email"]').value;
        const phone = form.querySelector('input[type="tel"]').value;
        const date = form.querySelector('input[type="date"]').value;
        const time = form.querySelector('select').value;

        if (!name || !email || !phone || !date || !time) {
            showNotification('Please fill in all required fields.', 'error');
            return;
        }

        const submitBtn = form.querySelector('.btn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Booking...';
        submitBtn.disabled = true;

        setTimeout(() => {
            showNotification(`${packageName} package booked successfully! We'll contact you soon to confirm your appointment.`, 'success');
            document.body.removeChild(modal);
        }, 2000);
    });
}

// Scroll Animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-item, .service-card, .treatment-item, .therapist-card, .package-card, .tip-item');
    animateElements.forEach(el => observer.observe(el));
}

// Mobile Menu
function initMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const nav = document.querySelector('.nav');

    mobileToggle?.addEventListener('click', () => {
        nav.classList.toggle('active');
        const icon = mobileToggle.querySelector('i');
        
        if (nav.classList.contains('active')) {
            icon.classList.remove('fa-bars');
            icon.classList.add('fa-times');
        } else {
            icon.classList.remove('fa-times');
            icon.classList.add('fa-bars');
        }
    });

    // Close menu when clicking on links
    const navLinks = document.querySelectorAll('.nav-list a');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            nav.classList.remove('active');
            const icon = mobileToggle.querySelector('i');
            icon.classList.remove('fa-times');
            icon.classList.add('fa-bars');
        });
    });
}

// Utility Functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '1rem 1.5rem',
        borderRadius: '15px',
        color: 'white',
        fontWeight: '500',
        zIndex: '9999',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        maxWidth: '350px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.2)'
    });

    // Set background color based on type
    const colors = {
        success: 'linear-gradient(135deg, #38a169, #68d391)',
        error: 'linear-gradient(135deg, #e53e3e, #fc8181)',
        info: 'linear-gradient(135deg, #3182ce, #63b3ed)',
        warning: 'linear-gradient(135deg, #d69e2e, #f6e05e)'
    };
    notification.style.background = colors[type] || colors.info;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function formatTime(timeString) {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Header scroll effect
window.addEventListener('scroll', () => {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(255, 255, 255, 0.98)';
        header.style.backdropFilter = 'blur(20px)';
    } else {
        header.style.background = 'rgba(255, 255, 255, 0.95)';
        header.style.backdropFilter = 'blur(10px)';
    }
});

// Add CSS for animations and modal
const style = document.createElement('style');
style.textContent = `
    .animate-in {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .package-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
        backdrop-filter: blur(5px);
    }

    .modal-content {
        background: white;
        border-radius: 20px;
        padding: 0;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 2rem;
        border-bottom: 1px solid #e2e8f0;
        background: linear-gradient(135deg, #f7fafc, #e6fffa);
        border-radius: 20px 20px 0 0;
    }

    .modal-header h3 {
        margin: 0;
        color: #2d3748;
        font-size: 1.3rem;
    }

    .close-modal {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #718096;
        padding: 0.5rem;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .close-modal:hover {
        background: #e2e8f0;
        color: #2d3748;
    }

    .modal-body {
        padding: 2rem;
    }

    .package-summary {
        text-align: center;
        margin-bottom: 2rem;
        padding: 1rem;
        background: #f7fafc;
        border-radius: 15px;
    }

    .package-summary h4 {
        margin: 0 0 0.5rem 0;
        color: #2d3748;
    }

    .package-summary .price {
        font-size: 1.5rem;
        font-weight: 700;
        color: #38a169;
    }

    .package-booking-form .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .package-booking-form .form-group {
        margin-bottom: 1rem;
    }

    .package-booking-form input,
    .package-booking-form select,
    .package-booking-form textarea {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
    }

    .package-booking-form input:focus,
    .package-booking-form select:focus,
    .package-booking-form textarea:focus {
        outline: none;
        border-color: #38a169;
    }

    @media (max-width: 768px) {
        .nav.active {
            display: block;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 1rem;
            border-radius: 0 0 15px 15px;
        }

        .nav-list {
            flex-direction: column;
            gap: 1rem;
        }

        .package-booking-form .form-row {
            grid-template-columns: 1fr;
        }
    }
`;
document.head.appendChild(style);
