<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:;">
    <title>RemoteIt - Remote Desktop Software</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="logo">
                <i class="fas fa-desktop"></i>
                <span>RemoteIt</span>
            </div>
            <div class="loading-spinner"></div>
            <div class="loading-text">Initializing...</div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="app hidden">
        <!-- Title Bar -->
        <div class="title-bar">
            <div class="title-bar-left">
                <div class="app-logo">
                    <i class="fas fa-desktop"></i>
                    <span>RemoteIt</span>
                </div>
            </div>
            <div class="title-bar-center">
                <span id="title-text">Remote Desktop Software</span>
            </div>
            <div class="title-bar-right">
                <button class="title-bar-button" id="minimize-btn">
                    <i class="fas fa-minus"></i>
                </button>
                <button class="title-bar-button" id="maximize-btn">
                    <i class="fas fa-square"></i>
                </button>
                <button class="title-bar-button close" id="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="sidebar">
            <div class="nav-section">
                <div class="nav-item active" data-view="dashboard">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </div>
                <div class="nav-item" data-view="devices">
                    <i class="fas fa-desktop"></i>
                    <span>My Devices</span>
                </div>
                <div class="nav-item" data-view="connections">
                    <i class="fas fa-link"></i>
                    <span>Connections</span>
                </div>
                <div class="nav-item" data-view="files">
                    <i class="fas fa-folder"></i>
                    <span>File Transfer</span>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-item" data-view="settings">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </div>
                <div class="nav-item" data-view="help">
                    <i class="fas fa-question-circle"></i>
                    <span>Help</span>
                </div>
            </div>

            <div class="nav-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <div class="user-name" id="user-name">Loading...</div>
                        <div class="user-status online">Online</div>
                    </div>
                </div>
                <button class="logout-btn" id="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard View -->
            <div id="dashboard-view" class="view active">
                <div class="view-header">
                    <h1>Dashboard</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" id="new-connection-btn">
                            <i class="fas fa-plus"></i>
                            New Connection
                        </button>
                        <button class="btn btn-secondary" id="host-mode-btn">
                            <i class="fas fa-share-alt"></i>
                            Host Mode
                        </button>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>Quick Connect</h3>
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="card-content">
                            <div class="quick-connect-form">
                                <input type="text" placeholder="Device ID or Name" id="quick-connect-input">
                                <button class="btn btn-primary" id="quick-connect-btn">Connect</button>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>Recent Connections</h3>
                            <i class="fas fa-history"></i>
                        </div>
                        <div class="card-content">
                            <div id="recent-connections" class="connection-list">
                                <!-- Recent connections will be populated here -->
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>System Status</h3>
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="card-content">
                            <div class="status-grid">
                                <div class="status-item">
                                    <span class="status-label">Connection</span>
                                    <span class="status-value online" id="connection-status">Online</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">Active Sessions</span>
                                    <span class="status-value" id="active-sessions">0</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">Data Transfer</span>
                                    <span class="status-value" id="data-transfer">0 MB/s</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>Performance</h3>
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-content">
                            <div class="performance-metrics">
                                <div class="metric">
                                    <span class="metric-label">CPU Usage</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: 25%"></div>
                                    </div>
                                    <span class="metric-value">25%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Memory</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: 45%"></div>
                                    </div>
                                    <span class="metric-value">45%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Network</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: 15%"></div>
                                    </div>
                                    <span class="metric-value">15%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Devices View -->
            <div id="devices-view" class="view">
                <div class="view-header">
                    <h1>My Devices</h1>
                    <div class="header-actions">
                        <button class="btn btn-secondary" id="refresh-devices-btn">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                        <button class="btn btn-primary" id="add-device-btn">
                            <i class="fas fa-plus"></i>
                            Add Device
                        </button>
                    </div>
                </div>

                <div class="devices-grid" id="devices-grid">
                    <!-- Devices will be populated here -->
                </div>
            </div>

            <!-- Connections View -->
            <div id="connections-view" class="view">
                <div class="view-header">
                    <h1>Active Connections</h1>
                    <div class="header-actions">
                        <button class="btn btn-secondary" id="refresh-connections-btn">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                    </div>
                </div>

                <div class="connections-list" id="connections-list">
                    <!-- Active connections will be populated here -->
                </div>
            </div>

            <!-- Files View -->
            <div id="files-view" class="view">
                <div class="view-header">
                    <h1>File Transfer</h1>
                    <div class="header-actions">
                        <button class="btn btn-primary" id="select-files-btn">
                            <i class="fas fa-file-upload"></i>
                            Select Files
                        </button>
                    </div>
                </div>

                <div class="file-transfer-container">
                    <div class="transfer-queue" id="transfer-queue">
                        <!-- File transfers will be shown here -->
                    </div>
                </div>
            </div>

            <!-- Settings View -->
            <div id="settings-view" class="view">
                <div class="view-header">
                    <h1>Settings</h1>
                </div>

                <div class="settings-container">
                    <div class="settings-section">
                        <h3>General</h3>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="auto-start">
                                Start RemoteIt on system startup
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="minimize-to-tray">
                                Minimize to system tray
                            </label>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3>Connection</h3>
                        <div class="setting-item">
                            <label for="connection-quality">Connection Quality:</label>
                            <select id="connection-quality">
                                <option value="auto">Auto</option>
                                <option value="high">High</option>
                                <option value="medium">Medium</option>
                                <option value="low">Low</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label for="frame-rate">Frame Rate:</label>
                            <input type="range" id="frame-rate" min="10" max="60" value="30">
                            <span id="frame-rate-value">30 FPS</span>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3>Security</h3>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="require-permission">
                                Require permission for incoming connections
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="session-recording">
                                Enable session recording
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help View -->
            <div id="help-view" class="view">
                <div class="view-header">
                    <h1>Help & Support</h1>
                </div>

                <div class="help-container">
                    <div class="help-section">
                        <h3>Getting Started</h3>
                        <ul>
                            <li><a href="#" class="help-link">Quick Start Guide</a></li>
                            <li><a href="#" class="help-link">Setting up Remote Access</a></li>
                            <li><a href="#" class="help-link">File Transfer Guide</a></li>
                        </ul>
                    </div>

                    <div class="help-section">
                        <h3>Troubleshooting</h3>
                        <ul>
                            <li><a href="#" class="help-link">Connection Issues</a></li>
                            <li><a href="#" class="help-link">Performance Problems</a></li>
                            <li><a href="#" class="help-link">Firewall Configuration</a></li>
                        </ul>
                    </div>

                    <div class="help-section">
                        <h3>Support</h3>
                        <div class="support-info">
                            <p>Version: <span id="app-version">1.0.0</span></p>
                            <p>Need help? Contact our support team:</p>
                            <button class="btn btn-primary" id="contact-support">
                                <i class="fas fa-envelope"></i>
                                Contact Support
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Login Modal -->
    <div id="login-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Sign In to RemoteIt</h2>
            </div>
            <div class="modal-body">
                <form id="login-form">
                    <div class="form-group">
                        <label for="email">Email:</label>
                        <input type="email" id="email" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" required>
                    </div>
                    <div class="form-group" id="mfa-group" style="display: none;">
                        <label for="mfa-code">MFA Code:</label>
                        <input type="text" id="mfa-code" placeholder="Enter 6-digit code">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Sign In</button>
                        <button type="button" class="btn btn-secondary" id="register-btn">Register</button>
                    </div>
                </form>
                <div id="login-error" class="error-message" style="display: none;"></div>
            </div>
        </div>
    </div>

    <!-- Connection Request Modal -->
    <div id="connection-request-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Incoming Connection Request</h2>
            </div>
            <div class="modal-body">
                <p>A user is requesting to connect to your computer.</p>
                <div class="connection-info">
                    <div class="info-item">
                        <span class="label">From:</span>
                        <span class="value" id="request-from">Unknown</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Device:</span>
                        <span class="value" id="request-device">Unknown</span>
                    </div>
                </div>
                <div class="form-actions">
                    <button class="btn btn-primary" id="accept-connection">Accept</button>
                    <button class="btn btn-secondary" id="reject-connection">Reject</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/app.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/devices.js"></script>
    <script src="js/connections.js"></script>
    <script src="js/files.js"></script>
    <script src="js/settings.js"></script>
</body>
</html>
