<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RemoteIt Portal - Web Access</title>
    <link rel="stylesheet" href="css/portal.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><circle cx='16' cy='16' r='8' fill='%23007acc'/><circle cx='16' cy='16' r='12' fill='none' stroke='%23007acc' stroke-width='2'/></svg>">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="logo">
                <i class="fas fa-desktop"></i>
                <span>RemoteIt Portal</span>
            </div>
            <div class="loading-spinner"></div>
            <div class="loading-text">Connecting...</div>
        </div>
    </div>

    <!-- Login Screen -->
    <div id="login-screen" class="login-screen hidden">
        <div class="login-container">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-desktop"></i>
                    <span>RemoteIt Portal</span>
                </div>
                <h1>Sign in to access your devices</h1>
                <p>Secure remote access from anywhere</p>
            </div>
            
            <form id="login-form" class="login-form">
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <div class="form-group" id="mfa-group" style="display: none;">
                    <label for="mfa-code">Two-Factor Authentication Code</label>
                    <input type="text" id="mfa-code" name="mfa-code" placeholder="Enter 6-digit code">
                </div>
                
                <button type="submit" class="btn btn-primary" id="login-btn">
                    <span class="btn-text">Sign In</span>
                    <div class="btn-spinner hidden">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>
                
                <div id="login-error" class="error-message hidden"></div>
            </form>
            
            <div class="login-footer">
                <p>Don't have an account? <a href="#" id="register-link">Sign up</a></p>
                <p><a href="#" id="forgot-password-link">Forgot your password?</a></p>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="app hidden">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <i class="fas fa-desktop"></i>
                    <span>RemoteIt Portal</span>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-bar">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search devices..." id="device-search">
                </div>
            </div>
            
            <div class="header-right">
                <div class="user-menu">
                    <div class="user-info">
                        <span class="user-name" id="user-name">Loading...</span>
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="user-dropdown">
                        <a href="#" id="profile-link">
                            <i class="fas fa-user-cog"></i>
                            Profile Settings
                        </a>
                        <a href="#" id="download-app-link">
                            <i class="fas fa-download"></i>
                            Download App
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" id="logout-link">
                            <i class="fas fa-sign-out-alt"></i>
                            Sign Out
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Quick Actions -->
            <section class="quick-actions">
                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="action-content">
                        <h3>Add Device</h3>
                        <p>Install RemoteIt on a new device</p>
                    </div>
                    <button class="btn btn-secondary" id="add-device-btn">Add Device</button>
                </div>
                
                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="action-content">
                        <h3>Quick Connect</h3>
                        <p>Connect to a device by ID</p>
                    </div>
                    <div class="quick-connect-form">
                        <input type="text" placeholder="Device ID" id="quick-connect-input">
                        <button class="btn btn-primary" id="quick-connect-btn">Connect</button>
                    </div>
                </div>
            </section>

            <!-- Device Grid -->
            <section class="devices-section">
                <div class="section-header">
                    <h2>My Devices</h2>
                    <div class="section-actions">
                        <button class="btn btn-secondary" id="refresh-devices-btn">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="grid">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="view-btn" data-view="list">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="devices-container" class="devices-grid">
                    <!-- Devices will be populated here -->
                </div>
                
                <div id="no-devices" class="no-devices hidden">
                    <div class="no-devices-icon">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <h3>No devices found</h3>
                    <p>Install RemoteIt on your devices to get started</p>
                    <button class="btn btn-primary" id="setup-device-btn">
                        <i class="fas fa-download"></i>
                        Download RemoteIt
                    </button>
                </div>
            </section>

            <!-- Active Sessions -->
            <section class="sessions-section">
                <div class="section-header">
                    <h2>Active Sessions</h2>
                    <span class="session-count" id="session-count">0 active</span>
                </div>
                
                <div id="sessions-container" class="sessions-list">
                    <!-- Active sessions will be populated here -->
                </div>
            </section>
        </main>
    </div>

    <!-- Remote Desktop Viewer -->
    <div id="remote-viewer" class="remote-viewer hidden">
        <div class="viewer-header">
            <div class="viewer-info">
                <span class="device-name" id="viewer-device-name">Device Name</span>
                <span class="connection-status" id="viewer-status">Connecting...</span>
            </div>
            
            <div class="viewer-controls">
                <button class="control-btn" id="fullscreen-btn" title="Fullscreen">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="control-btn" id="settings-btn" title="Settings">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="control-btn" id="file-transfer-btn" title="File Transfer">
                    <i class="fas fa-folder"></i>
                </button>
                <button class="control-btn danger" id="disconnect-btn" title="Disconnect">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        
        <div class="viewer-content">
            <canvas id="remote-canvas" class="remote-canvas"></canvas>
            <div class="viewer-overlay" id="viewer-overlay">
                <div class="connection-message">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>Establishing connection...</span>
                </div>
            </div>
        </div>
        
        <div class="viewer-footer">
            <div class="connection-info">
                <span class="info-item">
                    <i class="fas fa-signal"></i>
                    <span id="connection-quality">Good</span>
                </span>
                <span class="info-item">
                    <i class="fas fa-clock"></i>
                    <span id="session-duration">00:00</span>
                </span>
                <span class="info-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span id="frame-rate">30 FPS</span>
                </span>
            </div>
        </div>
    </div>

    <!-- Connection Request Modal -->
    <div id="connection-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Connect to Device</h3>
                <button class="modal-close" id="modal-close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <div class="device-info">
                    <div class="device-icon">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <div class="device-details">
                        <h4 id="modal-device-name">Device Name</h4>
                        <p id="modal-device-info">Platform • Last seen</p>
                    </div>
                </div>
                
                <div class="connection-options">
                    <div class="option-group">
                        <label>
                            <input type="radio" name="connection-type" value="view" checked>
                            <span class="option-label">
                                <i class="fas fa-eye"></i>
                                View Only
                            </span>
                            <span class="option-desc">View the remote screen without control</span>
                        </label>
                    </div>
                    
                    <div class="option-group">
                        <label>
                            <input type="radio" name="connection-type" value="control">
                            <span class="option-label">
                                <i class="fas fa-mouse-pointer"></i>
                                Full Control
                            </span>
                            <span class="option-desc">View and control the remote device</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="modal-footer">
                <button class="btn btn-secondary" id="modal-cancel-btn">Cancel</button>
                <button class="btn btn-primary" id="modal-connect-btn">
                    <i class="fas fa-link"></i>
                    Connect
                </button>
            </div>
        </div>
    </div>

    <!-- File Transfer Modal -->
    <div id="file-transfer-modal" class="modal hidden">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>File Transfer</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <div class="file-transfer-container">
                    <div class="file-panel local-panel">
                        <div class="panel-header">
                            <h4>Local Files</h4>
                            <button class="btn btn-small" id="upload-files-btn">
                                <i class="fas fa-upload"></i>
                                Upload
                            </button>
                        </div>
                        <div class="file-browser" id="local-files">
                            <!-- Local files will be populated here -->
                        </div>
                    </div>
                    
                    <div class="transfer-controls">
                        <button class="transfer-btn" id="transfer-to-remote" disabled>
                            <i class="fas fa-arrow-right"></i>
                        </button>
                        <button class="transfer-btn" id="transfer-to-local" disabled>
                            <i class="fas fa-arrow-left"></i>
                        </button>
                    </div>
                    
                    <div class="file-panel remote-panel">
                        <div class="panel-header">
                            <h4>Remote Files</h4>
                            <button class="btn btn-small" id="refresh-remote-btn">
                                <i class="fas fa-sync-alt"></i>
                                Refresh
                            </button>
                        </div>
                        <div class="file-browser" id="remote-files">
                            <!-- Remote files will be populated here -->
                        </div>
                    </div>
                </div>
                
                <div class="transfer-queue" id="transfer-queue">
                    <!-- Active transfers will be shown here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notifications" class="notifications"></div>

    <!-- Scripts -->
    <script src="js/portal.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/devices.js"></script>
    <script src="js/remote-viewer.js"></script>
    <script src="js/file-transfer.js"></script>
</body>
</html>
