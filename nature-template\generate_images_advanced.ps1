# 🌿 Green Nature Tech - Advanced Image Generation with New API Features
# Utilizes all new capabilities: width/height, seed, timeout, priority, generate-and-wait

param(
    [string]$OutputDir = "images",
    [switch]$UseWaitEndpoint = $false,
    [switch]$Force = $false,
    [int]$BaseSeed = 12345
)

Write-Host "🌿 ADVANCED IMAGE GENERATION - Green Nature Tech" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Ensure output directory exists
if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

# Advanced image configuration with new API features
$imageRequests = @(
    @{
        name = "hero-background-hd"
        filename = "hero-background-hd.jpg"
        prompt = "Dark mystical forest landscape with subtle green technology elements, misty atmosphere, tall ancient trees, green glowing accents, professional nature photography"
        width = 1920
        height = 1080
        seed = $BaseSeed
        timeout = 300
        priority = "high"
        description = "High-resolution hero background"
    },
    @{
        name = "green-innovation-square"
        filename = "green-innovation-square.jpg"
        prompt = "Modern sustainable technology laboratory, green innovation workspace, eco-friendly devices, clean energy solutions, professional photography"
        width = 1024
        height = 1024
        seed = $BaseSeed + 1
        timeout = 240
        priority = "high"
        description = "Square format innovation image"
    },
    @{
        name = "nature-tech-portrait"
        filename = "nature-tech-portrait.jpg"
        prompt = "Vertical nature and technology integration, biomimicry concepts, green research environment, sustainable innovation"
        width = 768
        height = 1024
        seed = $BaseSeed + 2
        timeout = 180
        priority = "normal"
        description = "Portrait orientation tech-nature"
    },
    @{
        name = "eco-solutions-wide"
        filename = "eco-solutions-wide.jpg"
        prompt = "Wide panoramic view of renewable energy solutions, solar panels, wind turbines, green technology landscape"
        width = 1600
        height = 900
        seed = $BaseSeed + 3
        timeout = 240
        priority = "normal"
        description = "Wide format eco solutions"
    },
    @{
        name = "sustainable-future-hd"
        filename = "sustainable-future-hd.jpg"
        prompt = "Ultra-detailed sustainable future cityscape, green architecture, renewable energy infrastructure, eco-friendly urban design"
        width = 2048
        height = 1152
        seed = $BaseSeed + 4
        timeout = 480
        priority = "high"
        description = "Ultra HD future city"
    },
    @{
        name = "green-lab-detailed"
        filename = "green-lab-detailed.jpg"
        prompt = "Highly detailed green innovation laboratory, advanced eco-friendly equipment, sustainable research facility, modern clean environment"
        width = 1536
        height = 1024
        seed = $BaseSeed + 5
        timeout = 360
        priority = "normal"
        description = "Detailed laboratory scene"
    }
)

# Check service health with new endpoint
Write-Host "🏥 Checking enhanced API service..." -ForegroundColor Cyan
try {
    $health = Invoke-RestMethod -Uri "http://localhost:7777/health" -TimeoutSec 10
    if ($health.status -eq "healthy") {
        Write-Host "✅ Service healthy - Database: $($health.database)" -ForegroundColor Green
        Write-Host "📊 Services: $($health.services)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Service not healthy" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Cannot connect to enhanced API service" -ForegroundColor Red
    Write-Host "Please ensure the service is running on localhost:7777" -ForegroundColor Yellow
    exit 1
}

# Get available models
Write-Host "🤖 Checking available models..." -ForegroundColor Cyan
try {
    $models = Invoke-RestMethod -Uri "http://localhost:7777/api/models"
    Write-Host "📝 Text Models: $($models.models.text -join ', ')" -ForegroundColor Gray
    Write-Host "🎨 Image Models: $($models.models.image -join ', ')" -ForegroundColor Gray
    Write-Host "🔧 Image Parameters: Width($($models.imageParameters.width.min)-$($models.imageParameters.width.max)), Height($($models.imageParameters.height.min)-$($models.imageParameters.height.max)), Seed($($models.imageParameters.seed.min)-$($models.imageParameters.seed.max))" -ForegroundColor Gray
} catch {
    Write-Host "⚠️ Could not fetch models info" -ForegroundColor Yellow
}

$results = @()
$successCount = 0
$failCount = 0
$skippedCount = 0

foreach ($img in $imageRequests) {
    Write-Host "`n🎯 Processing: $($img.name)" -ForegroundColor Magenta
    Write-Host "📝 Description: $($img.description)" -ForegroundColor Gray
    Write-Host "📐 Dimensions: $($img.width)x$($img.height)" -ForegroundColor Cyan
    Write-Host "🎲 Seed: $($img.seed)" -ForegroundColor Cyan
    Write-Host "⏱️ Timeout: $($img.timeout)s" -ForegroundColor Cyan
    Write-Host "🚀 Priority: $($img.priority)" -ForegroundColor Cyan
    
    # Check if already exists
    $outputPath = "$OutputDir\$($img.filename)"
    if ((Test-Path $outputPath) -and !$Force) {
        Write-Host "⏭️ Already exists, skipping..." -ForegroundColor Yellow
        $skippedCount++
        continue
    }
    
    try {
        # Prepare request body with all new parameters
        $body = @{
            type = "image"
            prompt = $img.prompt
            width = $img.width
            height = $img.height
            seed = $img.seed
            timeout = $img.timeout
            priority = $img.priority
        } | ConvertTo-Json
        
        if ($UseWaitEndpoint) {
            # Use the new generate-and-wait endpoint
            Write-Host "🔄 Using generate-and-wait endpoint..." -ForegroundColor Cyan
            $response = Invoke-RestMethod -Uri "http://localhost:7777/api/generate-and-wait" -Method POST -Body $body -ContentType "application/json" -TimeoutSec ($img.timeout + 30)
            
            if ($response.success -and $response.status -eq "completed") {
                Write-Host "✅ Generation completed immediately!" -ForegroundColor Green
                
                # Download image
                Write-Host "📥 Downloading image..." -ForegroundColor Cyan
                Invoke-WebRequest -Uri "http://localhost:7777/api/image/$($response.requestId)" -OutFile $outputPath
                
                if (Test-Path $outputPath) {
                    $fileSize = (Get-Item $outputPath).Length
                    Write-Host "✅ SUCCESS: $($img.name) - Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
                    $successCount++
                } else {
                    Write-Host "❌ Download failed for $($img.name)" -ForegroundColor Red
                    $failCount++
                }
            } else {
                Write-Host "❌ Generation failed: $($response.message)" -ForegroundColor Red
                $failCount++
            }
        } else {
            # Use traditional polling method
            Write-Host "🚀 Submitting generation request..." -ForegroundColor Cyan
            $response = Invoke-RestMethod -Uri "http://localhost:7777/api/generate" -Method POST -Body $body -ContentType "application/json"
            
            if ($response.success) {
                $requestId = $response.requestId
                Write-Host "✅ Request submitted! ID: $requestId" -ForegroundColor Green
                Write-Host "📊 Estimated time: $($response.estimatedTime)" -ForegroundColor Cyan
                
                # Enhanced status monitoring
                $maxAttempts = [math]::Ceiling($img.timeout / 10) + 5
                $attempt = 0
                $completed = $false
                
                Write-Host "⏳ Monitoring status (max $maxAttempts attempts)..." -ForegroundColor Yellow
                
                while ($attempt -lt $maxAttempts -and !$completed) {
                    $attempt++
                    Start-Sleep -Seconds 10
                    
                    try {
                        $status = Invoke-RestMethod -Uri "http://localhost:7777/api/status/$requestId"
                        
                        switch ($status.status) {
                            "completed" {
                                Write-Host "✅ Generation completed!" -ForegroundColor Green
                                $completed = $true
                                
                                # Download with enhanced info
                                Write-Host "📥 Downloading image..." -ForegroundColor Cyan
                                Invoke-WebRequest -Uri "http://localhost:7777/api/image/$requestId" -OutFile $outputPath
                                
                                if (Test-Path $outputPath) {
                                    $fileSize = (Get-Item $outputPath).Length
                                    Write-Host "✅ SUCCESS: $($img.name)" -ForegroundColor Green
                                    Write-Host "   📊 Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
                                    Write-Host "   📐 Dimensions: $($status.imageParameters.width)x$($status.imageParameters.height)" -ForegroundColor Green
                                    Write-Host "   🎲 Seed: $($status.imageParameters.seed)" -ForegroundColor Green
                                    $successCount++
                                } else {
                                    Write-Host "❌ Download failed" -ForegroundColor Red
                                    $failCount++
                                }
                            }
                            "failed" {
                                Write-Host "❌ Generation failed!" -ForegroundColor Red
                                $completed = $true
                                $failCount++
                            }
                            "processing" {
                                Write-Host "🔄 Processing... (attempt $attempt/$maxAttempts)" -ForegroundColor Yellow
                            }
                            "pending" {
                                Write-Host "⏸️ Pending... (attempt $attempt/$maxAttempts)" -ForegroundColor Yellow
                            }
                            default {
                                Write-Host "❓ Status: $($status.status) (attempt $attempt/$maxAttempts)" -ForegroundColor Yellow
                            }
                        }
                    } catch {
                        Write-Host "⚠️ Status check failed: $($_.Exception.Message)" -ForegroundColor Yellow
                    }
                }
                
                if (!$completed) {
                    Write-Host "⏰ Timeout after $maxAttempts attempts" -ForegroundColor Red
                    $failCount++
                }
            } else {
                Write-Host "❌ Failed to submit request" -ForegroundColor Red
                $failCount++
            }
        }
        
    } catch {
        Write-Host "❌ Error processing $($img.name): $($_.Exception.Message)" -ForegroundColor Red
        $failCount++
    }
    
    # Delay between requests
    if ($img -ne $imageRequests[-1]) {
        Write-Host "⏸️ Waiting 15 seconds before next request..." -ForegroundColor Cyan
        Start-Sleep -Seconds 15
    }
}

# Enhanced final summary
Write-Host "`n📊 ADVANCED GENERATION SUMMARY" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green
Write-Host "✅ Successful: $successCount" -ForegroundColor Green
Write-Host "⏭️ Skipped: $skippedCount" -ForegroundColor Yellow
Write-Host "❌ Failed: $failCount" -ForegroundColor Red
Write-Host "📁 Total Requests: $($imageRequests.Count)" -ForegroundColor Cyan
Write-Host "🎲 Base Seed Used: $BaseSeed" -ForegroundColor Cyan
Write-Host "🔄 Endpoint Used: $(if($UseWaitEndpoint) {'generate-and-wait'} else {'generate + polling'})" -ForegroundColor Cyan

# List all images with enhanced details
Write-Host "`n📂 Generated Images:" -ForegroundColor Cyan
Get-ChildItem -Path $OutputDir -Filter "*.jpg" | Sort-Object Name | ForEach-Object {
    $size = [math]::Round($_.Length / 1KB, 2)
    $created = $_.CreationTime.ToString("yyyy-MM-dd HH:mm")
    Write-Host "  📸 $($_.Name) - $size KB - Created: $created" -ForegroundColor White
}

Write-Host "`n🌿 Advanced image generation completed!" -ForegroundColor Green
Write-Host "🎯 All images generated with consistent seeds for reproducibility" -ForegroundColor Cyan
Write-Host "📐 Multiple aspect ratios and resolutions generated" -ForegroundColor Cyan
Write-Host "⚡ Priority-based processing utilized" -ForegroundColor Cyan
