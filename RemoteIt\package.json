{"name": "remoteit", "version": "1.0.0", "description": "Professional Remote Desktop Software", "main": "src/main.js", "author": "RemoteIt Technologies", "license": "MIT", "homepage": "https://remoteit.com", "repository": {"type": "git", "url": "https://github.com/remoteit/remoteit.git"}, "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"", "dev:main": "nodemon --exec electron . --watch src/main.js", "dev:renderer": "webpack serve --config webpack.dev.js", "build": "npm run build:main && npm run build:renderer", "build:main": "webpack --config webpack.main.js", "build:renderer": "webpack --config webpack.renderer.js", "build:native": "node-gyp rebuild", "package": "electron-builder", "package:windows": "electron-builder --windows", "package:macos": "electron-builder --macos", "package:linux": "electron-builder --linux", "test": "jest", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:e2e": "jest --testPathPattern=tests/e2e", "test-setup": "node test-setup.js", "start-all": "node start-remoteit.js", "start:auth": "node server/auth/AuthServer.js", "start:relay": "node server/relay/RelayServer.js", "start:stun": "node server/stun/STUNServer.js", "start:servers": "concurrently \"npm run start:auth\" \"npm run start:relay\" \"npm run start:stun\"", "test:internet": "node test-internet-connection.js", "build:installer": "node installer/create-installer.js", "build:all-platforms": "node installer/create-installer.js all", "deploy:cloud": "cd cloud/terraform && terraform apply", "lint": "eslint src/ --ext .js,.ts", "lint:fix": "eslint src/ --ext .js,.ts --fix", "docs": "jsdoc -c jsdoc.conf.json", "clean": "rimraf dist/ build/ node_modules/.cache/"}, "dependencies": {"electron": "^28.0.0", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "ws": "^8.14.2", "socket.io-client": "^4.7.4", "simple-peer": "^9.11.1", "node-datachannel": "^0.5.0", "robotjs": "^0.6.0", "screenshot-desktop": "^1.12.7", "sharp": "^0.33.1", "ffmpeg-static": "^5.2.0", "node-forge": "^1.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "axios": "^1.6.2", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "rate-limiter-flexible": "^4.0.1", "winston": "^3.11.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "mime-types": "^2.1.35", "file-type": "^18.7.0", "archiver": "^6.0.1", "unzipper": "^0.10.14"}, "devDependencies": {"electron-builder": "^24.8.1", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-merge": "^5.10.0", "html-webpack-plugin": "^5.5.4", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "file-loader": "^6.2.0", "url-loader": "^4.1.1", "babel-loader": "^9.1.3", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/preset-react": "^7.23.3", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/node": "^20.10.4", "jest": "^29.7.0", "eslint": "^8.55.0", "prettier": "^3.1.1", "nodemon": "^3.0.2", "concurrently": "^8.2.2", "rimraf": "^5.0.5", "jsdoc": "^4.0.2", "node-gyp": "^10.0.1"}, "build": {"appId": "com.remoteit.desktop", "productName": "RemoteIt", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "arm64"]}, {"target": "msi", "arch": ["x64"]}], "icon": "assets/icons/icon.ico", "requestedExecutionLevel": "requireAdministrator"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/icons/icon.icns", "category": "public.app-category.productivity", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64", "arm64"]}, {"target": "deb", "arch": ["x64", "arm64"]}, {"target": "rpm", "arch": ["x64", "arm64"]}], "icon": "assets/icons/", "category": "Network"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "RemoteIt"}, "publish": {"provider": "github", "owner": "remoteit", "repo": "remoteit"}}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["remote desktop", "screen sharing", "remote access", "vnc", "rdp", "teamviewer", "logmein", "electron", "cross-platform"]}