const { EventEmitter } = require('events');
const screenshot = require('screenshot-desktop');
const sharp = require('sharp');
const { screen } = require('electron');
const Logger = require('../utils/Logger');

class ScreenCapture extends EventEmitter {
  constructor() {
    super();
    this.logger = new Logger('screen-capture');
    this.isCapturing = false;
    this.captureInterval = null;
    this.currentDisplay = null;
    this.previousFrame = null;
    this.compressionQuality = 80;
    this.frameRate = 30;
    this.captureFormat = 'jpeg';
    
    // Performance settings
    this.adaptiveQuality = true;
    this.deltaCompression = true;
    this.maxFrameSize = 1920 * 1080;
    
    // Statistics
    this.stats = {
      framesCapture: 0,
      framesSent: 0,
      bytesTransferred: 0,
      averageFrameSize: 0,
      averageLatency: 0
    };
  }

  async initialize() {
    this.logger.info('Initializing screen capture...');
    
    try {
      // Get available displays
      const displays = screen.getAllDisplays();
      this.currentDisplay = displays.find(d => d.primary) || displays[0];
      
      this.logger.info(`Screen capture initialized. Primary display: ${this.currentDisplay.size.width}x${this.currentDisplay.size.height}`);
      
    } catch (error) {
      this.logger.error('Failed to initialize screen capture:', error.message);
      throw error;
    }
  }

  async startCapture(options = {}) {
    if (this.isCapturing) {
      this.logger.warn('Screen capture already running');
      return;
    }

    this.logger.info('Starting screen capture...');
    
    // Apply options
    this.frameRate = options.frameRate || 30;
    this.compressionQuality = options.quality || 80;
    this.captureFormat = options.format || 'jpeg';
    this.currentDisplay = options.display || this.currentDisplay;
    
    this.isCapturing = true;
    this.stats = {
      framesCapture: 0,
      framesSent: 0,
      bytesTransferred: 0,
      averageFrameSize: 0,
      averageLatency: 0,
      startTime: Date.now()
    };

    // Start capture loop
    this.captureInterval = setInterval(() => {
      this.captureFrame();
    }, 1000 / this.frameRate);

    this.emit('captureStarted', {
      display: this.currentDisplay,
      frameRate: this.frameRate,
      quality: this.compressionQuality
    });

    this.logger.info('Screen capture started');
  }

  async stopCapture() {
    if (!this.isCapturing) {
      return;
    }

    this.logger.info('Stopping screen capture...');
    
    this.isCapturing = false;
    
    if (this.captureInterval) {
      clearInterval(this.captureInterval);
      this.captureInterval = null;
    }

    this.previousFrame = null;
    
    this.emit('captureStopped', this.stats);
    this.logger.info('Screen capture stopped');
  }

  async captureFrame() {
    if (!this.isCapturing) {
      return;
    }

    try {
      const startTime = Date.now();
      
      // Capture screenshot
      const imageBuffer = await this.captureScreen();
      
      if (!imageBuffer) {
        return;
      }

      this.stats.framesCapture++;
      
      // Process the frame
      const processedFrame = await this.processFrame(imageBuffer);
      
      if (processedFrame) {
        const captureTime = Date.now() - startTime;
        
        // Update statistics
        this.updateStats(processedFrame, captureTime);
        
        // Emit frame data
        this.emit('frameCapture', {
          data: processedFrame,
          timestamp: Date.now(),
          frameNumber: this.stats.framesCapture,
          size: processedFrame.length,
          captureTime: captureTime,
          display: this.currentDisplay
        });

        this.stats.framesSent++;
      }
      
    } catch (error) {
      this.logger.error('Frame capture error:', error.message);
      this.emit('captureError', error);
    }
  }

  async captureScreen() {
    try {
      const options = {
        format: 'png',
        screen: this.currentDisplay.id
      };

      // Use different capture methods based on platform
      if (process.platform === 'win32') {
        return await this.captureWindows(options);
      } else if (process.platform === 'darwin') {
        return await this.captureMacOS(options);
      } else {
        return await this.captureLinux(options);
      }
      
    } catch (error) {
      this.logger.error('Screen capture failed:', error.message);
      return null;
    }
  }

  async captureWindows(options) {
    // Windows-specific screen capture
    return await screenshot({
      format: 'png',
      screen: options.screen
    });
  }

  async captureMacOS(options) {
    // macOS-specific screen capture
    return await screenshot({
      format: 'png',
      screen: options.screen
    });
  }

  async captureLinux(options) {
    // Linux-specific screen capture using X11
    return await screenshot({
      format: 'png',
      screen: options.screen
    });
  }

  async processFrame(imageBuffer) {
    try {
      let processedBuffer = imageBuffer;
      
      // Resize if needed
      const image = sharp(imageBuffer);
      const metadata = await image.metadata();
      
      if (metadata.width * metadata.height > this.maxFrameSize) {
        const scaleFactor = Math.sqrt(this.maxFrameSize / (metadata.width * metadata.height));
        const newWidth = Math.floor(metadata.width * scaleFactor);
        const newHeight = Math.floor(metadata.height * scaleFactor);
        
        processedBuffer = await image
          .resize(newWidth, newHeight, { 
            kernel: sharp.kernel.lanczos3,
            fastShrinkOnLoad: true 
          })
          .toBuffer();
      }

      // Delta compression
      if (this.deltaCompression && this.previousFrame) {
        const deltaFrame = await this.createDeltaFrame(processedBuffer);
        if (deltaFrame && deltaFrame.length < processedBuffer.length * 0.8) {
          this.previousFrame = processedBuffer;
          return deltaFrame;
        }
      }

      // Compress based on format
      let compressedBuffer;
      if (this.captureFormat === 'jpeg') {
        compressedBuffer = await sharp(processedBuffer)
          .jpeg({ 
            quality: this.compressionQuality,
            progressive: true,
            mozjpeg: true 
          })
          .toBuffer();
      } else if (this.captureFormat === 'webp') {
        compressedBuffer = await sharp(processedBuffer)
          .webp({ 
            quality: this.compressionQuality,
            effort: 4 
          })
          .toBuffer();
      } else {
        compressedBuffer = await sharp(processedBuffer)
          .png({ 
            compressionLevel: 6,
            progressive: true 
          })
          .toBuffer();
      }

      this.previousFrame = processedBuffer;
      return compressedBuffer;
      
    } catch (error) {
      this.logger.error('Frame processing error:', error.message);
      return imageBuffer; // Return original if processing fails
    }
  }

  async createDeltaFrame(currentFrame) {
    try {
      if (!this.previousFrame) {
        return null;
      }

      // Simple delta compression - compare pixel differences
      const current = sharp(currentFrame);
      const previous = sharp(this.previousFrame);
      
      const currentMeta = await current.metadata();
      const previousMeta = await previous.metadata();
      
      // Ensure same dimensions
      if (currentMeta.width !== previousMeta.width || 
          currentMeta.height !== previousMeta.height) {
        return null;
      }

      // Create difference image
      const deltaBuffer = await current
        .composite([{
          input: this.previousFrame,
          blend: 'difference'
        }])
        .jpeg({ quality: this.compressionQuality })
        .toBuffer();

      return deltaBuffer;
      
    } catch (error) {
      this.logger.error('Delta frame creation error:', error.message);
      return null;
    }
  }

  updateStats(frameData, captureTime) {
    this.stats.bytesTransferred += frameData.length;
    this.stats.averageFrameSize = this.stats.bytesTransferred / this.stats.framesSent;
    this.stats.averageLatency = (this.stats.averageLatency + captureTime) / 2;
    
    // Adaptive quality adjustment
    if (this.adaptiveQuality) {
      this.adjustQuality(frameData.length, captureTime);
    }
  }

  adjustQuality(frameSize, captureTime) {
    const targetFrameSize = 50000; // 50KB target
    const targetCaptureTime = 33; // 33ms for 30fps
    
    // Adjust quality based on frame size
    if (frameSize > targetFrameSize * 1.5) {
      this.compressionQuality = Math.max(20, this.compressionQuality - 5);
    } else if (frameSize < targetFrameSize * 0.5) {
      this.compressionQuality = Math.min(95, this.compressionQuality + 5);
    }
    
    // Adjust frame rate based on capture time
    if (captureTime > targetCaptureTime * 1.5) {
      this.frameRate = Math.max(10, this.frameRate - 2);
    } else if (captureTime < targetCaptureTime * 0.5) {
      this.frameRate = Math.min(60, this.frameRate + 2);
    }
  }

  setDisplay(displayId) {
    const displays = screen.getAllDisplays();
    const display = displays.find(d => d.id === displayId);
    
    if (display) {
      this.currentDisplay = display;
      this.logger.info(`Switched to display: ${display.size.width}x${display.size.height}`);
      this.emit('displayChanged', display);
    } else {
      this.logger.warn('Display not found:', displayId);
    }
  }

  setQuality(quality) {
    this.compressionQuality = Math.max(10, Math.min(100, quality));
    this.logger.info('Quality set to:', this.compressionQuality);
  }

  setFrameRate(frameRate) {
    this.frameRate = Math.max(1, Math.min(60, frameRate));
    
    if (this.isCapturing) {
      // Restart capture with new frame rate
      clearInterval(this.captureInterval);
      this.captureInterval = setInterval(() => {
        this.captureFrame();
      }, 1000 / this.frameRate);
    }
    
    this.logger.info('Frame rate set to:', this.frameRate);
  }

  setFormat(format) {
    const supportedFormats = ['jpeg', 'png', 'webp'];
    
    if (supportedFormats.includes(format)) {
      this.captureFormat = format;
      this.logger.info('Capture format set to:', format);
    } else {
      this.logger.warn('Unsupported format:', format);
    }
  }

  getStats() {
    const runtime = Date.now() - (this.stats.startTime || Date.now());
    
    return {
      ...this.stats,
      runtime: runtime,
      fps: this.stats.framesSent / (runtime / 1000),
      compressionRatio: this.stats.averageFrameSize / (this.currentDisplay.size.width * this.currentDisplay.size.height * 3),
      isCapturing: this.isCapturing,
      currentSettings: {
        frameRate: this.frameRate,
        quality: this.compressionQuality,
        format: this.captureFormat,
        display: this.currentDisplay
      }
    };
  }

  getDisplays() {
    return screen.getAllDisplays().map(display => ({
      id: display.id,
      label: display.label || `Display ${display.id}`,
      bounds: display.bounds,
      size: display.size,
      scaleFactor: display.scaleFactor,
      primary: display.primary
    }));
  }

  cleanup() {
    this.logger.info('Cleaning up screen capture...');
    this.stopCapture();
    this.removeAllListeners();
    this.logger.info('Screen capture cleanup complete');
  }
}

module.exports = ScreenCapture;
