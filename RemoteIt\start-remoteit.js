#!/usr/bin/env node

// RemoteIt Startup Script
// This script starts all necessary services for testing

const { spawn } = require('child_process');
const path = require('path');
const os = require('os');

console.log('🚀 Starting RemoteIt Services...\n');

const processes = [];

function startProcess(name, command, args, options = {}) {
  console.log(`📡 Starting ${name}...`);
  
  const proc = spawn(command, args, {
    stdio: 'pipe',
    cwd: __dirname,
    ...options
  });
  
  proc.stdout.on('data', (data) => {
    console.log(`[${name}] ${data.toString().trim()}`);
  });
  
  proc.stderr.on('data', (data) => {
    console.error(`[${name} ERROR] ${data.toString().trim()}`);
  });
  
  proc.on('close', (code) => {
    console.log(`[${name}] Process exited with code ${code}`);
  });
  
  processes.push({ name, proc });
  return proc;
}

// Start Auth Server
const authServer = startProcess(
  'Auth Server',
  'node',
  ['server/auth/AuthServer.js']
);

// Wait a bit then start Relay Server
setTimeout(() => {
  const relayServer = startProcess(
    'Relay Server',
    'node',
    ['server/relay/RelayServer.js']
  );
}, 2000);

// Wait a bit more then start Electron app
setTimeout(() => {
  console.log('\n🖥️  Starting Desktop Application...');
  
  const electronApp = startProcess(
    'Desktop App',
    'npx',
    ['electron', 'src/main.js']
  );
}, 5000);

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down RemoteIt services...');
  
  processes.forEach(({ name, proc }) => {
    console.log(`   Stopping ${name}...`);
    proc.kill('SIGTERM');
  });
  
  setTimeout(() => {
    console.log('✅ All services stopped.');
    process.exit(0);
  }, 2000);
});

console.log('\n📋 Services Status:');
console.log('   Auth Server: Starting on port 3000');
console.log('   Relay Server: Starting on port 8080/8081');
console.log('   Desktop App: Starting...');
console.log('\n💡 Press Ctrl+C to stop all services');
console.log('\n🌐 Access URLs:');
console.log('   Auth API: http://localhost:3000');
console.log('   Relay API: http://localhost:8080');
console.log('   WebSocket: ws://localhost:8081');

// Keep the process alive
setInterval(() => {
  // Check if all processes are still running
  const runningProcesses = processes.filter(({ proc }) => !proc.killed);
  if (runningProcesses.length === 0) {
    console.log('All processes have stopped. Exiting...');
    process.exit(0);
  }
}, 5000);
