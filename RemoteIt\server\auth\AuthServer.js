const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const helmet = require('helmet');
const { RateLimiterMemory } = require('rate-limiter-flexible');
const crypto = require('crypto');

// Simple logger for server components
class Logger {
  constructor(module) {
    this.module = module;
  }
  
  info(message, meta = {}) {
    console.log(`[INFO] [${this.module}] ${message}`, meta);
  }
  
  error(message, meta = {}) {
    console.error(`[ERROR] [${this.module}] ${message}`, meta);
  }
  
  warn(message, meta = {}) {
    console.warn(`[WARN] [${this.module}] ${message}`, meta);
  }
  
  debug(message, meta = {}) {
    console.log(`[DEBUG] [${this.module}] ${message}`, meta);
  }
}

class AuthServer {
  constructor(options = {}) {
    this.port = options.port || 3000;
    this.jwtSecret = options.jwtSecret || process.env.JWT_SECRET || 'your-secret-key';
    this.logger = new Logger('auth-server');
    
    // In-memory storage for demo (use database in production)
    this.users = new Map();
    this.devices = new Map();
    this.sessions = new Map();
    this.refreshTokens = new Map();
    
    // Rate limiters
    this.loginLimiter = new RateLimiterMemory({
      keyGenerator: (req) => req.ip,
      points: 5, // 5 attempts
      duration: 900, // Per 15 minutes
    });
    
    this.registerLimiter = new RateLimiterMemory({
      keyGenerator: (req) => req.ip,
      points: 3, // 3 registrations
      duration: 3600, // Per hour
    });
    
    this.setupServer();
    this.createDefaultUsers();
  }

  setupServer() {
    this.app = express();
    
    // Security middleware
    this.app.use(helmet());
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
      credentials: true
    }));
    
    this.app.use(express.json({ limit: '1mb' }));
    this.app.use(express.urlencoded({ extended: true }));
    
    // Logging middleware
    this.app.use((req, res, next) => {
      this.logger.info(`${req.method} ${req.path}`, { ip: req.ip });
      next();
    });
    
    this.setupRoutes();
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'auth-server'
      });
    });

    // User registration
    this.app.post('/auth/register', async (req, res) => {
      try {
        await this.registerLimiter.consume(req.ip);
        
        const { email, password, name, deviceInfo } = req.body;
        
        if (!email || !password || !name) {
          return res.status(400).json({ error: 'Missing required fields' });
        }
        
        if (this.users.has(email)) {
          return res.status(409).json({ error: 'Email already registered' });
        }
        
        // Validate password strength
        if (password.length < 8) {
          return res.status(400).json({ error: 'Password must be at least 8 characters' });
        }
        
        // Hash password
        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(password, saltRounds);
        
        // Create user
        const userId = crypto.randomUUID();
        const user = {
          id: userId,
          email: email.toLowerCase(),
          name: name,
          passwordHash: passwordHash,
          createdAt: new Date().toISOString(),
          isActive: true,
          mfaEnabled: false,
          emailVerified: false
        };
        
        this.users.set(email.toLowerCase(), user);
        
        // Register device if provided
        if (deviceInfo) {
          await this.registerDevice(userId, deviceInfo);
        }
        
        this.logger.info('User registered:', email);
        
        res.status(201).json({
          message: 'User registered successfully',
          userId: userId
        });
        
      } catch (rejRes) {
        if (rejRes.remainingHits !== undefined) {
          res.status(429).json({ error: 'Too many registration attempts' });
        } else {
          this.logger.error('Registration error:', rejRes.message);
          res.status(500).json({ error: 'Registration failed' });
        }
      }
    });

    // User login
    this.app.post('/auth/login', async (req, res) => {
      try {
        await this.loginLimiter.consume(req.ip);
        
        const { email, password, deviceId, deviceInfo, mfaCode } = req.body;
        
        if (!email || !password) {
          return res.status(400).json({ error: 'Email and password required' });
        }
        
        const user = this.users.get(email.toLowerCase());
        if (!user || !user.isActive) {
          return res.status(401).json({ error: 'Invalid credentials' });
        }
        
        // Verify password
        const isValidPassword = await bcrypt.compare(password, user.passwordHash);
        if (!isValidPassword) {
          return res.status(401).json({ error: 'Invalid credentials' });
        }
        
        // Check MFA if enabled
        if (user.mfaEnabled && !mfaCode) {
          return res.status(403).json({ 
            error: 'MFA required',
            requiresMfa: true 
          });
        }
        
        if (user.mfaEnabled && mfaCode) {
          // In production, verify MFA code with TOTP library
          if (mfaCode !== '123456') { // Demo code
            return res.status(401).json({ error: 'Invalid MFA code' });
          }
        }
        
        // Generate tokens
        const accessToken = this.generateAccessToken(user);
        const refreshToken = this.generateRefreshToken(user);
        
        // Store refresh token
        this.refreshTokens.set(refreshToken, {
          userId: user.id,
          deviceId: deviceId,
          createdAt: Date.now(),
          expiresAt: Date.now() + (30 * 24 * 60 * 60 * 1000) // 30 days
        });
        
        // Register/update device
        if (deviceId && deviceInfo) {
          await this.registerDevice(user.id, { ...deviceInfo, id: deviceId });
        }
        
        // Create session
        const sessionId = crypto.randomUUID();
        this.sessions.set(sessionId, {
          userId: user.id,
          deviceId: deviceId,
          createdAt: Date.now(),
          lastActivity: Date.now(),
          ip: req.ip
        });
        
        this.logger.info('User logged in:', email);
        
        res.json({
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            emailVerified: user.emailVerified
          },
          accessToken: accessToken,
          refreshToken: refreshToken,
          expiresIn: 3600 // 1 hour
        });
        
      } catch (rejRes) {
        if (rejRes.remainingHits !== undefined) {
          res.status(429).json({ error: 'Too many login attempts' });
        } else {
          this.logger.error('Login error:', rejRes.message);
          res.status(500).json({ error: 'Login failed' });
        }
      }
    });

    // Token refresh
    this.app.post('/auth/refresh', async (req, res) => {
      try {
        const { refreshToken, deviceId } = req.body;
        
        if (!refreshToken) {
          return res.status(400).json({ error: 'Refresh token required' });
        }
        
        const tokenData = this.refreshTokens.get(refreshToken);
        if (!tokenData || Date.now() > tokenData.expiresAt) {
          this.refreshTokens.delete(refreshToken);
          return res.status(401).json({ error: 'Invalid or expired refresh token' });
        }
        
        const user = Array.from(this.users.values()).find(u => u.id === tokenData.userId);
        if (!user || !user.isActive) {
          return res.status(401).json({ error: 'User not found or inactive' });
        }
        
        // Generate new access token
        const accessToken = this.generateAccessToken(user);
        
        this.logger.info('Token refreshed for user:', user.email);
        
        res.json({
          accessToken: accessToken,
          expiresIn: 3600
        });
        
      } catch (error) {
        this.logger.error('Token refresh error:', error.message);
        res.status(500).json({ error: 'Token refresh failed' });
      }
    });

    // Logout
    this.app.post('/auth/logout', this.authenticateToken, async (req, res) => {
      try {
        const { deviceId } = req.body;
        
        // Remove refresh tokens for this device
        for (const [token, data] of this.refreshTokens) {
          if (data.userId === req.user.id && data.deviceId === deviceId) {
            this.refreshTokens.delete(token);
          }
        }
        
        // Remove sessions for this device
        for (const [sessionId, session] of this.sessions) {
          if (session.userId === req.user.id && session.deviceId === deviceId) {
            this.sessions.delete(sessionId);
          }
        }
        
        this.logger.info('User logged out:', req.user.email);
        
        res.json({ message: 'Logged out successfully' });
        
      } catch (error) {
        this.logger.error('Logout error:', error.message);
        res.status(500).json({ error: 'Logout failed' });
      }
    });

    // Validate token
    this.app.get('/auth/validate', this.authenticateToken, (req, res) => {
      res.json({
        valid: true,
        user: {
          id: req.user.id,
          email: req.user.email,
          name: req.user.name,
          emailVerified: req.user.emailVerified
        }
      });
    });

    // Get user devices
    this.app.get('/devices', this.authenticateToken, (req, res) => {
      const userDevices = Array.from(this.devices.values())
        .filter(device => device.userId === req.user.id);
      
      res.json({ devices: userDevices });
    });

    // Register device
    this.app.post('/devices', this.authenticateToken, async (req, res) => {
      try {
        const deviceInfo = req.body;
        const device = await this.registerDevice(req.user.id, deviceInfo);
        
        res.status(201).json({ device });
        
      } catch (error) {
        this.logger.error('Device registration error:', error.message);
        res.status(500).json({ error: 'Device registration failed' });
      }
    });

    // Update device
    this.app.put('/devices/:deviceId', this.authenticateToken, async (req, res) => {
      try {
        const { deviceId } = req.params;
        const updates = req.body;
        
        const device = this.devices.get(deviceId);
        if (!device || device.userId !== req.user.id) {
          return res.status(404).json({ error: 'Device not found' });
        }
        
        Object.assign(device, updates, { updatedAt: new Date().toISOString() });
        
        res.json({ device });
        
      } catch (error) {
        this.logger.error('Device update error:', error.message);
        res.status(500).json({ error: 'Device update failed' });
      }
    });

    // Delete device
    this.app.delete('/devices/:deviceId', this.authenticateToken, async (req, res) => {
      try {
        const { deviceId } = req.params;
        
        const device = this.devices.get(deviceId);
        if (!device || device.userId !== req.user.id) {
          return res.status(404).json({ error: 'Device not found' });
        }
        
        this.devices.delete(deviceId);
        
        res.json({ message: 'Device deleted successfully' });
        
      } catch (error) {
        this.logger.error('Device deletion error:', error.message);
        res.status(500).json({ error: 'Device deletion failed' });
      }
    });
  }

  // Middleware to authenticate JWT tokens
  authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }
    
    jwt.verify(token, this.jwtSecret, (err, user) => {
      if (err) {
        return res.status(403).json({ error: 'Invalid or expired token' });
      }
      
      req.user = user;
      next();
    });
  };

  generateAccessToken(user) {
    return jwt.sign(
      {
        id: user.id,
        email: user.email,
        name: user.name,
        emailVerified: user.emailVerified
      },
      this.jwtSecret,
      { expiresIn: '1h' }
    );
  }

  generateRefreshToken(user) {
    return jwt.sign(
      { id: user.id, type: 'refresh' },
      this.jwtSecret,
      { expiresIn: '30d' }
    );
  }

  async registerDevice(userId, deviceInfo) {
    const deviceId = deviceInfo.id || crypto.randomUUID();
    
    const device = {
      id: deviceId,
      userId: userId,
      name: deviceInfo.name || deviceInfo.hostname || 'Unknown Device',
      platform: deviceInfo.platform,
      arch: deviceInfo.arch,
      version: deviceInfo.version,
      capabilities: deviceInfo.capabilities || {},
      isOnline: false,
      registeredAt: new Date().toISOString(),
      lastSeen: new Date().toISOString(),
      unattendedAccess: false
    };
    
    this.devices.set(deviceId, device);
    
    this.logger.info('Device registered:', deviceId, device.name);
    
    return device;
  }

  createDefaultUsers() {
    // Create a default admin user for testing
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123';
    
    bcrypt.hash(adminPassword, 12).then(hash => {
      const adminUser = {
        id: crypto.randomUUID(),
        email: adminEmail,
        name: 'Administrator',
        passwordHash: hash,
        createdAt: new Date().toISOString(),
        isActive: true,
        mfaEnabled: false,
        emailVerified: true
      };
      
      this.users.set(adminEmail, adminUser);
      this.logger.info('Default admin user created:', adminEmail);
    });
  }

  start() {
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(this.port, (error) => {
        if (error) {
          reject(error);
        } else {
          this.logger.info(`Auth server started on port ${this.port}`);
          resolve();
        }
      });
    });
  }

  stop() {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          this.logger.info('Auth server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

module.exports = AuthServer;

// Start server if run directly
if (require.main === module) {
  const server = new AuthServer({
    port: process.env.PORT || 3000,
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key'
  });
  
  server.start().catch(error => {
    console.error('Failed to start auth server:', error);
    process.exit(1);
  });
  
  // Graceful shutdown
  process.on('SIGINT', async () => {
    console.log('Shutting down auth server...');
    await server.stop();
    process.exit(0);
  });
}
