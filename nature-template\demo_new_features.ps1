# Demo of New Image Generation API Features
Write-Host "DEMO: New Image Generation API Features" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Check service health
Write-Host "Checking service health..." -ForegroundColor Cyan
try {
    $health = Invoke-RestMethod -Uri "http://localhost:7777/health" -TimeoutSec 5
    Write-Host "Service Status: $($health.status)" -ForegroundColor Green
    Write-Host "Database: $($health.database)" -ForegroundColor Green
} catch {
    Write-Host "Service not available" -ForegroundColor Red
    exit 1
}

# Demo 1: Custom Dimensions
Write-Host "`nDEMO 1: Custom Dimensions (1024x1024 square)" -ForegroundColor Yellow
$body1 = @{
    type = "image"
    prompt = "Green technology innovation concept, sustainable design"
    width = 1024
    height = 1024
    timeout = 240
    priority = "high"
} | ConvertTo-Json

Write-Host "Request Body:" -ForegroundColor Cyan
Write-Host $body1 -ForegroundColor Gray

$response1 = Invoke-RestMethod -Uri "http://localhost:7777/api/generate" -Method POST -Body $body1 -ContentType "application/json"

if ($response1.success) {
    Write-Host "SUCCESS: Request ID $($response1.requestId)" -ForegroundColor Green
    Write-Host "Dimensions: $($response1.imageParameters.width)x$($response1.imageParameters.height)" -ForegroundColor Gray
    Write-Host "Timeout: $($response1.generationParameters.timeout)s" -ForegroundColor Gray
    Write-Host "Priority: $($response1.generationParameters.priority)" -ForegroundColor Gray
    
    # Wait and check status
    Write-Host "Waiting 60 seconds..." -ForegroundColor Yellow
    Start-Sleep -Seconds 60
    
    $status1 = Invoke-RestMethod -Uri "http://localhost:7777/api/status/$($response1.requestId)"
    Write-Host "Status: $($status1.status)" -ForegroundColor Cyan
    
    if ($status1.status -eq "completed") {
        Write-Host "Downloading image..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri "http://localhost:7777/api/image/$($response1.requestId)" -OutFile "images/demo-custom-dimensions.jpg"
        
        if (Test-Path "images/demo-custom-dimensions.jpg") {
            $size = (Get-Item "images/demo-custom-dimensions.jpg").Length
            Write-Host "Downloaded successfully! Size: $([math]::Round($size/1KB, 2)) KB" -ForegroundColor Green
        }
    }
}

Start-Sleep -Seconds 10

# Demo 2: Seed for Reproducibility
Write-Host "`nDEMO 2: Seed for Reproducibility (seed: 12345)" -ForegroundColor Yellow
$body2 = @{
    type = "image"
    prompt = "Nature and technology harmony, eco-friendly innovation"
    width = 800
    height = 600
    seed = 12345
    timeout = 180
    priority = "normal"
} | ConvertTo-Json

Write-Host "Request Body:" -ForegroundColor Cyan
Write-Host $body2 -ForegroundColor Gray

$response2 = Invoke-RestMethod -Uri "http://localhost:7777/api/generate" -Method POST -Body $body2 -ContentType "application/json"

if ($response2.success) {
    Write-Host "SUCCESS: Request ID $($response2.requestId)" -ForegroundColor Green
    Write-Host "Seed Used: $($response2.imageParameters.seed)" -ForegroundColor Gray
    Write-Host "This seed can be reused for identical results!" -ForegroundColor Yellow
    
    # Wait and check status
    Write-Host "Waiting 60 seconds..." -ForegroundColor Yellow
    Start-Sleep -Seconds 60
    
    $status2 = Invoke-RestMethod -Uri "http://localhost:7777/api/status/$($response2.requestId)"
    Write-Host "Status: $($status2.status)" -ForegroundColor Cyan
    
    if ($status2.status -eq "completed") {
        Write-Host "Downloading image..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri "http://localhost:7777/api/image/$($response2.requestId)" -OutFile "images/demo-with-seed.jpg"
        
        if (Test-Path "images/demo-with-seed.jpg") {
            $size = (Get-Item "images/demo-with-seed.jpg").Length
            Write-Host "Downloaded successfully! Size: $([math]::Round($size/1KB, 2)) KB" -ForegroundColor Green
        }
    }
}

Start-Sleep -Seconds 10

# Demo 3: Auto-Generated Seed
Write-Host "`nDEMO 3: Auto-Generated Seed (seed: null)" -ForegroundColor Yellow
$body3 = @{
    type = "image"
    prompt = "Sustainable future city, green architecture"
    width = 1200
    height = 800
    seed = $null
    timeout = 300
    priority = "high"
} | ConvertTo-Json

Write-Host "Request Body:" -ForegroundColor Cyan
Write-Host $body3 -ForegroundColor Gray

$response3 = Invoke-RestMethod -Uri "http://localhost:7777/api/generate" -Method POST -Body $body3 -ContentType "application/json"

if ($response3.success) {
    Write-Host "SUCCESS: Request ID $($response3.requestId)" -ForegroundColor Green
    Write-Host "Auto-Generated Seed: $($response3.imageParameters.seed)" -ForegroundColor Gray
    Write-Host "Save this seed to reproduce this exact image later!" -ForegroundColor Yellow
    
    # Wait and check status
    Write-Host "Waiting 60 seconds..." -ForegroundColor Yellow
    Start-Sleep -Seconds 60
    
    $status3 = Invoke-RestMethod -Uri "http://localhost:7777/api/status/$($response3.requestId)"
    Write-Host "Status: $($status3.status)" -ForegroundColor Cyan
    
    if ($status3.status -eq "completed") {
        Write-Host "Downloading image..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri "http://localhost:7777/api/image/$($response3.requestId)" -OutFile "images/demo-auto-seed.jpg"
        
        if (Test-Path "images/demo-auto-seed.jpg") {
            $size = (Get-Item "images/demo-auto-seed.jpg").Length
            Write-Host "Downloaded successfully! Size: $([math]::Round($size/1KB, 2)) KB" -ForegroundColor Green
        }
    }
}

Start-Sleep -Seconds 10

# Demo 4: Generate-and-Wait Endpoint
Write-Host "`nDEMO 4: Generate-and-Wait Endpoint (immediate result)" -ForegroundColor Yellow
$body4 = @{
    type = "image"
    prompt = "Green innovation laboratory, sustainable technology"
    width = 640
    height = 480
    timeout = 120
    priority = "urgent"
} | ConvertTo-Json

Write-Host "Request Body:" -ForegroundColor Cyan
Write-Host $body4 -ForegroundColor Gray
Write-Host "Using generate-and-wait endpoint..." -ForegroundColor Yellow

try {
    $response4 = Invoke-RestMethod -Uri "http://localhost:7777/api/generate-and-wait" -Method POST -Body $body4 -ContentType "application/json" -TimeoutSec 180
    
    if ($response4.success -and $response4.status -eq "completed") {
        Write-Host "SUCCESS: Image generated immediately!" -ForegroundColor Green
        Write-Host "Request ID: $($response4.requestId)" -ForegroundColor Gray
        Write-Host "Generation Time: $((Get-Date $response4.completedAt) - (Get-Date $response4.createdAt))" -ForegroundColor Gray
        
        Write-Host "Downloading image..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri "http://localhost:7777/api/image/$($response4.requestId)" -OutFile "images/demo-immediate.jpg"
        
        if (Test-Path "images/demo-immediate.jpg") {
            $size = (Get-Item "images/demo-immediate.jpg").Length
            Write-Host "Downloaded successfully! Size: $([math]::Round($size/1KB, 2)) KB" -ForegroundColor Green
        }
    }
} catch {
    Write-Host "Generate-and-wait failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Summary
Write-Host "`nDEMO SUMMARY" -ForegroundColor Green
Write-Host "=============" -ForegroundColor Green

Write-Host "New Features Demonstrated:" -ForegroundColor Cyan
Write-Host "1. Custom Dimensions (width/height 256-2048px)" -ForegroundColor White
Write-Host "2. Seed Parameter (0-2147483647 for reproducibility)" -ForegroundColor White
Write-Host "3. Auto-Generated Seeds (system creates unique seeds)" -ForegroundColor White
Write-Host "4. Timeout Control (30-600 seconds)" -ForegroundColor White
Write-Host "5. Priority Levels (low/normal/high/urgent)" -ForegroundColor White
Write-Host "6. Generate-and-Wait Endpoint (immediate results)" -ForegroundColor White

Write-Host "`nGenerated Demo Images:" -ForegroundColor Cyan
Get-ChildItem -Path "images" -Filter "demo-*.jpg" | ForEach-Object {
    $size = [math]::Round($_.Length / 1KB, 2)
    Write-Host "  $($_.Name) - $size KB" -ForegroundColor White
}

Write-Host "`nAll new API features successfully demonstrated!" -ForegroundColor Green
