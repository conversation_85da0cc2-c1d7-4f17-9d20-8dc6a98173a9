// Bus Booking Template JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initBookingForm();
    initContactForm();
    initScrollAnimations();
    initMobileMenu();
});

// Booking Form Functionality
function initBookingForm() {
    const swapBtn = document.getElementById('swap-locations');
    const fromInput = document.getElementById('from-input');
    const toInput = document.getElementById('to-input');
    const searchBtn = document.querySelector('.search-btn');
    const departureDate = document.getElementById('departure-date');

    // Set minimum date to today
    if (departureDate) {
        const today = new Date().toISOString().split('T')[0];
        departureDate.setAttribute('min', today);
        departureDate.value = today;
    }

    // Swap locations functionality
    swapBtn?.addEventListener('click', () => {
        const fromValue = fromInput.value;
        const toValue = toInput.value;
        
        fromInput.value = toValue;
        toInput.value = fromValue;
        
        // Add animation effect
        swapBtn.style.transform = 'rotate(180deg)';
        setTimeout(() => {
            swapBtn.style.transform = 'rotate(0deg)';
        }, 300);
    });

    // Search buses functionality
    searchBtn?.addEventListener('click', (e) => {
        e.preventDefault();
        
        const from = fromInput.value.trim();
        const to = toInput.value.trim();
        const date = departureDate.value;
        const passengers = document.getElementById('passengers').value;

        if (!from || !to) {
            showNotification('Please enter both departure and destination cities.', 'error');
            return;
        }

        if (from.toLowerCase() === to.toLowerCase()) {
            showNotification('Departure and destination cities cannot be the same.', 'error');
            return;
        }

        if (!date) {
            showNotification('Please select a departure date.', 'error');
            return;
        }

        // Simulate bus search
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
        searchBtn.disabled = true;

        setTimeout(() => {
            showBusResults(from, to, date, passengers);
            searchBtn.innerHTML = '<i class="fas fa-search"></i> Search Buses';
            searchBtn.disabled = false;
        }, 2000);
    });

    // Auto-complete simulation for location inputs
    [fromInput, toInput].forEach(input => {
        if (input) {
            input.addEventListener('input', debounce(handleLocationInput, 300));
        }
    });
}

function handleLocationInput(event) {
    const query = event.target.value;
    if (query.length < 2) return;

    // Simulate location suggestions
    console.log(`Searching for cities matching: ${query}`);
    // In a real app, this would call a cities API
}

function showBusResults(from, to, date, passengers) {
    // Simulate bus search results
    const buses = [
        { 
            operator: 'Express Lines', 
            departure: '08:00 AM', 
            arrival: '02:30 PM', 
            duration: '6h 30m', 
            price: 35, 
            seats: 12,
            amenities: ['WiFi', 'AC', 'Charging Port']
        },
        { 
            operator: 'Comfort Travel', 
            departure: '10:30 AM', 
            arrival: '05:15 PM', 
            duration: '6h 45m', 
            price: 32, 
            seats: 8,
            amenities: ['WiFi', 'AC', 'Snacks']
        },
        { 
            operator: 'Premium Coach', 
            departure: '02:00 PM', 
            arrival: '08:45 PM', 
            duration: '6h 45m', 
            price: 42, 
            seats: 15,
            amenities: ['WiFi', 'AC', 'Entertainment', 'Meals']
        }
    ];

    let resultsHtml = `
        <div class="bus-results-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Available Buses</h3>
                    <button class="close-modal">&times;</button>
                </div>
                <div class="trip-info">
                    <p><strong>Route:</strong> ${from} → ${to}</p>
                    <p><strong>Date:</strong> ${formatDate(date)}</p>
                    <p><strong>Passengers:</strong> ${passengers}</p>
                </div>
                <div class="bus-results">
    `;

    buses.forEach((bus, index) => {
        resultsHtml += `
            <div class="bus-option" data-index="${index}">
                <div class="bus-info">
                    <div class="bus-operator">${bus.operator}</div>
                    <div class="bus-timing">
                        <span class="departure">${bus.departure}</span>
                        <span class="duration">${bus.duration}</span>
                        <span class="arrival">${bus.arrival}</span>
                    </div>
                    <div class="bus-amenities">
                        ${bus.amenities.map(amenity => `<span class="amenity">${amenity}</span>`).join('')}
                    </div>
                </div>
                <div class="bus-booking">
                    <div class="bus-price">$${bus.price}</div>
                    <div class="seats-available">${bus.seats} seats left</div>
                    <button class="btn btn-primary select-bus">Select Seats</button>
                </div>
            </div>
        `;
    });

    resultsHtml += `
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', resultsHtml);

    // Add event listeners for the modal
    const modal = document.querySelector('.bus-results-modal');
    const closeBtn = modal.querySelector('.close-modal');
    const selectButtons = modal.querySelectorAll('.select-bus');

    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    selectButtons.forEach((btn, index) => {
        btn.addEventListener('click', () => {
            const selectedBus = buses[index];
            showSeatSelection(selectedBus, from, to, date);
            document.body.removeChild(modal);
        });
    });

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
}

function showSeatSelection(bus, from, to, date) {
    // Simulate seat selection interface
    showNotification(`Seat selection for ${bus.operator} - ${bus.departure} departure. Price: $${bus.price} per seat.`, 'info');
    
    setTimeout(() => {
        showNotification('Booking confirmed! You will receive your e-ticket via email shortly.', 'success');
    }, 3000);
}

// Contact Form Functionality
function initContactForm() {
    const contactForm = document.querySelector('.contact-form form');
    
    contactForm?.addEventListener('submit', (e) => {
        e.preventDefault();
        
        const name = contactForm.querySelector('input[type="text"]').value;
        const email = contactForm.querySelector('input[type="email"]').value;
        const topic = contactForm.querySelector('select').value;
        const message = contactForm.querySelector('textarea').value;

        if (!name || !email || !topic || !message) {
            showNotification('Please fill in all fields.', 'error');
            return;
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            showNotification('Please enter a valid email address.', 'error');
            return;
        }

        // Simulate form submission
        const submitBtn = contactForm.querySelector('.btn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Sending...';
        submitBtn.disabled = true;

        setTimeout(() => {
            showNotification('Message sent successfully! We\'ll get back to you within 24 hours.', 'success');
            contactForm.reset();
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });
}

// Scroll Animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-item, .route-card, .service-card, .step-item');
    animateElements.forEach(el => observer.observe(el));
}

// Mobile Menu
function initMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const nav = document.querySelector('.nav');

    mobileToggle?.addEventListener('click', () => {
        nav.classList.toggle('active');
        const icon = mobileToggle.querySelector('i');
        
        if (nav.classList.contains('active')) {
            icon.classList.remove('fa-bars');
            icon.classList.add('fa-times');
        } else {
            icon.classList.remove('fa-times');
            icon.classList.add('fa-bars');
        }
    });

    // Close menu when clicking on links
    const navLinks = document.querySelectorAll('.nav-list a');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            nav.classList.remove('active');
            const icon = mobileToggle.querySelector('i');
            icon.classList.remove('fa-times');
            icon.classList.add('fa-bars');
        });
    });
}

// Utility Functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '1rem 1.5rem',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '500',
        zIndex: '9999',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        maxWidth: '350px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.15)'
    });

    // Set background color based on type
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        info: '#3b82f6',
        warning: '#f59e0b'
    };
    notification.style.backgroundColor = colors[type] || colors.info;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Header scroll effect
window.addEventListener('scroll', () => {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(255, 255, 255, 0.98)';
        header.style.backdropFilter = 'blur(20px)';
    } else {
        header.style.background = 'rgba(255, 255, 255, 0.95)';
        header.style.backdropFilter = 'blur(10px)';
    }
});

// Add CSS for animations and modal
const style = document.createElement('style');
style.textContent = `
    .animate-in {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .bus-results-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
    }

    .modal-content {
        background: white;
        border-radius: 16px;
        padding: 0;
        max-width: 800px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 2rem;
        border-bottom: 1px solid #e5e7eb;
        background: #f8fafc;
        border-radius: 16px 16px 0 0;
    }

    .close-modal {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #6b7280;
    }

    .trip-info {
        padding: 1.5rem 2rem;
        background: #f8fafc;
        border-bottom: 1px solid #e5e7eb;
    }

    .bus-results {
        padding: 2rem;
    }

    .bus-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .bus-option:hover {
        border-color: #2563eb;
    }

    .bus-operator {
        font-weight: 600;
        font-size: 1.1rem;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .bus-timing {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        color: #6b7280;
    }

    .bus-amenities {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .amenity {
        background: #e0f2fe;
        color: #0369a1;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.8rem;
    }

    .bus-booking {
        text-align: center;
    }

    .bus-price {
        font-size: 1.5rem;
        font-weight: 700;
        color: #10b981;
        margin-bottom: 0.5rem;
    }

    .seats-available {
        font-size: 0.9rem;
        color: #6b7280;
        margin-bottom: 1rem;
    }

    @media (max-width: 768px) {
        .nav.active {
            display: block;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 1rem;
        }

        .nav-list {
            flex-direction: column;
            gap: 1rem;
        }

        .bus-option {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }
    }
`;
document.head.appendChild(style);
