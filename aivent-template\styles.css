/* AIvent - AI Conference Website Template Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background: #0a0a0a;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-sphere {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #8b5cf6;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #ffffff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e1b4b 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    /* Fallback pattern if image fails to load */
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ffffff" stroke-width="0.1" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 50%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(59, 130, 246, 0.3) 0%, transparent 50%);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 10, 10, 0.4);
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text {
    max-width: 600px;
}

.subtitle {
    display: block;
    font-size: 1.2rem;
    color: #8b5cf6;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #ffffff, #e5e7eb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.event-details {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.event-date,
.event-location {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #d1d5db;
    font-weight: 500;
}

.event-date i,
.event-location i {
    color: #8b5cf6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    color: #ffffff;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #ffffff;
    border: 2px solid #8b5cf6;
}

.btn-secondary:hover {
    background: #8b5cf6;
    color: #ffffff;
}

/* Countdown Section */
.countdown-section {
    background: rgba(139, 92, 246, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
}

.countdown-section h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #8b5cf6;
}

.countdown-section p {
    color: #d1d5db;
    margin-bottom: 2rem;
}

.countdown {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.countdown-item {
    text-align: center;
}

.countdown-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    color: #ffffff;
    line-height: 1;
}

.countdown-label {
    font-size: 0.9rem;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.event-location-detail {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    color: #d1d5db;
}

.event-location-detail i {
    color: #8b5cf6;
    font-size: 1.2rem;
}

.event-location-detail div {
    text-align: left;
}

.event-location-detail strong {
    display: block;
    color: #ffffff;
}

/* Logo Banner Section */
.logo-banner-section {
    padding: 3rem 0;
    background: rgba(139, 92, 246, 0.05);
    border-top: 1px solid rgba(139, 92, 246, 0.1);
    border-bottom: 1px solid rgba(139, 92, 246, 0.1);
}

.logo-banner-container {
    text-align: center;
}

.logo-banner {
    max-width: 100%;
    height: auto;
    max-height: 120px;
    filter: drop-shadow(0 4px 8px rgba(139, 92, 246, 0.3));
    transition: transform 0.3s ease;
}

.logo-banner:hover {
    transform: scale(1.05);
}

/* Section Titles */
.section-title {
    font-size: 3rem;
    font-weight: 800;
    text-align: center;
    margin-bottom: 3rem;
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* About Section */
.about {
    padding: 6rem 0;
    background: #111111;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.about-text h3 {
    font-size: 1.5rem;
    color: #ffffff;
    margin-bottom: 1rem;
}

.about-text p {
    color: #d1d5db;
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.7;
}

.about-features {
    list-style: none;
}

.about-features li {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    color: #d1d5db;
}

.about-features i {
    color: #8b5cf6;
    font-size: 1.2rem;
}

.about-image {
    position: relative;
}

.venue-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Speakers Section */
.speakers {
    padding: 6rem 0;
    background: #0a0a0a;
}

.speakers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.speaker-card {
    background: rgba(139, 92, 246, 0.05);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.speaker-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
}

.speaker-image {
    width: 120px;
    height: 120px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #8b5cf6;
}

.speaker-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.speaker-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 2rem;
}

.speaker-info h3 {
    font-size: 1.5rem;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.speaker-title {
    color: #8b5cf6;
    font-weight: 600;
    margin-bottom: 1rem;
}

.session-tag {
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
}

.speaker-session p {
    color: #d1d5db;
    line-height: 1.6;
}

/* Schedule Section */
.schedule {
    padding: 6rem 0;
    background: #111111;
}

.schedule-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.tab-btn {
    padding: 1rem 2rem;
    background: transparent;
    border: 2px solid #8b5cf6;
    color: #8b5cf6;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: #8b5cf6;
    color: #ffffff;
}

.schedule-content {
    max-width: 800px;
    margin: 0 auto;
}

.schedule-day {
    display: none;
}

.schedule-day.active {
    display: block;
}

.schedule-item {
    display: flex;
    gap: 2rem;
    padding: 2rem;
    background: rgba(139, 92, 246, 0.05);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 12px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.schedule-item:hover {
    background: rgba(139, 92, 246, 0.1);
}

.schedule-time {
    font-size: 1.2rem;
    font-weight: 700;
    color: #8b5cf6;
    min-width: 120px;
}

.schedule-details h4 {
    font-size: 1.3rem;
    color: #ffffff;
    margin-bottom: 0.5rem;
}

.schedule-details p {
    color: #d1d5db;
}

/* Tickets Section */
.tickets {
    padding: 6rem 0;
    background: #0a0a0a;
}

.tickets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.ticket-card {
    background: rgba(139, 92, 246, 0.05);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 16px;
    padding: 2.5rem 2rem;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
}

.ticket-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
}

.ticket-card.featured {
    border: 2px solid #8b5cf6;
    background: rgba(139, 92, 246, 0.1);
}

.ticket-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    color: #ffffff;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.ticket-card h3 {
    font-size: 1.8rem;
    color: #ffffff;
    margin-bottom: 1rem;
}

.ticket-price {
    font-size: 3rem;
    font-weight: 800;
    color: #8b5cf6;
    margin-bottom: 2rem;
}

.ticket-features {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
}

.ticket-features li {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    color: #d1d5db;
}

.ticket-features i {
    color: #8b5cf6;
    font-size: 1.1rem;
}

/* Footer */
.footer {
    background: #111111;
    padding: 4rem 0 2rem;
    border-top: 1px solid rgba(139, 92, 246, 0.2);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: #ffffff;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.footer-section p,
.footer-section li {
    color: #d1d5db;
    margin-bottom: 0.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #8b5cf6;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8b5cf6;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #8b5cf6;
    color: #ffffff;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(139, 92, 246, 0.1);
    color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .speakers-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(10, 10, 10, 0.98);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .event-details {
        flex-direction: column;
        gap: 1rem;
    }

    .countdown {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .countdown-number {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .about-text h2 {
        font-size: 2.5rem;
    }

    .speakers-grid {
        grid-template-columns: 1fr;
    }

    .schedule-tabs {
        flex-direction: column;
        align-items: center;
    }

    .schedule-item {
        flex-direction: column;
        gap: 1rem;
    }

    .schedule-time {
        min-width: auto;
    }

    .tickets-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .countdown-section {
        padding: 1.5rem;
    }

    .countdown {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .speaker-card,
    .ticket-card {
        padding: 1.5rem;
    }

    .ticket-price {
        font-size: 2.5rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.hero-text,
.countdown-section {
    animation: fadeInUp 1s ease-out;
}

.countdown-number {
    animation: pulse 2s infinite;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Focus States for Accessibility */
.btn:focus,
.tab-btn:focus,
.nav-menu a:focus {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .hero-background::before {
        background: none;
    }

    .countdown-section,
    .speaker-card,
    .ticket-card {
        border-width: 2px;
    }
}
