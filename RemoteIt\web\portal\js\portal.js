// RemoteIt Web Portal - Main Application
class RemoteItPortal {
  constructor() {
    this.apiBaseUrl = 'http://localhost:3000';
    this.relayUrl = 'ws://localhost:8081';
    this.currentUser = null;
    this.accessToken = null;
    this.devices = [];
    this.activeSessions = [];
    this.socket = null;
    
    this.init();
  }

  async init() {
    console.log('Initializing RemoteIt Portal...');
    
    // Show loading screen
    this.showLoading();
    
    try {
      // Check if user is already authenticated
      const token = localStorage.getItem('remoteit_token');
      if (token) {
        this.accessToken = token;
        const isValid = await this.validateToken();
        
        if (isValid) {
          await this.loadUserData();
          this.showApp();
        } else {
          this.showLogin();
        }
      } else {
        this.showLogin();
      }
      
      // Setup event listeners
      this.setupEventListeners();
      
    } catch (error) {
      console.error('Initialization failed:', error);
      this.showNotification('Failed to initialize application', 'error');
      this.showLogin();
    }
  }

  showLoading() {
    document.getElementById('loading-screen').classList.remove('hidden');
    document.getElementById('login-screen').classList.add('hidden');
    document.getElementById('app').classList.add('hidden');
  }

  showLogin() {
    document.getElementById('loading-screen').classList.add('hidden');
    document.getElementById('login-screen').classList.remove('hidden');
    document.getElementById('app').classList.add('hidden');
  }

  showApp() {
    document.getElementById('loading-screen').classList.add('hidden');
    document.getElementById('login-screen').classList.add('hidden');
    document.getElementById('app').classList.remove('hidden');
  }

  setupEventListeners() {
    // Login form
    document.getElementById('login-form').addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleLogin();
    });

    // Logout
    document.getElementById('logout-link').addEventListener('click', (e) => {
      e.preventDefault();
      this.handleLogout();
    });

    // Quick connect
    document.getElementById('quick-connect-btn').addEventListener('click', () => {
      this.handleQuickConnect();
    });

    // Add device
    document.getElementById('add-device-btn').addEventListener('click', () => {
      this.showAddDeviceModal();
    });

    // Refresh devices
    document.getElementById('refresh-devices-btn').addEventListener('click', () => {
      this.loadDevices();
    });

    // View toggle
    document.querySelectorAll('.view-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.toggleView(e.target.dataset.view);
      });
    });

    // Device search
    document.getElementById('device-search').addEventListener('input', (e) => {
      this.filterDevices(e.target.value);
    });

    // Modal close buttons
    document.querySelectorAll('.modal-close, #modal-close-btn, #modal-cancel-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        this.closeModals();
      });
    });

    // Connect button in modal
    document.getElementById('modal-connect-btn').addEventListener('click', () => {
      this.handleModalConnect();
    });
  }

  async handleLogin() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const mfaCode = document.getElementById('mfa-code').value;

    if (!email || !password) {
      this.showLoginError('Please enter email and password');
      return;
    }

    this.setLoginLoading(true);

    try {
      const response = await fetch(`${this.apiBaseUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email,
          password,
          mfaCode,
          deviceId: this.getDeviceId(),
          deviceInfo: this.getDeviceInfo()
        })
      });

      const data = await response.json();

      if (response.ok) {
        this.accessToken = data.accessToken;
        this.currentUser = data.user;
        
        localStorage.setItem('remoteit_token', data.accessToken);
        localStorage.setItem('remoteit_refresh_token', data.refreshToken);
        
        await this.loadUserData();
        this.showApp();
        this.showNotification('Successfully signed in', 'success');
        
      } else {
        if (data.requiresMfa) {
          document.getElementById('mfa-group').style.display = 'block';
          this.showLoginError('Please enter your MFA code');
        } else {
          this.showLoginError(data.error || 'Login failed');
        }
      }
      
    } catch (error) {
      console.error('Login error:', error);
      this.showLoginError('Login failed. Please check your connection.');
    }

    this.setLoginLoading(false);
  }

  async handleLogout() {
    try {
      if (this.accessToken) {
        await fetch(`${this.apiBaseUrl}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            deviceId: this.getDeviceId()
          })
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    }

    // Clear local storage
    localStorage.removeItem('remoteit_token');
    localStorage.removeItem('remoteit_refresh_token');
    
    // Reset state
    this.accessToken = null;
    this.currentUser = null;
    this.devices = [];
    this.activeSessions = [];
    
    // Disconnect socket
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    
    this.showLogin();
    this.showNotification('Successfully signed out', 'success');
  }

  async validateToken() {
    try {
      const response = await fetch(`${this.apiBaseUrl}/auth/validate`, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        this.currentUser = data.user;
        return true;
      }
      
      return false;
      
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  async loadUserData() {
    // Update user info in UI
    document.getElementById('user-name').textContent = this.currentUser.name;
    
    // Load devices
    await this.loadDevices();
    
    // Connect to relay server
    this.connectToRelay();
  }

  async loadDevices() {
    try {
      const response = await fetch(`${this.apiBaseUrl}/devices`, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        this.devices = data.devices;
        this.renderDevices();
      } else {
        throw new Error('Failed to load devices');
      }
      
    } catch (error) {
      console.error('Failed to load devices:', error);
      this.showNotification('Failed to load devices', 'error');
    }
  }

  renderDevices() {
    const container = document.getElementById('devices-container');
    const noDevices = document.getElementById('no-devices');
    
    if (this.devices.length === 0) {
      container.classList.add('hidden');
      noDevices.classList.remove('hidden');
      return;
    }
    
    container.classList.remove('hidden');
    noDevices.classList.add('hidden');
    
    container.innerHTML = '';
    
    this.devices.forEach(device => {
      const deviceElement = this.createDeviceElement(device);
      container.appendChild(deviceElement);
    });
  }

  createDeviceElement(device) {
    const div = document.createElement('div');
    div.className = 'device-card';
    div.dataset.deviceId = device.id;
    
    div.innerHTML = `
      <div class="device-header">
        <div class="device-icon">
          <i class="fas fa-${this.getDeviceIcon(device.platform)}"></i>
        </div>
        <div class="device-info">
          <h3>${device.name}</h3>
          <span class="device-status ${device.isOnline ? 'online' : 'offline'}">
            ${device.isOnline ? 'Online' : 'Offline'}
          </span>
        </div>
      </div>
      <div class="device-details">
        <div class="device-detail">
          <span class="label">Platform:</span>
          <span class="value">${device.platform}</span>
        </div>
        <div class="device-detail">
          <span class="label">Last Seen:</span>
          <span class="value">${this.formatDate(device.lastSeen)}</span>
        </div>
      </div>
      <div class="device-actions">
        <button class="btn btn-primary connect-btn" ${!device.isOnline ? 'disabled' : ''}>
          <i class="fas fa-link"></i>
          Connect
        </button>
        <button class="btn btn-secondary">
          <i class="fas fa-cog"></i>
          Settings
        </button>
      </div>
    `;
    
    // Add click handler for connect button
    const connectBtn = div.querySelector('.connect-btn');
    connectBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.showConnectionModal(device);
    });
    
    return div;
  }

  getDeviceIcon(platform) {
    switch (platform?.toLowerCase()) {
      case 'windows': return 'windows';
      case 'macos': case 'darwin': return 'apple';
      case 'linux': return 'linux';
      case 'android': return 'android';
      case 'ios': return 'apple';
      default: return 'desktop';
    }
  }

  formatDate(dateString) {
    if (!dateString) return 'Never';
    
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    
    return date.toLocaleDateString();
  }

  showConnectionModal(device) {
    document.getElementById('modal-device-name').textContent = device.name;
    document.getElementById('modal-device-info').textContent = 
      `${device.platform} • Last seen ${this.formatDate(device.lastSeen)}`;
    
    document.getElementById('connection-modal').classList.remove('hidden');
    document.getElementById('connection-modal').dataset.deviceId = device.id;
  }

  handleModalConnect() {
    const deviceId = document.getElementById('connection-modal').dataset.deviceId;
    const connectionType = document.querySelector('input[name="connection-type"]:checked').value;
    
    this.connectToDevice(deviceId, connectionType);
    this.closeModals();
  }

  async connectToDevice(deviceId, connectionType = 'control') {
    try {
      this.showNotification('Initiating connection...', 'info');
      
      // This would initiate the WebRTC connection through the relay server
      // For now, we'll simulate the connection
      setTimeout(() => {
        this.showRemoteViewer(deviceId);
        this.showNotification('Connected successfully', 'success');
      }, 2000);
      
    } catch (error) {
      console.error('Connection failed:', error);
      this.showNotification('Connection failed', 'error');
    }
  }

  showRemoteViewer(deviceId) {
    const device = this.devices.find(d => d.id === deviceId);
    if (!device) return;
    
    document.getElementById('viewer-device-name').textContent = device.name;
    document.getElementById('viewer-status').textContent = 'Connected';
    document.getElementById('remote-viewer').classList.remove('hidden');
    
    // Setup viewer controls
    this.setupViewerControls();
  }

  setupViewerControls() {
    document.getElementById('disconnect-btn').addEventListener('click', () => {
      this.disconnectViewer();
    });
    
    document.getElementById('fullscreen-btn').addEventListener('click', () => {
      this.toggleFullscreen();
    });
  }

  disconnectViewer() {
    document.getElementById('remote-viewer').classList.add('hidden');
    this.showNotification('Disconnected', 'info');
  }

  toggleFullscreen() {
    const viewer = document.getElementById('remote-viewer');
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      viewer.requestFullscreen();
    }
  }

  connectToRelay() {
    try {
      this.socket = new WebSocket(this.relayUrl);
      
      this.socket.onopen = () => {
        console.log('Connected to relay server');
        
        // Register as web client
        this.socket.send(JSON.stringify({
          type: 'register',
          deviceId: this.getDeviceId(),
          deviceInfo: {
            ...this.getDeviceInfo(),
            type: 'web-client'
          }
        }));
      };
      
      this.socket.onmessage = (event) => {
        this.handleRelayMessage(JSON.parse(event.data));
      };
      
      this.socket.onclose = () => {
        console.log('Disconnected from relay server');
        // Attempt to reconnect after 5 seconds
        setTimeout(() => this.connectToRelay(), 5000);
      };
      
      this.socket.onerror = (error) => {
        console.error('Relay connection error:', error);
      };
      
    } catch (error) {
      console.error('Failed to connect to relay:', error);
    }
  }

  handleRelayMessage(message) {
    switch (message.type) {
      case 'registered':
        console.log('Registered with relay server');
        break;
        
      case 'deviceStatusUpdate':
        this.updateDeviceStatus(message.deviceId, message.isOnline);
        break;
        
      case 'connectionRequest':
        this.handleConnectionRequest(message);
        break;
        
      default:
        console.log('Unknown relay message:', message);
    }
  }

  updateDeviceStatus(deviceId, isOnline) {
    const device = this.devices.find(d => d.id === deviceId);
    if (device) {
      device.isOnline = isOnline;
      this.renderDevices();
    }
  }

  getDeviceId() {
    let deviceId = localStorage.getItem('remoteit_device_id');
    if (!deviceId) {
      deviceId = 'web-' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('remoteit_device_id', deviceId);
    }
    return deviceId;
  }

  getDeviceInfo() {
    return {
      name: 'Web Browser',
      platform: 'web',
      userAgent: navigator.userAgent,
      capabilities: {
        screenSharing: false,
        fileTransfer: true,
        remoteControl: false
      }
    };
  }

  closeModals() {
    document.querySelectorAll('.modal').forEach(modal => {
      modal.classList.add('hidden');
    });
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
      <div style="display: flex; align-items: center; gap: 8px;">
        <i class="fas fa-${this.getNotificationIcon(type)}"></i>
        <span>${message}</span>
      </div>
    `;
    
    document.getElementById('notifications').appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      notification.remove();
    }, 5000);
  }

  getNotificationIcon(type) {
    switch (type) {
      case 'success': return 'check-circle';
      case 'error': return 'exclamation-circle';
      case 'warning': return 'exclamation-triangle';
      default: return 'info-circle';
    }
  }

  setLoginLoading(loading) {
    const btn = document.getElementById('login-btn');
    const text = btn.querySelector('.btn-text');
    const spinner = btn.querySelector('.btn-spinner');
    
    if (loading) {
      text.classList.add('hidden');
      spinner.classList.remove('hidden');
      btn.disabled = true;
    } else {
      text.classList.remove('hidden');
      spinner.classList.add('hidden');
      btn.disabled = false;
    }
  }

  showLoginError(message) {
    const errorElement = document.getElementById('login-error');
    errorElement.textContent = message;
    errorElement.classList.remove('hidden');
  }

  // Placeholder methods for additional features
  handleQuickConnect() {
    const deviceId = document.getElementById('quick-connect-input').value.trim();
    if (deviceId) {
      this.connectToDevice(deviceId);
    }
  }

  showAddDeviceModal() {
    this.showNotification('Download RemoteIt app to add devices', 'info');
  }

  toggleView(view) {
    document.querySelectorAll('.view-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-view="${view}"]`).classList.add('active');
    
    const container = document.getElementById('devices-container');
    container.className = view === 'grid' ? 'devices-grid' : 'devices-list';
  }

  filterDevices(query) {
    const devices = document.querySelectorAll('.device-card');
    devices.forEach(device => {
      const name = device.querySelector('h3').textContent.toLowerCase();
      const visible = name.includes(query.toLowerCase());
      device.style.display = visible ? 'block' : 'none';
    });
  }
}

// Initialize the portal when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.portal = new RemoteItPortal();
});
