const request = require('supertest');
const { expect } = require('chai');
const AuthServer = require('../../server/auth/AuthServer');

describe('Authentication Integration Tests', () => {
  let authServer;
  let app;
  let testUser;

  before(async () => {
    // Start auth server for testing
    authServer = new AuthServer({
      port: 3001,
      jwtSecret: 'test-secret'
    });
    
    await authServer.start();
    app = authServer.app;
    
    // Create test user
    testUser = {
      email: '<EMAIL>',
      password: 'testpassword123',
      name: 'Test User'
    };
  });

  after(async () => {
    if (authServer) {
      await authServer.stop();
    }
  });

  describe('User Registration', () => {
    it('should register a new user successfully', async () => {
      const response = await request(app)
        .post('/auth/register')
        .send(testUser)
        .expect(201);

      expect(response.body).to.have.property('userId');
      expect(response.body.message).to.equal('User registered successfully');
    });

    it('should reject registration with existing email', async () => {
      await request(app)
        .post('/auth/register')
        .send(testUser)
        .expect(409);
    });

    it('should reject registration with weak password', async () => {
      await request(app)
        .post('/auth/register')
        .send({
          email: '<EMAIL>',
          password: '123',
          name: 'Weak User'
        })
        .expect(400);
    });

    it('should reject registration with missing fields', async () => {
      await request(app)
        .post('/auth/register')
        .send({
          email: '<EMAIL>'
        })
        .expect(400);
    });
  });

  describe('User Login', () => {
    let accessToken;
    let refreshToken;

    it('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password,
          deviceId: 'test-device-1',
          deviceInfo: {
            name: 'Test Device',
            platform: 'test',
            version: '1.0.0'
          }
        })
        .expect(200);

      expect(response.body).to.have.property('user');
      expect(response.body).to.have.property('accessToken');
      expect(response.body).to.have.property('refreshToken');
      expect(response.body.user.email).to.equal(testUser.email);

      accessToken = response.body.accessToken;
      refreshToken = response.body.refreshToken;
    });

    it('should reject login with invalid credentials', async () => {
      await request(app)
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword'
        })
        .expect(401);
    });

    it('should reject login with non-existent user', async () => {
      await request(app)
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(401);
    });

    it('should validate access token', async () => {
      const response = await request(app)
        .get('/auth/validate')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.valid).to.be.true;
      expect(response.body.user.email).to.equal(testUser.email);
    });

    it('should refresh access token', async () => {
      const response = await request(app)
        .post('/auth/refresh')
        .send({
          refreshToken: refreshToken,
          deviceId: 'test-device-1'
        })
        .expect(200);

      expect(response.body).to.have.property('accessToken');
      expect(response.body).to.have.property('expiresIn');
    });

    it('should logout successfully', async () => {
      await request(app)
        .post('/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          deviceId: 'test-device-1'
        })
        .expect(200);
    });
  });

  describe('Device Management', () => {
    let accessToken;

    before(async () => {
      // Login to get access token
      const response = await request(app)
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password,
          deviceId: 'test-device-2'
        });
      
      accessToken = response.body.accessToken;
    });

    it('should register a device', async () => {
      const deviceInfo = {
        id: 'test-device-3',
        name: 'Test Device 3',
        platform: 'windows',
        arch: 'x64',
        version: '1.0.0',
        capabilities: {
          screenSharing: true,
          fileTransfer: true
        }
      };

      const response = await request(app)
        .post('/devices')
        .set('Authorization', `Bearer ${accessToken}`)
        .send(deviceInfo)
        .expect(201);

      expect(response.body.device).to.have.property('id');
      expect(response.body.device.name).to.equal(deviceInfo.name);
    });

    it('should get user devices', async () => {
      const response = await request(app)
        .get('/devices')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).to.have.property('devices');
      expect(response.body.devices).to.be.an('array');
      expect(response.body.devices.length).to.be.greaterThan(0);
    });

    it('should update device information', async () => {
      // First get devices to find one to update
      const devicesResponse = await request(app)
        .get('/devices')
        .set('Authorization', `Bearer ${accessToken}`);

      const deviceId = devicesResponse.body.devices[0].id;

      const updateData = {
        name: 'Updated Device Name',
        unattendedAccess: true
      };

      const response = await request(app)
        .put(`/devices/${deviceId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.device.name).to.equal(updateData.name);
      expect(response.body.device.unattendedAccess).to.be.true;
    });

    it('should delete a device', async () => {
      // First get devices to find one to delete
      const devicesResponse = await request(app)
        .get('/devices')
        .set('Authorization', `Bearer ${accessToken}`);

      const deviceId = devicesResponse.body.devices[0].id;

      await request(app)
        .delete(`/devices/${deviceId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);
    });
  });

  describe('Security Tests', () => {
    it('should reject requests without authentication', async () => {
      await request(app)
        .get('/devices')
        .expect(401);
    });

    it('should reject requests with invalid token', async () => {
      await request(app)
        .get('/devices')
        .set('Authorization', 'Bearer invalid-token')
        .expect(403);
    });

    it('should handle rate limiting', async () => {
      // Make multiple rapid requests to trigger rate limiting
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(
          request(app)
            .post('/auth/login')
            .send({
              email: '<EMAIL>',
              password: 'wrongpassword'
            })
        );
      }

      const responses = await Promise.all(promises);
      
      // At least one should be rate limited
      const rateLimited = responses.some(response => response.status === 429);
      expect(rateLimited).to.be.true;
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed JSON', async () => {
      await request(app)
        .post('/auth/login')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);
    });

    it('should handle missing content-type', async () => {
      await request(app)
        .post('/auth/login')
        .send('email=<EMAIL>&password=test')
        .expect(400);
    });

    it('should return proper error format', async () => {
      const response = await request(app)
        .post('/auth/login')
        .send({
          email: 'invalid-email',
          password: 'test'
        })
        .expect(400);

      expect(response.body).to.have.property('error');
      expect(response.body.error).to.be.a('string');
    });
  });

  describe('Health Check', () => {
    it('should return healthy status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).to.have.property('status');
      expect(response.body.status).to.equal('healthy');
      expect(response.body).to.have.property('timestamp');
      expect(response.body).to.have.property('service');
    });
  });
});
