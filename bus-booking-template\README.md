# BusGo - Bus Booking Template

A comprehensive bus booking platform with route search, seat selection, and ticket management functionality.

## Features

- **Route Search & Booking** - Interactive search with departure/destination cities
- **Seat Selection Interface** - Visual seat map for choosing preferred seats
- **Payment Processing** - Secure booking and payment simulation
- **Ticket Management** - Digital ticket generation and management
- **Travel Tracking** - Real-time bus tracking and updates
- **Responsive Design** - Mobile-first approach for all devices

## Design

- **Color Scheme**: Professional blue and green travel colors
- **Typography**: Inter font family for modern readability
- **Layout**: Clean, functional design focused on booking flow
- **Interactive Elements**: Route search with modal results

## Files

- `web/index.html` - Main bus booking website
- `css/style.css` - Complete styling with travel theme (400+ lines)
- `js/main.js` - Interactive booking system (400+ lines)
- `about.txt` - Comprehensive project documentation

## JavaScript Features

- **Interactive Booking Form** - Route search with validation
- **Location Swap** - Easy departure/destination switching
- **Bus Results Modal** - Detailed bus options with amenities
- **Seat Selection Simulation** - Interactive seat booking process
- **Form Validation** - Complete input validation and error handling
- **Responsive Navigation** - Mobile-friendly menu system

## Usage

1. Open `web/index.html` in your browser
2. Test the route search functionality
3. Explore bus options and booking flow
4. Customize with your bus routes and operators
5. Update pricing and service information

## Customization

- Update bus company branding and colors
- Modify available routes and destinations
- Customize bus types and amenities
- Add real-time API integrations
- Connect to payment processing systems

Perfect for bus companies, transportation services, and travel booking platforms.
