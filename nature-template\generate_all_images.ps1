# Generate all remaining images for Nature Tech template
Write-Host "NATURE TECH - COMPREHENSIVE IMAGE GENERATION" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

# Define all images needed
$images = @(
    @{
        name = "tech-nature-hero"
        filename = "tech-nature-hero.jpg"
        prompt = "Technology and nature integration, modern sustainable innovation, green tech solutions, professional design, high quality"
    },
    @{
        name = "green-lab"
        filename = "green-lab.jpg"
        prompt = "Green innovation laboratory, sustainable technology research, eco-friendly equipment, modern clean environment"
    },
    @{
        name = "renewable-energy"
        filename = "renewable-energy.jpg"
        prompt = "Renewable energy solutions, solar panels, wind turbines, clean energy technology, sustainable power generation"
    },
    @{
        name = "eco-architecture"
        filename = "eco-architecture.jpg"
        prompt = "Eco-friendly architecture, green building design, sustainable construction, environmental architecture"
    },
    @{
        name = "smart-city"
        filename = "smart-city.jpg"
        prompt = "Smart sustainable city, green urban planning, eco-friendly infrastructure, future city design"
    },
    @{
        name = "bio-technology"
        filename = "bio-technology.jpg"
        prompt = "Biotechnology innovation, green science, sustainable research, environmental technology solutions"
    }
)

$successCount = 0
$failCount = 0

foreach ($img in $images) {
    Write-Host "`nGenerating: $($img.name)" -ForegroundColor Cyan
    Write-Host "Filename: $($img.filename)" -ForegroundColor Yellow
    Write-Host "Prompt: $($img.prompt)" -ForegroundColor Gray
    
    # Check if already exists
    $outputPath = "images\$($img.filename)"
    if (Test-Path $outputPath) {
        Write-Host "Already exists, skipping..." -ForegroundColor Yellow
        continue
    }
    
    try {
        # Run the single image generator
        $result = & .\generate_one_image.ps1 -ImageName $img.name -Filename $img.filename -Prompt $img.prompt
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "SUCCESS: $($img.name)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "FAILED: $($img.name)" -ForegroundColor Red
            $failCount++
        }
    } catch {
        Write-Host "ERROR: $($img.name) - $($_.Exception.Message)" -ForegroundColor Red
        $failCount++
    }

    # Wait between requests
    Write-Host "Waiting 15 seconds before next image..." -ForegroundColor Cyan
    Start-Sleep -Seconds 15
}

# Final summary
Write-Host "`nFINAL SUMMARY" -ForegroundColor Green
Write-Host "=================" -ForegroundColor Green
Write-Host "Successful: $successCount" -ForegroundColor Green
Write-Host "Failed: $failCount" -ForegroundColor Red
Write-Host "Total Images: $($images.Count)" -ForegroundColor Cyan

# List all images in directory
Write-Host "`nCurrent Images in Directory:" -ForegroundColor Cyan
Get-ChildItem -Path "images" -Filter "*.jpg" | ForEach-Object {
    $size = [math]::Round($_.Length / 1KB, 2)
    Write-Host "  $($_.Name) - $size KB" -ForegroundColor White
}

Write-Host "`nImage generation process completed!" -ForegroundColor Green
