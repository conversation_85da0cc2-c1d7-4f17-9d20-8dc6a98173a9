#!/bin/bash

# RemoteIt Simple Cloud Deployment Script
# Deploys RemoteIt to a single VPS for testing

echo "🚀 RemoteIt Simple Cloud Deployment"
echo "=================================="

# Configuration
SERVER_IP=${1:-"your-server-ip"}
DOMAIN=${2:-"remoteit.yourdomain.com"}

if [ "$SERVER_IP" = "your-server-ip" ]; then
    echo "Usage: ./deploy-simple-cloud.sh <server-ip> [domain]"
    echo "Example: ./deploy-simple-cloud.sh ************* remoteit.example.com"
    exit 1
fi

echo "Server IP: $SERVER_IP"
echo "Domain: $DOMAIN"
echo ""

# VPS Requirements Check
echo "📋 VPS Requirements:"
echo "   - Ubuntu 20.04+ or CentOS 8+"
echo "   - 2+ CPU cores"
echo "   - 4+ GB RAM" 
echo "   - 50+ GB storage"
echo "   - Public IP address"
echo "   - Ports 80, 443, 3000, 8080, 8081, 3478 open"
echo ""

read -p "Continue with deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    exit 1
fi

# Generate deployment package
echo "📦 Creating deployment package..."
tar -czf remoteit-deploy.tar.gz \
    --exclude=node_modules \
    --exclude=.git \
    --exclude=dist \
    --exclude=build \
    server/ \
    config.js \
    package.json \
    README.md

echo "✅ Deployment package created: remoteit-deploy.tar.gz"

# Generate deployment script for VPS
cat > deploy-on-server.sh << 'EOF'
#!/bin/bash

echo "🔧 Setting up RemoteIt on VPS..."

# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PostgreSQL
sudo apt install -y postgresql postgresql-contrib

# Install Redis
sudo apt install -y redis-server

# Install nginx (for reverse proxy)
sudo apt install -y nginx

# Install PM2 for process management
sudo npm install -g pm2

# Create remoteit user
sudo useradd -m -s /bin/bash remoteit
sudo usermod -aG sudo remoteit

# Setup application directory
sudo mkdir -p /opt/remoteit
sudo chown remoteit:remoteit /opt/remoteit

# Extract application
sudo -u remoteit tar -xzf remoteit-deploy.tar.gz -C /opt/remoteit

# Install dependencies
cd /opt/remoteit
sudo -u remoteit npm install --production

# Setup PostgreSQL database
sudo -u postgres createuser remoteit
sudo -u postgres createdb remoteit_db -O remoteit
sudo -u postgres psql -c "ALTER USER remoteit PASSWORD 'secure_password_123';"

# Configure Redis
sudo systemctl enable redis-server
sudo systemctl start redis-server

# Create environment file
sudo -u remoteit cat > /opt/remoteit/.env << ENVEOF
NODE_ENV=production
PORT=3000
RELAY_PORT=8080
WEBSOCKET_PORT=8081
STUN_PORT=3478

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=remoteit_db
DB_USER=remoteit
DB_PASS=secure_password_123

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Security
JWT_SECRET=$(openssl rand -base64 32)
ENCRYPTION_KEY=$(openssl rand -base64 32)

# Server
SERVER_IP=$1
DOMAIN=$2
ENVEOF

# Create PM2 ecosystem file
sudo -u remoteit cat > /opt/remoteit/ecosystem.config.js << PMEOF
module.exports = {
  apps: [
    {
      name: 'remoteit-auth',
      script: 'server/auth/AuthServer.js',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      }
    },
    {
      name: 'remoteit-relay',
      script: 'server/relay/RelayServer.js',
      env: {
        NODE_ENV: 'production',
        PORT: 8080,
        WS_PORT: 8081
      }
    },
    {
      name: 'remoteit-stun',
      script: 'server/stun/STUNServer.js',
      env: {
        NODE_ENV: 'production',
        PORT: 3478
      }
    }
  ]
};
PMEOF

# Start services with PM2
cd /opt/remoteit
sudo -u remoteit pm2 start ecosystem.config.js
sudo -u remoteit pm2 save
sudo pm2 startup

# Configure nginx reverse proxy
sudo cat > /etc/nginx/sites-available/remoteit << NGINXEOF
server {
    listen 80;
    server_name $2;

    # API Server
    location /api/ {
        proxy_pass http://localhost:3000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # Relay Server WebSocket
    location /ws/ {
        proxy_pass http://localhost:8081/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # Health check
    location /health {
        proxy_pass http://localhost:8080/health;
    }
}
NGINXEOF

# Enable nginx site
sudo ln -sf /etc/nginx/sites-available/remoteit /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t && sudo systemctl reload nginx

# Configure firewall
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3478/udp
sudo ufw --force enable

echo "✅ RemoteIt deployed successfully!"
echo ""
echo "🌐 Service URLs:"
echo "   API: http://$2/api/"
echo "   WebSocket: ws://$2/ws/"
echo "   Health: http://$2/health"
echo ""
echo "📋 Next steps:"
echo "   1. Point domain $2 to this server IP: $1"
echo "   2. Install SSL certificate: sudo certbot --nginx -d $2"
echo "   3. Update client config to use: $2"
echo ""
echo "🔧 Management commands:"
echo "   pm2 status          - Check service status"
echo "   pm2 logs            - View logs"
echo "   pm2 restart all     - Restart services"
echo "   sudo systemctl status nginx - Check nginx"

EOF

chmod +x deploy-on-server.sh

echo ""
echo "📋 Deployment Instructions:"
echo "=========================="
echo ""
echo "1. Copy files to your VPS:"
echo "   scp remoteit-deploy.tar.gz deploy-on-server.sh user@$SERVER_IP:~/"
echo ""
echo "2. Run deployment on VPS:"
echo "   ssh user@$SERVER_IP"
echo "   chmod +x deploy-on-server.sh"
echo "   ./deploy-on-server.sh $SERVER_IP $DOMAIN"
echo ""
echo "3. Point domain to server:"
echo "   Create A record: $DOMAIN -> $SERVER_IP"
echo ""
echo "4. Install SSL certificate:"
echo "   sudo apt install certbot python3-certbot-nginx"
echo "   sudo certbot --nginx -d $DOMAIN"
echo ""
echo "5. Update client configuration:"
echo "   REMOTEIT_API_URL=https://$DOMAIN/api"
echo "   REMOTEIT_RELAY_URL=wss://$DOMAIN/ws"
echo ""

# Cleanup
rm -f deploy-on-server.sh

echo "🎉 Deployment package ready!"
echo "   Package: remoteit-deploy.tar.gz"
echo "   Follow the instructions above to deploy to your VPS."
