Learn, get trained and remember it for your future ui/ux making usage:
1. https://preview.themeforest.net/item/oneuiux-creative-multipurpose-mobile-html-template/full_screen_preview/26757994?_ga=2.94086237.1785270743.1753092745-572956060.1753092745
2. https://preview.themeforest.net/item/max-solaris-responsive-tablet-and-mobile-html-template/full_screen_preview/17127123?_ga=2.30575743.1785270743.1753092745-572956060.1753092745
3. https://maxartkiller.com/website/adminuxmobile2/index.html
4. https://wholesale1-demo.myshopify.com/
5. https://templates.sanstive.com/sijoner/template-kit/home/
6. https://cs-oars-1.myshopify.com/
7. https://cs-oars-2.myshopify.com/
8. https://cs-oars-3.myshopify.com/
9. https://cs-oars-4.myshopify.com/
10. https://cs-oars-6.myshopify.com/
11. https://cs-oars-7.myshopify.com/
12. https://elessi2.myshopify.com/
13. https://live.21lab.co/kiraz/
14. https://live.21lab.co/kiraz/home-2/
15. https://live.21lab.co/kiraz/home-3/
16. https://live.21lab.co/kiraz/home-7/
17. https://live.21lab.co/kiraz/home-9/
18. https://marveltheme.com/tf/html/bentofolio-html/index.html
19. https://ecomartstw.baseecom.com/main-files/index.html
20. https://ecomartstw.baseecom.com/main-files/index-2.html
21. https://ecomartstw.baseecom.com/main-files/index-3.html
22. https://ecomartstw.baseecom.com/main-files/index-4.html
23. https://ecomartstw.baseecom.com/main-files/index-5.html
24. https://ecomartstw.baseecom.com/main-files/index-6.html
25. https://ecomartstw.baseecom.com/main-files/index-7.html
26. https://fashion.minimog.co/
27. https://demo.minimog.co/
28. https://skins.minimog.co/
29. https://next.minimog.co/
30. https://unlockdesizn.com/html/health-and-beauty/comfort-home/index-multipage.html
31. https://unlockdesizn.com/html/health-and-beauty/comfort-home/index-multipage4.html
32. https://bundleshop014.myshopify.com/?_ab=0&_fd=0&_sc=1
33. https://themeforshop.github.io/kala-babies-demo/#home
34. https://adnix-admin-template.multipurposethemes.com/main-semidark/index.html
35. https://adnix-admin-template.multipurposethemes.com/main-semidark/index5.html
36. https://adnix-admin-template.multipurposethemes.com/main-mini-sidebar/index3.html
37. https://blueberry-react-next.maraviyainfotech.com/
38. https://themazine.com/mr/corpai-html/main-html/index.html
39. https://themazine.com/mr/corpai-html/main-html/index2.html
40. https://travila.alithemes.net/
41. https://travila.alithemes.net/home-two/
42. https://travila.alithemes.net/home-three/
43. https://cs-everything-marketdeal.myshopify.com/?_ab=0&_fd=0&_sc=1
44. https://digitallaw.thememountdemo.com/digitallaw-infostack/




These are basically just for conceptual learning. 
1. https://blog.logrocket.com/ux-design/40-essential-ui-elements/
2. https://www.uxpin.com/studio/blog/user-interface-elements-every-designer-should-know/
3. https://app.uxcel.com/glossary/ui-components
4. https://app.uxcel.com/tutorials/mastering-elevation-for-dark-ui-a-comprehensive-guide-342
5. https://designli.co/blog/5-most-important-ui-design-elements-with-examples/
6. https://careerfoundry.com/en/blog/ui-design/ui-element-glossary/
