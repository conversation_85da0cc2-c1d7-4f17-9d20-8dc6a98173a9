# Simple Enhanced Image Generator
param(
    [string]$ImageName = "test-image",
    [string]$Filename = "test-image.jpg",
    [string]$Prompt = "Green nature technology concept",
    [int]$Width = 1024,
    [int]$Height = 1024,
    [int]$Seed = 12345,
    [int]$Timeout = 240,
    [string]$Priority = "high"
)

Write-Host "ADVANCED IMAGE GENERATION" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Green
Write-Host "Image: $ImageName" -ForegroundColor Cyan
Write-Host "Dimensions: ${Width}x${Height}" -ForegroundColor Cyan
Write-Host "Seed: $Seed" -ForegroundColor Cyan
Write-Host "Timeout: ${Timeout}s" -ForegroundColor Cyan
Write-Host "Priority: $Priority" -ForegroundColor Cyan

# Check service health
try {
    $health = Invoke-RestMethod -Uri "http://localhost:7777/health" -TimeoutSec 10
    Write-Host "Service healthy" -ForegroundColor Green
} catch {
    Write-Host "Cannot connect to API service" -ForegroundColor Red
    exit 1
}

# Prepare request body with all new parameters
$body = @{
    type = "image"
    prompt = $Prompt
    width = $Width
    height = $Height
    seed = $Seed
    timeout = $Timeout
    priority = $Priority
} | ConvertTo-Json

Write-Host "Starting generation..." -ForegroundColor Cyan

try {
    # Submit request
    $response = Invoke-RestMethod -Uri "http://localhost:7777/api/generate" -Method POST -Body $body -ContentType "application/json"
    
    if ($response.success) {
        $requestId = $response.requestId
        Write-Host "Request submitted! ID: $requestId" -ForegroundColor Green
        
        # Wait for completion
        Write-Host "Waiting for completion..." -ForegroundColor Yellow
        $maxAttempts = 30
        $attempt = 0
        
        while ($attempt -lt $maxAttempts) {
            $attempt++
            Start-Sleep -Seconds 10
            
            $status = Invoke-RestMethod -Uri "http://localhost:7777/api/status/$requestId"
            
            if ($status.status -eq "completed") {
                Write-Host "Generation completed!" -ForegroundColor Green
                
                # Download image
                $outputPath = "images\$Filename"
                Write-Host "Downloading to $outputPath..." -ForegroundColor Cyan
                Invoke-WebRequest -Uri "http://localhost:7777/api/image/$requestId" -OutFile $outputPath
                
                if (Test-Path $outputPath) {
                    $fileSize = (Get-Item $outputPath).Length
                    Write-Host "SUCCESS! Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
                    exit 0
                } else {
                    Write-Host "Download failed" -ForegroundColor Red
                    exit 1
                }
            } elseif ($status.status -eq "failed") {
                Write-Host "Generation failed!" -ForegroundColor Red
                exit 1
            } else {
                Write-Host "Status: $($status.status) (attempt $attempt/$maxAttempts)" -ForegroundColor Yellow
            }
        }
        
        Write-Host "Timeout after $maxAttempts attempts" -ForegroundColor Red
        exit 1
    } else {
        Write-Host "Failed to submit request" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
