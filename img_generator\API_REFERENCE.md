# API Reference Guide

## Base URL
```
http://localhost:7777
```

## Authentication
No authentication required for local development.

## Content Types
All requests should use `Content-Type: application/json`.

---

## 🔍 Health Check

### GET /health
Check if the API server is running and healthy.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-07-26T02:01:06.000Z",
  "service": "Content Generator API",
  "version": "1.0.0"
}
```

---

## 🎨 Image Generation

### POST /api/generate
Generate images using AI.

**Request Body:**
```json
{
  "type": "image",
  "prompt": "A beautiful sunset over mountains",
  "width": 640,
  "height": 800,
  "seed": 12345,
  "timeout": 300
}
```

**Parameters:**
- `type` (required): Must be "image"
- `prompt` (required): Text description of desired image
- `width` (optional): Image width in pixels (default: 640)
  - Must be multiple of 16 (FLUX requirement)
  - Range: 256-2048 pixels
  - Examples: 512, 640, 768, 1024, 1440
- `height` (optional): Image height in pixels (default: 800)
  - Must be multiple of 16 (FLUX requirement)
  - Range: 256-2048 pixels
  - Examples: 512, 800, 1024, 1072, 1088
- `seed` (optional): Seed number for reproduction (from previous generation)
- `timeout` (optional): Generation timeout in seconds (default: 120)

**Response:**
```json
{
  "success": true,
  "requestId": "1753475448770",
  "message": "Request queued for processing",
  "type": "image",
  "model": "black-forest-labs/FLUX.1-schnell-Free",
  "estimatedTime": "30-200 seconds",
  "imageParameters": {
    "width": 640,
    "height": 800,
    "seed": null
  }
}
```

---

## 📝 Text Generation

### POST /api/generate
Generate text using AI.

**Request Body:**
```json
{
  "type": "text",
  "prompt": "Write a short story about a robot",
  "maxTokens": 500,
  "timeout": 60
}
```

**Parameters:**
- `type` (required): Must be "text"
- `prompt` (required): Text prompt for generation
- `maxTokens` (optional): Maximum tokens to generate (default: 500)
- `timeout` (optional): Generation timeout in seconds (default: 60)

**Response:**
```json
{
  "success": true,
  "requestId": "1753475448800",
  "message": "Request queued for processing",
  "type": "text",
  "model": "deepseek-ai/DeepSeek-R1-0528",
  "estimatedTime": "10-60 seconds"
}
```

---

## 📊 Status Checking

### GET /api/status/{requestId}
Check the status of a generation request.

**URL Parameters:**
- `requestId`: The request ID returned from /api/generate

**Response (Pending):**
```json
{
  "requestId": "1753475448770",
  "status": "pending",
  "type": "image",
  "prompt": "A beautiful sunset over mountains",
  "createdAt": "2025-07-26T02:01:06.000Z"
}
```

**Response (Processing):**
```json
{
  "requestId": "1753475448770",
  "status": "processing",
  "type": "image",
  "prompt": "A beautiful sunset over mountains",
  "createdAt": "2025-07-26T02:01:06.000Z"
}
```

**Response (Completed - Image):**
```json
{
  "requestId": "1753475448770",
  "status": "completed",
  "type": "image",
  "prompt": "A beautiful sunset over mountains",
  "model": "black-forest-labs/FLUX.1-schnell-Free",
  "filePath": "generated/images/1753475448770.png",
  "imageUrl": "/api/image/1753475448770",
  "imageParameters": {
    "width": 640,
    "height": 800,
    "seed": null
  },
  "seedUsed": 1847392847,
  "createdAt": "2025-07-26T02:01:06.000Z",
  "completedAt": "2025-07-26T02:01:26.000Z"
}
```

**Response (Completed - Text):**
```json
{
  "requestId": "1753475448800",
  "status": "completed",
  "type": "text",
  "prompt": "Write a short story about a robot",
  "model": "deepseek-ai/DeepSeek-R1-0528",
  "content": "Generated text content here...",
  "filePath": "generated/texts/1753475448800.txt",
  "createdAt": "2025-07-26T02:01:06.000Z",
  "completedAt": "2025-07-26T02:01:26.000Z"
}
```

**Response (Failed):**
```json
{
  "requestId": "1753475448770",
  "status": "failed",
  "type": "image",
  "prompt": "A beautiful sunset over mountains",
  "error": "Generation timeout exceeded",
  "createdAt": "2025-07-26T02:01:06.000Z",
  "completedAt": "2025-07-26T02:03:06.000Z"
}
```

---

## 🖼️ Image Retrieval

### GET /api/image/{requestId}
Retrieve a generated image file.

**URL Parameters:**
- `requestId`: The request ID of a completed image generation

**Response:**
- Content-Type: `image/png`
- Returns the actual image file

**Example:**
```
GET /api/image/1753475448770
```

---

## 🤖 Available Models

### GET /api/models
Get list of available AI models.

**Response:**
```json
{
  "success": true,
  "models": {
    "image": [
      {
        "id": "black-forest-labs/FLUX.1-schnell-Free",
        "name": "FLUX.1 Schnell (Free)",
        "description": "Fast, high-quality image generation"
      }
    ],
    "text": [
      {
        "id": "deepseek-ai/DeepSeek-R1-0528",
        "name": "DeepSeek R1",
        "description": "Advanced reasoning and text generation"
      }
    ]
  }
}
```

---

## 📈 Rate Limits

### Current Limits
- **Images**: 6 requests per minute
- **Text**: 60 requests per minute

### Rate Limit Headers
Response headers include rate limit information:
```
X-RateLimit-Limit: 6
X-RateLimit-Remaining: 5
X-RateLimit-Reset: 1753475500000
```

### Rate Limit Exceeded Response
```json
{
  "success": false,
  "error": "Rate limit exceeded",
  "message": "Too many image requests. Limit: 6 per minute",
  "retryAfter": 45
}
```

---

## ❌ Error Responses

### 400 Bad Request

**Missing Required Fields:**
```json
{
  "success": false,
  "error": "Invalid request",
  "message": "Missing required field: prompt"
}
```

**Invalid Resolution (Not Multiple of 16):**
```json
{
  "error": "Invalid width",
  "message": "Width must be a multiple of 16. Current: 1080. Suggested: 1088",
  "details": {
    "current": 1080,
    "suggested": 1088,
    "requirement": "FLUX models require dimensions to be multiples of 16"
  }
}
```

**Resolution Out of Range:**
```json
{
  "error": "Invalid width",
  "message": "Width must be an integer between 256 and 2048"
}
```

### 404 Not Found
```json
{
  "success": false,
  "error": "Request not found",
  "message": "No request found with ID: 1753475448999"
}
```

### 429 Too Many Requests
```json
{
  "success": false,
  "error": "Rate limit exceeded",
  "message": "Too many requests. Please try again later.",
  "retryAfter": 30
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "error": "Internal server error",
  "message": "An unexpected error occurred"
}
```

---

## 🔄 Request Lifecycle

1. **Submit** → POST /api/generate
2. **Queue** → Request added to processing queue
3. **Process** → AI generation begins
4. **Complete** → Result saved and status updated
5. **Retrieve** → GET /api/status or /api/image

## 📋 Status Values

- `pending` - Request queued, waiting to be processed
- `processing` - AI generation in progress
- `completed` - Generation successful, result available
- `failed` - Generation failed, check error message

---

**API Version**: 1.0.0  
**Last Updated**: July 26, 2025
