const axios = require('axios');
const Store = require('electron-store');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { EventEmitter } = require('events');
const Logger = require('../utils/Logger');

class AuthManager extends EventEmitter {
  constructor() {
    super();
    this.store = new Store({ name: 'auth' });
    this.logger = new Logger('auth');
    this.currentUser = null;
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenRefreshTimer = null;
    
    // API configuration
    this.apiBaseUrl = process.env.REMOTEIT_API_URL || 'https://api.remoteit.com';
    this.clientId = process.env.REMOTEIT_CLIENT_ID || 'remoteit-desktop';
    this.clientSecret = process.env.REMOTEIT_CLIENT_SECRET;
    
    // Device information
    this.deviceId = this.getOrCreateDeviceId();
    this.deviceInfo = this.getDeviceInfo();
  }

  async initialize() {
    this.logger.info('Initializing authentication manager...');
    
    // Load stored tokens
    this.loadStoredTokens();
    
    // Validate current session
    if (this.accessToken) {
      try {
        await this.validateToken();
        this.logger.info('Existing session validated');
        this.emit('authStateChanged', { authenticated: true, user: this.currentUser });
      } catch (error) {
        this.logger.warn('Stored token invalid, clearing session');
        await this.clearSession();
      }
    }
    
    this.logger.info('Authentication manager initialized');
  }

  async login(credentials) {
    try {
      this.logger.info('Attempting login for user:', credentials.email);
      
      const response = await axios.post(`${this.apiBaseUrl}/auth/login`, {
        email: credentials.email,
        password: credentials.password,
        deviceId: this.deviceId,
        deviceInfo: this.deviceInfo,
        clientId: this.clientId,
        mfaCode: credentials.mfaCode
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': `RemoteIt Desktop/${require('../../package.json').version}`
        }
      });

      const { user, accessToken, refreshToken, expiresIn } = response.data;
      
      // Store tokens and user info
      this.currentUser = user;
      this.accessToken = accessToken;
      this.refreshToken = refreshToken;
      
      // Save to secure storage
      this.saveTokens();
      
      // Setup token refresh
      this.setupTokenRefresh(expiresIn);
      
      this.logger.info('Login successful for user:', user.email);
      this.emit('authStateChanged', { authenticated: true, user });
      
      return { success: true, user };
      
    } catch (error) {
      this.logger.error('Login failed:', error.message);
      
      if (error.response) {
        const { status, data } = error.response;
        
        if (status === 401) {
          return { success: false, error: 'Invalid credentials' };
        } else if (status === 403 && data.requiresMfa) {
          return { success: false, error: 'MFA required', requiresMfa: true };
        } else if (status === 429) {
          return { success: false, error: 'Too many login attempts. Please try again later.' };
        }
      }
      
      return { success: false, error: 'Login failed. Please check your connection and try again.' };
    }
  }

  async logout() {
    try {
      this.logger.info('Logging out user:', this.currentUser?.email);
      
      // Notify server of logout
      if (this.accessToken) {
        try {
          await axios.post(`${this.apiBaseUrl}/auth/logout`, {
            deviceId: this.deviceId
          }, {
            headers: {
              'Authorization': `Bearer ${this.accessToken}`
            },
            timeout: 5000
          });
        } catch (error) {
          this.logger.warn('Failed to notify server of logout:', error.message);
        }
      }
      
      // Clear local session
      await this.clearSession();
      
      this.logger.info('Logout successful');
      this.emit('authStateChanged', { authenticated: false, user: null });
      
      return { success: true };
      
    } catch (error) {
      this.logger.error('Logout error:', error.message);
      return { success: false, error: error.message };
    }
  }

  async refreshAccessToken() {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }
    
    try {
      this.logger.debug('Refreshing access token...');
      
      const response = await axios.post(`${this.apiBaseUrl}/auth/refresh`, {
        refreshToken: this.refreshToken,
        deviceId: this.deviceId
      }, {
        timeout: 10000
      });
      
      const { accessToken, refreshToken, expiresIn } = response.data;
      
      this.accessToken = accessToken;
      if (refreshToken) {
        this.refreshToken = refreshToken;
      }
      
      this.saveTokens();
      this.setupTokenRefresh(expiresIn);
      
      this.logger.debug('Access token refreshed successfully');
      
    } catch (error) {
      this.logger.error('Token refresh failed:', error.message);
      await this.clearSession();
      this.emit('authStateChanged', { authenticated: false, user: null });
      throw error;
    }
  }

  async validateToken() {
    if (!this.accessToken) {
      throw new Error('No access token available');
    }
    
    try {
      const response = await axios.get(`${this.apiBaseUrl}/auth/validate`, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        },
        timeout: 5000
      });
      
      this.currentUser = response.data.user;
      return true;
      
    } catch (error) {
      if (error.response?.status === 401) {
        // Try to refresh token
        if (this.refreshToken) {
          await this.refreshAccessToken();
          return true;
        }
      }
      throw error;
    }
  }

  async register(userData) {
    try {
      this.logger.info('Attempting registration for user:', userData.email);
      
      const response = await axios.post(`${this.apiBaseUrl}/auth/register`, {
        ...userData,
        deviceId: this.deviceId,
        deviceInfo: this.deviceInfo
      }, {
        timeout: 10000
      });
      
      this.logger.info('Registration successful for user:', userData.email);
      return { success: true, message: 'Registration successful. Please check your email to verify your account.' };
      
    } catch (error) {
      this.logger.error('Registration failed:', error.message);
      
      if (error.response) {
        const { status, data } = error.response;
        
        if (status === 409) {
          return { success: false, error: 'Email already registered' };
        } else if (status === 400) {
          return { success: false, error: data.message || 'Invalid registration data' };
        }
      }
      
      return { success: false, error: 'Registration failed. Please try again.' };
    }
  }

  async resetPassword(email) {
    try {
      await axios.post(`${this.apiBaseUrl}/auth/reset-password`, { email });
      return { success: true, message: 'Password reset email sent' };
    } catch (error) {
      this.logger.error('Password reset failed:', error.message);
      return { success: false, error: 'Failed to send password reset email' };
    }
  }

  getCurrentUser() {
    return this.currentUser;
  }

  getAccessToken() {
    return this.accessToken;
  }

  isAuthenticated() {
    return !!this.accessToken && !!this.currentUser;
  }

  getOrCreateDeviceId() {
    let deviceId = this.store.get('deviceId');
    
    if (!deviceId) {
      deviceId = crypto.randomUUID();
      this.store.set('deviceId', deviceId);
      this.logger.info('Generated new device ID:', deviceId);
    }
    
    return deviceId;
  }

  getDeviceInfo() {
    const os = require('os');
    const { app } = require('electron');
    
    return {
      platform: os.platform(),
      arch: os.arch(),
      hostname: os.hostname(),
      version: app.getVersion(),
      userAgent: `RemoteIt Desktop/${app.getVersion()} (${os.platform()} ${os.arch()})`
    };
  }

  loadStoredTokens() {
    try {
      const encryptedData = this.store.get('tokens');
      if (encryptedData) {
        const decrypted = this.decrypt(encryptedData);
        const tokens = JSON.parse(decrypted);
        
        this.accessToken = tokens.accessToken;
        this.refreshToken = tokens.refreshToken;
        this.currentUser = tokens.user;
      }
    } catch (error) {
      this.logger.warn('Failed to load stored tokens:', error.message);
      this.store.delete('tokens');
    }
  }

  saveTokens() {
    try {
      const tokens = {
        accessToken: this.accessToken,
        refreshToken: this.refreshToken,
        user: this.currentUser
      };
      
      const encrypted = this.encrypt(JSON.stringify(tokens));
      this.store.set('tokens', encrypted);
      
    } catch (error) {
      this.logger.error('Failed to save tokens:', error.message);
    }
  }

  async clearSession() {
    this.currentUser = null;
    this.accessToken = null;
    this.refreshToken = null;
    
    if (this.tokenRefreshTimer) {
      clearTimeout(this.tokenRefreshTimer);
      this.tokenRefreshTimer = null;
    }
    
    this.store.delete('tokens');
  }

  setupTokenRefresh(expiresIn) {
    if (this.tokenRefreshTimer) {
      clearTimeout(this.tokenRefreshTimer);
    }
    
    // Refresh token 5 minutes before expiry
    const refreshTime = (expiresIn - 300) * 1000;
    
    this.tokenRefreshTimer = setTimeout(async () => {
      try {
        await this.refreshAccessToken();
      } catch (error) {
        this.logger.error('Automatic token refresh failed:', error.message);
      }
    }, refreshTime);
  }

  encrypt(text) {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(this.deviceId, 'salt', 32);
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipherGCM(algorithm, key, iv);
    cipher.setAAD(Buffer.from(this.deviceId));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return {
      iv: iv.toString('hex'),
      encrypted,
      authTag: authTag.toString('hex')
    };
  }

  decrypt(encryptedData) {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(this.deviceId, 'salt', 32);
    
    const decipher = crypto.createDecipherGCM(algorithm, key);
    decipher.setAAD(Buffer.from(this.deviceId));
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}

module.exports = AuthManager;
