const crypto = require('crypto');
const { EventEmitter } = require('events');
const Store = require('electron-store');
const Logger = require('../utils/Logger');

class SecurityManager extends EventEmitter {
  constructor() {
    super();
    this.logger = new Logger('security');
    this.store = new Store({ name: 'security' });
    
    // Security settings
    this.settings = {
      requirePermission: true,
      sessionRecording: false,
      maxSessionDuration: 8 * 60 * 60 * 1000, // 8 hours
      idleTimeout: 30 * 60 * 1000, // 30 minutes
      maxFailedAttempts: 5,
      lockoutDuration: 15 * 60 * 1000, // 15 minutes
      allowedIpRanges: [],
      blockedIpRanges: [],
      trustedDevices: new Set(),
      sessionEncryption: true,
      auditLogging: true
    };
    
    // Security state
    this.activeSessions = new Map();
    this.failedAttempts = new Map();
    this.blockedIps = new Map();
    this.sessionKeys = new Map();
    this.auditLog = [];
    
    // Load settings
    this.loadSettings();
  }

  async initialize() {
    this.logger.info('Initializing security manager...');
    
    try {
      // Generate master key if not exists
      await this.ensureMasterKey();
      
      // Setup session monitoring
      this.startSessionMonitoring();
      
      // Setup audit logging
      this.setupAuditLogging();
      
      // Cleanup expired sessions
      this.cleanupExpiredSessions();
      
      this.logger.info('Security manager initialized successfully');
      
    } catch (error) {
      this.logger.error('Failed to initialize security manager:', error.message);
      throw error;
    }
  }

  async ensureMasterKey() {
    let masterKey = this.store.get('masterKey');
    
    if (!masterKey) {
      masterKey = crypto.randomBytes(32).toString('hex');
      this.store.set('masterKey', masterKey);
      this.logger.info('Generated new master key');
    }
    
    this.masterKey = masterKey;
  }

  loadSettings() {
    const storedSettings = this.store.get('settings', {});
    this.settings = { ...this.settings, ...storedSettings };
    
    // Convert arrays back to Sets
    if (storedSettings.trustedDevices) {
      this.settings.trustedDevices = new Set(storedSettings.trustedDevices);
    }
  }

  saveSettings() {
    const settingsToSave = {
      ...this.settings,
      trustedDevices: Array.from(this.settings.trustedDevices)
    };
    
    this.store.set('settings', settingsToSave);
  }

  // Session Security
  async createSecureSession(sessionId, deviceId, userId, connectionInfo) {
    try {
      // Validate connection
      if (!this.validateConnection(connectionInfo)) {
        throw new Error('Connection validation failed');
      }
      
      // Check if device is trusted
      const isTrusted = this.settings.trustedDevices.has(deviceId);
      
      // Generate session key
      const sessionKey = this.generateSessionKey();
      this.sessionKeys.set(sessionId, sessionKey);
      
      const session = {
        id: sessionId,
        deviceId: deviceId,
        userId: userId,
        startTime: Date.now(),
        lastActivity: Date.now(),
        connectionInfo: connectionInfo,
        isTrusted: isTrusted,
        encrypted: this.settings.sessionEncryption,
        permissions: this.getSessionPermissions(deviceId, userId),
        recordingEnabled: this.settings.sessionRecording
      };
      
      this.activeSessions.set(sessionId, session);
      
      // Log security event
      this.logSecurityEvent('session_created', {
        sessionId,
        deviceId,
        userId,
        isTrusted,
        connectionInfo
      });
      
      this.emit('sessionCreated', session);
      return session;
      
    } catch (error) {
      this.logger.error('Failed to create secure session:', error.message);
      throw error;
    }
  }

  validateConnection(connectionInfo) {
    const { clientIP, hostIP } = connectionInfo;
    
    // Check IP restrictions
    if (this.isIpBlocked(clientIP)) {
      this.logSecurityEvent('connection_blocked', { ip: clientIP, reason: 'IP blocked' });
      return false;
    }
    
    if (!this.isIpAllowed(clientIP)) {
      this.logSecurityEvent('connection_blocked', { ip: clientIP, reason: 'IP not in allowed range' });
      return false;
    }
    
    return true;
  }

  isIpBlocked(ip) {
    // Check if IP is in blocked list
    if (this.blockedIps.has(ip)) {
      const blockInfo = this.blockedIps.get(ip);
      if (Date.now() < blockInfo.expiresAt) {
        return true;
      } else {
        this.blockedIps.delete(ip);
      }
    }
    
    // Check blocked IP ranges
    return this.settings.blockedIpRanges.some(range => this.isIpInRange(ip, range));
  }

  isIpAllowed(ip) {
    // If no allowed ranges specified, allow all
    if (this.settings.allowedIpRanges.length === 0) {
      return true;
    }
    
    // Check if IP is in allowed ranges
    return this.settings.allowedIpRanges.some(range => this.isIpInRange(ip, range));
  }

  isIpInRange(ip, range) {
    // Simple CIDR check implementation
    const [rangeIp, mask] = range.split('/');
    const maskBits = parseInt(mask) || 32;
    
    const ipInt = this.ipToInt(ip);
    const rangeInt = this.ipToInt(rangeIp);
    const maskInt = (0xFFFFFFFF << (32 - maskBits)) >>> 0;
    
    return (ipInt & maskInt) === (rangeInt & maskInt);
  }

  ipToInt(ip) {
    return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet), 0) >>> 0;
  }

  generateSessionKey() {
    return crypto.randomBytes(32);
  }

  getSessionPermissions(deviceId, userId) {
    // Default permissions
    const permissions = {
      screenShare: true,
      remoteControl: true,
      fileTransfer: true,
      clipboard: true,
      recording: this.settings.sessionRecording
    };
    
    // Modify based on device trust level
    if (!this.settings.trustedDevices.has(deviceId)) {
      permissions.fileTransfer = false; // Restrict file transfer for untrusted devices
    }
    
    return permissions;
  }

  // Authentication Security
  recordFailedAttempt(identifier, ip) {
    const key = `${identifier}:${ip}`;
    const attempts = this.failedAttempts.get(key) || { count: 0, firstAttempt: Date.now() };
    
    attempts.count++;
    attempts.lastAttempt = Date.now();
    
    this.failedAttempts.set(key, attempts);
    
    // Check if should block
    if (attempts.count >= this.settings.maxFailedAttempts) {
      this.blockIp(ip, 'Too many failed attempts');
      this.logSecurityEvent('ip_blocked', { ip, reason: 'Failed attempts', count: attempts.count });
    }
    
    this.logSecurityEvent('failed_attempt', { identifier, ip, count: attempts.count });
  }

  blockIp(ip, reason) {
    this.blockedIps.set(ip, {
      reason: reason,
      blockedAt: Date.now(),
      expiresAt: Date.now() + this.settings.lockoutDuration
    });
  }

  clearFailedAttempts(identifier, ip) {
    const key = `${identifier}:${ip}`;
    this.failedAttempts.delete(key);
  }

  // Device Trust Management
  trustDevice(deviceId, deviceInfo) {
    this.settings.trustedDevices.add(deviceId);
    this.saveSettings();
    
    this.logSecurityEvent('device_trusted', { deviceId, deviceInfo });
    this.emit('deviceTrusted', { deviceId, deviceInfo });
  }

  untrustDevice(deviceId) {
    this.settings.trustedDevices.delete(deviceId);
    this.saveSettings();
    
    this.logSecurityEvent('device_untrusted', { deviceId });
    this.emit('deviceUntrusted', { deviceId });
  }

  isDeviceTrusted(deviceId) {
    return this.settings.trustedDevices.has(deviceId);
  }

  // Encryption
  encryptData(data, sessionId) {
    if (!this.settings.sessionEncryption) {
      return data;
    }
    
    const sessionKey = this.sessionKeys.get(sessionId);
    if (!sessionKey) {
      throw new Error('Session key not found');
    }
    
    const algorithm = 'aes-256-gcm';
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipher(algorithm, sessionKey);
    cipher.setAAD(Buffer.from(sessionId));
    
    let encrypted = cipher.update(data);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    
    const authTag = cipher.getAuthTag();
    
    return {
      iv: iv.toString('hex'),
      encrypted: encrypted.toString('hex'),
      authTag: authTag.toString('hex')
    };
  }

  decryptData(encryptedData, sessionId) {
    if (!this.settings.sessionEncryption) {
      return encryptedData;
    }
    
    const sessionKey = this.sessionKeys.get(sessionId);
    if (!sessionKey) {
      throw new Error('Session key not found');
    }
    
    const algorithm = 'aes-256-gcm';
    
    const decipher = crypto.createDecipher(algorithm, sessionKey);
    decipher.setAAD(Buffer.from(sessionId));
    decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
    
    let decrypted = decipher.update(Buffer.from(encryptedData.encrypted, 'hex'));
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    
    return decrypted;
  }

  // Session Monitoring
  startSessionMonitoring() {
    setInterval(() => {
      this.checkSessionTimeouts();
      this.cleanupExpiredSessions();
      this.cleanupFailedAttempts();
    }, 60000); // Check every minute
  }

  checkSessionTimeouts() {
    const now = Date.now();
    
    for (const [sessionId, session] of this.activeSessions) {
      // Check idle timeout
      if (now - session.lastActivity > this.settings.idleTimeout) {
        this.terminateSession(sessionId, 'Idle timeout');
        continue;
      }
      
      // Check max session duration
      if (now - session.startTime > this.settings.maxSessionDuration) {
        this.terminateSession(sessionId, 'Maximum duration exceeded');
        continue;
      }
    }
  }

  updateSessionActivity(sessionId) {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.lastActivity = Date.now();
    }
  }

  terminateSession(sessionId, reason) {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.endTime = Date.now();
      session.endReason = reason;
      
      this.activeSessions.delete(sessionId);
      this.sessionKeys.delete(sessionId);
      
      this.logSecurityEvent('session_terminated', {
        sessionId,
        reason,
        duration: session.endTime - session.startTime
      });
      
      this.emit('sessionTerminated', session);
    }
  }

  cleanupExpiredSessions() {
    // Remove old session data
    const cutoff = Date.now() - 24 * 60 * 60 * 1000; // 24 hours
    
    for (const [sessionId, session] of this.activeSessions) {
      if (session.endTime && session.endTime < cutoff) {
        this.activeSessions.delete(sessionId);
        this.sessionKeys.delete(sessionId);
      }
    }
  }

  cleanupFailedAttempts() {
    const cutoff = Date.now() - this.settings.lockoutDuration;
    
    for (const [key, attempts] of this.failedAttempts) {
      if (attempts.lastAttempt < cutoff) {
        this.failedAttempts.delete(key);
      }
    }
  }

  // Audit Logging
  setupAuditLogging() {
    if (!this.settings.auditLogging) return;
    
    // Setup audit log rotation
    setInterval(() => {
      this.rotateAuditLog();
    }, 24 * 60 * 60 * 1000); // Daily rotation
  }

  logSecurityEvent(event, data) {
    if (!this.settings.auditLogging) return;
    
    const logEntry = {
      timestamp: new Date().toISOString(),
      event: event,
      data: data,
      id: crypto.randomUUID()
    };
    
    this.auditLog.push(logEntry);
    this.logger.info(`Security event: ${event}`, data);
    
    // Emit for real-time monitoring
    this.emit('securityEvent', logEntry);
  }

  rotateAuditLog() {
    if (this.auditLog.length > 10000) {
      // Keep only recent entries
      this.auditLog = this.auditLog.slice(-5000);
    }
  }

  getAuditLog(filter = {}) {
    let logs = this.auditLog;
    
    if (filter.event) {
      logs = logs.filter(log => log.event === filter.event);
    }
    
    if (filter.since) {
      logs = logs.filter(log => new Date(log.timestamp) >= filter.since);
    }
    
    if (filter.limit) {
      logs = logs.slice(-filter.limit);
    }
    
    return logs;
  }

  // Settings Management
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
    
    this.logSecurityEvent('settings_updated', { settings: Object.keys(newSettings) });
    this.emit('settingsUpdated', this.settings);
  }

  getSettings() {
    return { ...this.settings };
  }

  // Security Status
  getSecurityStatus() {
    return {
      activeSessions: this.activeSessions.size,
      trustedDevices: this.settings.trustedDevices.size,
      blockedIps: this.blockedIps.size,
      failedAttempts: this.failedAttempts.size,
      auditLogEntries: this.auditLog.length,
      settings: this.settings
    };
  }

  cleanup() {
    this.logger.info('Cleaning up security manager...');
    
    // Terminate all sessions
    for (const sessionId of this.activeSessions.keys()) {
      this.terminateSession(sessionId, 'Application shutdown');
    }
    
    // Save final state
    this.saveSettings();
    
    this.removeAllListeners();
    this.logger.info('Security manager cleanup complete');
  }
}

module.exports = SecurityManager;
