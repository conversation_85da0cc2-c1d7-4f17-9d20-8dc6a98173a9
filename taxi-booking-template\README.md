# RideNow - Taxi Booking Template

A modern ride-hailing platform template with comprehensive booking functionality and professional design.

## Features

- **Interactive Booking Form** - Real-time ride booking with location inputs
- **Service Types** - Economy, Comfort, Premium, and XL ride options
- **Safety Features** - Comprehensive safety information and protocols
- **Driver Section** - Information for potential drivers
- **How It Works** - Step-by-step process explanation
- **Contact System** - Customer support and contact forms

## Design

- **Color Scheme**: Yellow and black transportation theme
- **Typography**: Inter font family for modern readability
- **Layout**: Mobile-first responsive design
- **Interactive Elements**: Modal ride selection and notifications

## Files

- `web/index.html` - Main taxi booking website
- `css/style.css` - Complete styling (300+ lines)
- `js/main.js` - Interactive booking system (300+ lines)
- `about.txt` - Comprehensive project documentation

## JavaScript Features

- **Real-time Booking** - Interactive ride selection with modal
- **GPS Simulation** - Current location detection
- **Form Validation** - Complete booking form validation
- **Notifications** - Toast notification system
- **Responsive Menu** - Mobile navigation

## Usage

1. Open `web/index.html` in your browser
2. Test the booking functionality
3. Customize branding and service areas
4. Update pricing and service types
5. Integrate with actual booking APIs

## Customization

- Update company name and branding
- Modify service types and pricing
- Customize service areas and locations
- Add real GPS and mapping integration
- Connect to payment processing systems

Perfect for ride-hailing services, taxi companies, and transportation platforms.
