<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EduConnect - Find Your Perfect Tutor</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Inter', sans-serif; line-height: 1.6; color: #1f2937; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .header { background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 1rem 0; }
        .header-content { display: flex; justify-content: space-between; align-items: center; }
        .logo h1 { color: #2563eb; font-size: 2rem; font-weight: 700; }
        .nav-list { display: flex; list-style: none; gap: 2rem; }
        .nav-list a { text-decoration: none; color: #1f2937; font-weight: 500; }
        .btn { padding: 0.75rem 1.5rem; border: none; border-radius: 8px; font-weight: 600; cursor: pointer; }
        .btn-primary { background: #2563eb; color: white; }
        .hero { background: linear-gradient(135deg, rgba(37, 99, 235, 0.8), rgba(59, 130, 246, 0.8)), url('../images/hero-education-background.jpg'); background-size: cover; background-position: center; background-attachment: fixed; color: white; padding: 6rem 0; text-align: center; }
        .hero h1 { font-size: 3rem; margin-bottom: 1rem; }
        .hero p { font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; }
        .search-form { background: white; padding: 2rem; border-radius: 16px; max-width: 600px; margin: 0 auto; color: #1f2937; }
        .form-group { margin-bottom: 1rem; }
        .form-group input, .form-group select { width: 100%; padding: 0.75rem; border: 2px solid #e5e7eb; border-radius: 8px; }
        .features { padding: 4rem 0; background: #f8fafc; }
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; }
        .feature-item { text-align: center; padding: 2rem; background: white; border-radius: 16px; }
        .feature-icon { width: 80px; height: 80px; background: #2563eb; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 2rem; }
        .teachers { padding: 4rem 0; }
        .section-header { text-align: center; margin-bottom: 3rem; }
        .section-header h2 { font-size: 2.5rem; margin-bottom: 1rem; }
        .teachers-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; }
        .teacher-card { background: white; border-radius: 16px; padding: 2rem; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .teacher-avatar { width: 80px; height: 80px; background: #e5e7eb; border-radius: 50%; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center; font-size: 2rem; color: #6b7280; }
        .teacher-name { font-size: 1.3rem; font-weight: 600; margin-bottom: 0.5rem; }
        .teacher-subject { color: #2563eb; font-weight: 500; margin-bottom: 1rem; }
        .teacher-rating { display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem; }
        .stars { color: #fbbf24; }
        .teacher-price { font-size: 1.2rem; font-weight: 700; color: #10b981; margin-bottom: 1rem; }
        .subjects { padding: 4rem 0; background: #f8fafc; }
        .subjects-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; }
        .subject-card { background: white; padding: 1.5rem; border-radius: 12px; text-align: center; cursor: pointer; transition: transform 0.3s ease; }
        .subject-card:hover { transform: translateY(-5px); }
        .footer { background: #1f2937; color: white; padding: 3rem 0 1rem; }
        .footer-content { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; }
        .footer h3 { color: #3b82f6; margin-bottom: 1rem; }
        @media (max-width: 768px) { .hero h1 { font-size: 2rem; } .teachers-grid, .features-grid { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-graduation-cap"></i> EduConnect</h1>
                </div>
                <nav class="nav">
                    <ul class="nav-list">
                        <li><a href="#home">Home</a></li>
                        <li><a href="#teachers">Teachers</a></li>
                        <li><a href="#subjects">Subjects</a></li>
                        <li><a href="#about">About</a></li>
                    </ul>
                </nav>
                <button class="btn btn-primary">Sign Up</button>
            </div>
        </div>
    </header>

    <section class="hero" id="home">
        <div class="container">
            <h1>Find Your Perfect Tutor</h1>
            <p>Connect with qualified teachers for personalized learning experiences</p>
            <div class="search-form">
                <div class="form-group">
                    <select>
                        <option>Select Subject</option>
                        <option>Mathematics</option>
                        <option>Physics</option>
                        <option>Chemistry</option>
                        <option>Biology</option>
                        <option>English</option>
                        <option>Computer Science</option>
                    </select>
                </div>
                <div class="form-group">
                    <input type="text" placeholder="Enter your location">
                </div>
                <button class="btn btn-primary" style="width: 100%;">Find Teachers</button>
            </div>
        </div>
    </section>

    <section class="features">
        <div class="container">
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-user-graduate"></i></div>
                    <h3>Qualified Teachers</h3>
                    <p>All our tutors are verified and experienced professionals</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-clock"></i></div>
                    <h3>Flexible Scheduling</h3>
                    <p>Book lessons at times that work best for you</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-home"></i></div>
                    <h3>Online & In-Person</h3>
                    <p>Choose between online sessions or face-to-face tutoring</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-chart-line"></i></div>
                    <h3>Track Progress</h3>
                    <p>Monitor your learning progress with detailed reports</p>
                </div>
            </div>
        </div>
    </section>

    <section class="teachers" id="teachers">
        <div class="container">
            <div class="section-header">
                <h2>Featured Teachers</h2>
                <p>Meet our top-rated tutors</p>
            </div>
            <div class="teachers-grid">
                <div class="teacher-card">
                    <div class="teacher-avatar" style="background-image: url('../images/teacher-sarah-johnson.jpg'); background-size: cover; background-position: center;"></div>
                    <div class="teacher-name">Dr. Sarah Johnson</div>
                    <div class="teacher-subject">Mathematics & Physics</div>
                    <div class="teacher-rating">
                        <span class="stars">★★★★★</span>
                        <span>4.9 (127 reviews)</span>
                    </div>
                    <div class="teacher-price">$45/hour</div>
                    <button class="btn btn-primary">Book Session</button>
                </div>
                <div class="teacher-card">
                    <div class="teacher-avatar" style="background-image: url('../images/teacher-michael-chen.jpg'); background-size: cover; background-position: center;"></div>
                    <div class="teacher-name">Prof. Michael Chen</div>
                    <div class="teacher-subject">Computer Science</div>
                    <div class="teacher-rating">
                        <span class="stars">★★★★★</span>
                        <span>4.8 (89 reviews)</span>
                    </div>
                    <div class="teacher-price">$50/hour</div>
                    <button class="btn btn-primary">Book Session</button>
                </div>
                <div class="teacher-card">
                    <div class="teacher-avatar" style="background-image: url('../images/teacher-emily-davis.jpg'); background-size: cover; background-position: center;"></div>
                    <div class="teacher-name">Ms. Emily Davis</div>
                    <div class="teacher-subject">English Literature</div>
                    <div class="teacher-rating">
                        <span class="stars">★★★★★</span>
                        <span>4.9 (156 reviews)</span>
                    </div>
                    <div class="teacher-price">$40/hour</div>
                    <button class="btn btn-primary">Book Session</button>
                </div>
            </div>
        </div>
    </section>

    <section class="subjects" id="subjects">
        <div class="container">
            <div class="section-header">
                <h2>Popular Subjects</h2>
                <p>Find tutors for any subject</p>
            </div>
            <div class="subjects-grid">
                <div class="subject-card">
                    <i class="fas fa-calculator" style="font-size: 2rem; color: #2563eb; margin-bottom: 1rem;"></i>
                    <h3>Mathematics</h3>
                    <p>245 tutors available</p>
                </div>
                <div class="subject-card">
                    <i class="fas fa-atom" style="font-size: 2rem; color: #2563eb; margin-bottom: 1rem;"></i>
                    <h3>Physics</h3>
                    <p>189 tutors available</p>
                </div>
                <div class="subject-card">
                    <i class="fas fa-flask" style="font-size: 2rem; color: #2563eb; margin-bottom: 1rem;"></i>
                    <h3>Chemistry</h3>
                    <p>156 tutors available</p>
                </div>
                <div class="subject-card">
                    <i class="fas fa-dna" style="font-size: 2rem; color: #2563eb; margin-bottom: 1rem;"></i>
                    <h3>Biology</h3>
                    <p>134 tutors available</p>
                </div>
                <div class="subject-card">
                    <i class="fas fa-book" style="font-size: 2rem; color: #2563eb; margin-bottom: 1rem;"></i>
                    <h3>English</h3>
                    <p>298 tutors available</p>
                </div>
                <div class="subject-card">
                    <i class="fas fa-code" style="font-size: 2rem; color: #2563eb; margin-bottom: 1rem;"></i>
                    <h3>Programming</h3>
                    <p>167 tutors available</p>
                </div>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div>
                    <h3><i class="fas fa-graduation-cap"></i> EduConnect</h3>
                    <p>Connecting students with qualified tutors for personalized learning experiences.</p>
                </div>
                <div>
                    <h4>For Students</h4>
                    <ul style="list-style: none;">
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Find Tutors</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Book Sessions</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Track Progress</a></li>
                    </ul>
                </div>
                <div>
                    <h4>For Teachers</h4>
                    <ul style="list-style: none;">
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Become a Tutor</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Teacher Resources</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Earnings</a></li>
                    </ul>
                </div>
                <div>
                    <h4>Support</h4>
                    <ul style="list-style: none;">
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Help Center</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Contact Us</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">FAQ</a></li>
                    </ul>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #374151;">
                <p>&copy; 2024 EduConnect. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
