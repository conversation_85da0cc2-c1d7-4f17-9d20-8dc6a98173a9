param(
    [string]$ImageName = "hero-background",
    [string]$Filename = "hero-background.jpg",
    [string]$Prompt = "Dark forest landscape with subtle green technology elements, misty atmosphere, tall trees silhouettes, green glowing accents, professional nature photography, high resolution"
)

Write-Host "Generating: $ImageName" -ForegroundColor Green
Write-Host "Prompt: $Prompt" -ForegroundColor Cyan

# Check service
try {
    $health = Invoke-RestMethod -Uri "http://localhost:7777/health" -TimeoutSec 5
    Write-Host "Service is healthy" -ForegroundColor Green
} catch {
    Write-Host "Service not available" -ForegroundColor Red
    exit 1
}

# Submit request
$body = @{
    type = "image"
    prompt = $Prompt
} | ConvertTo-Json

Write-Host "Submitting request..." -ForegroundColor Cyan
$response = Invoke-RestMethod -Uri "http://localhost:7777/api/generate" -Method POST -Body $body -ContentType "application/json"

if ($response.success) {
    $requestId = $response.requestId
    Write-Host "Request ID: $requestId" -ForegroundColor Green

    # Wait 60 seconds
    Write-Host "Waiting 60 seconds..." -ForegroundColor Yellow
    Start-Sleep -Seconds 60

    # Check status multiple times
    for ($i = 1; $i -le 10; $i++) {
        Write-Host "Checking status (attempt $i)..." -ForegroundColor Cyan

        $status = Invoke-RestMethod -Uri "http://localhost:7777/api/status/$requestId"

        if ($status.status -eq "completed") {
            Write-Host "Generation completed!" -ForegroundColor Green

            # Download
            $outputPath = "images\$Filename"
            Write-Host "Downloading to $outputPath..." -ForegroundColor Cyan
            Invoke-WebRequest -Uri "http://localhost:7777/api/image/$requestId" -OutFile $outputPath

            if (Test-Path $outputPath) {
                $fileSize = (Get-Item $outputPath).Length
                Write-Host "Success! Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
                exit 0
            } else {
                Write-Host "Download failed" -ForegroundColor Red
                exit 1
            }
        } elseif ($status.status -eq "failed") {
            Write-Host "Generation failed" -ForegroundColor Red
            exit 1
        } else {
            Write-Host "Status: $($status.status)" -ForegroundColor Yellow
            Start-Sleep -Seconds 15
        }
    }

    Write-Host "Timeout" -ForegroundColor Red
    exit 1
} else {
    Write-Host "Failed to submit" -ForegroundColor Red
    exit 1
}
