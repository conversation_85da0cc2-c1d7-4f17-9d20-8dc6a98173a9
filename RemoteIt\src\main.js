const { app, BrowserWindow, Menu, ipcMain, dialog, shell, screen } = require('electron');
const { autoUpdater } = require('electron-updater');
const Store = require('electron-store');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Import RemoteIt modules
const AuthManager = require('./auth/AuthManager');
const ConnectionManager = require('./connection/ConnectionManager');
const ScreenCapture = require('./capture/ScreenCapture');
const InputHandler = require('./input/InputHandler');
const FileManager = require('./file/FileManager');
const SecurityManager = require('./security/SecurityManager');
const Logger = require('./utils/Logger');

class RemoteItApp {
  constructor() {
    this.mainWindow = null;
    this.hostWindow = null;
    this.clientWindows = new Map();
    this.store = new Store();
    this.logger = new Logger('main');
    
    // Initialize managers
    this.authManager = new AuthManager();
    this.connectionManager = new ConnectionManager();
    this.screenCapture = new ScreenCapture();
    this.inputHandler = new InputHandler();
    this.fileManager = new FileManager();
    this.securityManager = new SecurityManager();
    
    this.setupApp();
  }

  setupApp() {
    // App event handlers
    app.whenReady().then(() => this.onReady());
    app.on('window-all-closed', () => this.onWindowAllClosed());
    app.on('activate', () => this.onActivate());
    app.on('before-quit', () => this.onBeforeQuit());
    app.on('second-instance', () => this.onSecondInstance());
    
    // Security settings
    app.on('web-contents-created', (event, contents) => {
      this.setupWebContentsSecurity(contents);
    });
    
    // IPC handlers
    this.setupIpcHandlers();
    
    // Auto updater
    this.setupAutoUpdater();
  }

  async onReady() {
    this.logger.info('RemoteIt application starting...');
    
    // Create application menu
    this.createMenu();
    
    // Create main window
    await this.createMainWindow();
    
    // Initialize services
    await this.initializeServices();
    
    // Check for updates
    if (!app.isPackaged) {
      this.logger.info('Development mode - skipping auto updater');
    } else {
      autoUpdater.checkForUpdatesAndNotify();
    }
    
    this.logger.info('RemoteIt application ready');
  }

  async createMainWindow() {
    const windowState = this.store.get('windowState', {
      width: 1200,
      height: 800,
      x: undefined,
      y: undefined
    });

    this.mainWindow = new BrowserWindow({
      width: windowState.width,
      height: windowState.height,
      x: windowState.x,
      y: windowState.y,
      minWidth: 800,
      minHeight: 600,
      show: false,
      icon: this.getAppIcon(),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false
      },
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default'
    });

    // Load the main interface
    if (app.isPackaged) {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    } else {
      this.mainWindow.loadURL('http://localhost:3000');
    }

    // Window event handlers
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      if (!app.isPackaged) {
        this.mainWindow.webContents.openDevTools();
      }
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    this.mainWindow.on('resize', () => {
      this.saveWindowState();
    });

    this.mainWindow.on('move', () => {
      this.saveWindowState();
    });
  }

  async createHostWindow() {
    if (this.hostWindow) {
      this.hostWindow.focus();
      return;
    }

    this.hostWindow = new BrowserWindow({
      width: 400,
      height: 300,
      resizable: false,
      alwaysOnTop: true,
      skipTaskbar: true,
      show: false,
      frame: false,
      transparent: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      }
    });

    this.hostWindow.loadFile(path.join(__dirname, '../renderer/host.html'));

    this.hostWindow.once('ready-to-show', () => {
      this.hostWindow.show();
    });

    this.hostWindow.on('closed', () => {
      this.hostWindow = null;
    });
  }

  async createClientWindow(sessionId, hostInfo) {
    const displays = screen.getAllDisplays();
    const primaryDisplay = screen.getPrimaryDisplay();
    
    const clientWindow = new BrowserWindow({
      width: Math.min(1920, primaryDisplay.workAreaSize.width),
      height: Math.min(1080, primaryDisplay.workAreaSize.height),
      show: false,
      icon: this.getAppIcon(),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
        additionalArguments: [`--session-id=${sessionId}`]
      }
    });

    clientWindow.loadFile(path.join(__dirname, '../renderer/client.html'));

    clientWindow.once('ready-to-show', () => {
      clientWindow.show();
    });

    clientWindow.on('closed', () => {
      this.clientWindows.delete(sessionId);
      this.connectionManager.endSession(sessionId);
    });

    this.clientWindows.set(sessionId, clientWindow);
    return clientWindow;
  }

  setupIpcHandlers() {
    // Authentication
    ipcMain.handle('auth:login', async (event, credentials) => {
      return await this.authManager.login(credentials);
    });

    ipcMain.handle('auth:logout', async (event) => {
      return await this.authManager.logout();
    });

    ipcMain.handle('auth:getUser', async (event) => {
      return await this.authManager.getCurrentUser();
    });

    // Connection management
    ipcMain.handle('connection:getDevices', async (event) => {
      return await this.connectionManager.getDevices();
    });

    ipcMain.handle('connection:connect', async (event, deviceId) => {
      const sessionId = await this.connectionManager.connect(deviceId);
      const hostInfo = await this.connectionManager.getHostInfo(deviceId);
      await this.createClientWindow(sessionId, hostInfo);
      return sessionId;
    });

    ipcMain.handle('connection:disconnect', async (event, sessionId) => {
      return await this.connectionManager.disconnect(sessionId);
    });

    // Screen capture
    ipcMain.handle('screen:startCapture', async (event, options) => {
      return await this.screenCapture.startCapture(options);
    });

    ipcMain.handle('screen:stopCapture', async (event) => {
      return await this.screenCapture.stopCapture();
    });

    ipcMain.handle('screen:getDisplays', async (event) => {
      return screen.getAllDisplays();
    });

    // Input handling
    ipcMain.handle('input:sendEvent', async (event, inputEvent) => {
      return await this.inputHandler.sendEvent(inputEvent);
    });

    // File management
    ipcMain.handle('file:browse', async (event, path) => {
      return await this.fileManager.browse(path);
    });

    ipcMain.handle('file:transfer', async (event, transferInfo) => {
      return await this.fileManager.transfer(transferInfo);
    });

    ipcMain.handle('file:selectFiles', async (event, options) => {
      const result = await dialog.showOpenDialog(this.mainWindow, options);
      return result;
    });

    // System
    ipcMain.handle('system:getInfo', async (event) => {
      return {
        platform: os.platform(),
        arch: os.arch(),
        hostname: os.hostname(),
        version: app.getVersion(),
        userDataPath: app.getPath('userData')
      };
    });

    ipcMain.handle('system:openExternal', async (event, url) => {
      return await shell.openExternal(url);
    });

    // Window management
    ipcMain.handle('window:minimize', (event) => {
      const window = BrowserWindow.fromWebContents(event.sender);
      window?.minimize();
    });

    ipcMain.handle('window:maximize', (event) => {
      const window = BrowserWindow.fromWebContents(event.sender);
      if (window?.isMaximized()) {
        window.unmaximize();
      } else {
        window?.maximize();
      }
    });

    ipcMain.handle('window:close', (event) => {
      const window = BrowserWindow.fromWebContents(event.sender);
      window?.close();
    });

    ipcMain.handle('window:showHost', async (event) => {
      await this.createHostWindow();
    });
  }

  setupWebContentsSecurity(contents) {
    // Prevent navigation to external URLs
    contents.on('will-navigate', (event, navigationUrl) => {
      const parsedUrl = new URL(navigationUrl);
      
      if (parsedUrl.origin !== 'http://localhost:3000' && 
          parsedUrl.protocol !== 'file:') {
        event.preventDefault();
      }
    });

    // Prevent new window creation
    contents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });
  }

  setupAutoUpdater() {
    autoUpdater.on('checking-for-update', () => {
      this.logger.info('Checking for updates...');
    });

    autoUpdater.on('update-available', (info) => {
      this.logger.info('Update available:', info);
    });

    autoUpdater.on('update-not-available', (info) => {
      this.logger.info('Update not available:', info);
    });

    autoUpdater.on('error', (err) => {
      this.logger.error('Auto updater error:', err);
    });

    autoUpdater.on('download-progress', (progressObj) => {
      this.logger.info(`Download progress: ${progressObj.percent}%`);
    });

    autoUpdater.on('update-downloaded', (info) => {
      this.logger.info('Update downloaded:', info);
      autoUpdater.quitAndInstall();
    });
  }

  async initializeServices() {
    try {
      await this.authManager.initialize();
      await this.connectionManager.initialize();
      await this.securityManager.initialize();
      this.logger.info('All services initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize services:', error);
      throw error;
    }
  }

  createMenu() {
    const template = [
      {
        label: 'File',
        submenu: [
          {
            label: 'New Connection',
            accelerator: 'CmdOrCtrl+N',
            click: () => this.mainWindow?.webContents.send('menu:newConnection')
          },
          { type: 'separator' },
          {
            label: 'Exit',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => app.quit()
          }
        ]
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      },
      {
        label: 'Help',
        submenu: [
          {
            label: 'About RemoteIt',
            click: () => this.showAboutDialog()
          },
          {
            label: 'Documentation',
            click: () => shell.openExternal('https://docs.remoteit.com')
          }
        ]
      }
    ];

    if (process.platform === 'darwin') {
      template.unshift({
        label: app.getName(),
        submenu: [
          { role: 'about' },
          { type: 'separator' },
          { role: 'services' },
          { type: 'separator' },
          { role: 'hide' },
          { role: 'hideOthers' },
          { role: 'unhide' },
          { type: 'separator' },
          { role: 'quit' }
        ]
      });
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  showAboutDialog() {
    dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: 'About RemoteIt',
      message: 'RemoteIt',
      detail: `Version: ${app.getVersion()}\nProfessional Remote Desktop Software\n\n© 2025 RemoteIt Technologies`
    });
  }

  getAppIcon() {
    const iconPath = path.join(__dirname, '../assets/icons');
    if (process.platform === 'win32') {
      return path.join(iconPath, 'icon.ico');
    } else if (process.platform === 'darwin') {
      return path.join(iconPath, 'icon.icns');
    } else {
      return path.join(iconPath, 'icon.png');
    }
  }

  saveWindowState() {
    if (this.mainWindow) {
      const bounds = this.mainWindow.getBounds();
      this.store.set('windowState', bounds);
    }
  }

  onWindowAllClosed() {
    if (process.platform !== 'darwin') {
      app.quit();
    }
  }

  onActivate() {
    if (BrowserWindow.getAllWindows().length === 0) {
      this.createMainWindow();
    }
  }

  onBeforeQuit() {
    this.logger.info('Application shutting down...');
    this.saveWindowState();
    
    // Cleanup services
    this.connectionManager.cleanup();
    this.screenCapture.cleanup();
  }

  onSecondInstance() {
    // Focus the main window if a second instance is launched
    if (this.mainWindow) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.focus();
    }
  }
}

// Ensure single instance
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  // Create the application
  new RemoteItApp();
}
