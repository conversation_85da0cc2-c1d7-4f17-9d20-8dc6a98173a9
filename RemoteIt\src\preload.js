const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Authentication
  auth: {
    login: (credentials) => ipcRenderer.invoke('auth:login', credentials),
    logout: () => ipcRenderer.invoke('auth:logout'),
    getUser: () => ipcRenderer.invoke('auth:getUser'),
    onAuthStateChanged: (callback) => {
      ipcRenderer.on('auth:stateChanged', callback);
      return () => ipcRenderer.removeListener('auth:stateChanged', callback);
    }
  },

  // Connection management
  connection: {
    getDevices: () => ipcRenderer.invoke('connection:getDevices'),
    connect: (deviceId) => ipcRenderer.invoke('connection:connect', deviceId),
    disconnect: (sessionId) => ipcRenderer.invoke('connection:disconnect', sessionId),
    onConnectionStateChanged: (callback) => {
      ipcRenderer.on('connection:stateChanged', callback);
      return () => ipcRenderer.removeListener('connection:stateChanged', callback);
    },
    onDeviceListChanged: (callback) => {
      ipcRenderer.on('connection:deviceListChanged', callback);
      return () => ipcRenderer.removeListener('connection:deviceListChanged', callback);
    }
  },

  // Screen capture
  screen: {
    startCapture: (options) => ipcRenderer.invoke('screen:startCapture', options),
    stopCapture: () => ipcRenderer.invoke('screen:stopCapture'),
    getDisplays: () => ipcRenderer.invoke('screen:getDisplays'),
    onScreenData: (callback) => {
      ipcRenderer.on('screen:data', callback);
      return () => ipcRenderer.removeListener('screen:data', callback);
    }
  },

  // Input handling
  input: {
    sendEvent: (inputEvent) => ipcRenderer.invoke('input:sendEvent', inputEvent),
    onInputEvent: (callback) => {
      ipcRenderer.on('input:event', callback);
      return () => ipcRenderer.removeListener('input:event', callback);
    }
  },

  // File management
  file: {
    browse: (path) => ipcRenderer.invoke('file:browse', path),
    transfer: (transferInfo) => ipcRenderer.invoke('file:transfer', transferInfo),
    selectFiles: (options) => ipcRenderer.invoke('file:selectFiles', options),
    onTransferProgress: (callback) => {
      ipcRenderer.on('file:transferProgress', callback);
      return () => ipcRenderer.removeListener('file:transferProgress', callback);
    }
  },

  // System
  system: {
    getInfo: () => ipcRenderer.invoke('system:getInfo'),
    openExternal: (url) => ipcRenderer.invoke('system:openExternal', url)
  },

  // Window management
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    close: () => ipcRenderer.invoke('window:close'),
    showHost: () => ipcRenderer.invoke('window:showHost')
  },

  // Menu events
  menu: {
    onNewConnection: (callback) => {
      ipcRenderer.on('menu:newConnection', callback);
      return () => ipcRenderer.removeListener('menu:newConnection', callback);
    }
  },

  // Notifications
  notifications: {
    show: (title, body, options = {}) => {
      if (Notification.permission === 'granted') {
        return new Notification(title, { body, ...options });
      } else if (Notification.permission !== 'denied') {
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            return new Notification(title, { body, ...options });
          }
        });
      }
    }
  },

  // Utilities
  utils: {
    platform: process.platform,
    versions: process.versions,
    getSessionId: () => {
      const args = process.argv;
      const sessionArg = args.find(arg => arg.startsWith('--session-id='));
      return sessionArg ? sessionArg.split('=')[1] : null;
    }
  }
});

// Security: Remove Node.js globals from renderer process
delete window.require;
delete window.exports;
delete window.module;

// Add security headers
window.addEventListener('DOMContentLoaded', () => {
  // Prevent context menu in production
  if (process.env.NODE_ENV === 'production') {
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
    });
  }

  // Prevent drag and drop of files
  document.addEventListener('dragover', (e) => {
    e.preventDefault();
  });

  document.addEventListener('drop', (e) => {
    e.preventDefault();
  });

  // Add CSP meta tag if not present
  if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
    const cspMeta = document.createElement('meta');
    cspMeta.setAttribute('http-equiv', 'Content-Security-Policy');
    cspMeta.setAttribute('content', 
      "default-src 'self'; " +
      "script-src 'self' 'unsafe-inline'; " +
      "style-src 'self' 'unsafe-inline'; " +
      "img-src 'self' data: blob:; " +
      "connect-src 'self' ws: wss:; " +
      "media-src 'self' blob:; " +
      "object-src 'none'; " +
      "base-uri 'self';"
    );
    document.head.appendChild(cspMeta);
  }
});

// Error handling
window.addEventListener('error', (event) => {
  console.error('Renderer error:', event.error);
  ipcRenderer.send('renderer:error', {
    message: event.error.message,
    stack: event.error.stack,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno
  });
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  ipcRenderer.send('renderer:unhandledRejection', {
    reason: event.reason,
    promise: event.promise
  });
});

// Performance monitoring
if (typeof PerformanceObserver !== 'undefined') {
  const observer = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    entries.forEach((entry) => {
      if (entry.entryType === 'navigation') {
        console.log('Navigation timing:', {
          domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
          loadComplete: entry.loadEventEnd - entry.loadEventStart,
          totalTime: entry.loadEventEnd - entry.fetchStart
        });
      }
    });
  });
  
  observer.observe({ entryTypes: ['navigation', 'measure'] });
}

// Memory usage monitoring
setInterval(() => {
  if (performance.memory) {
    const memInfo = {
      usedJSHeapSize: performance.memory.usedJSHeapSize,
      totalJSHeapSize: performance.memory.totalJSHeapSize,
      jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
    };
    
    // Log warning if memory usage is high
    const usagePercent = (memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit) * 100;
    if (usagePercent > 80) {
      console.warn('High memory usage detected:', usagePercent.toFixed(2) + '%');
    }
  }
}, 30000); // Check every 30 seconds
