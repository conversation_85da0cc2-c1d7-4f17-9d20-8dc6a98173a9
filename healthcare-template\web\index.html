<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MediCare - Your Health Partner</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Inter', sans-serif; line-height: 1.6; color: #1f2937; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .header { background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 1rem 0; position: fixed; top: 0; left: 0; right: 0; z-index: 1000; }
        .header-content { display: flex; justify-content: space-between; align-items: center; }
        .logo h1 { color: #0ea5e9; font-size: 2rem; font-weight: 700; }
        .nav-list { display: flex; list-style: none; gap: 2rem; }
        .nav-list a { text-decoration: none; color: #1f2937; font-weight: 500; }
        .btn { padding: 0.75rem 1.5rem; border: none; border-radius: 8px; font-weight: 600; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #0ea5e9; color: white; }
        .btn-outline { background: transparent; color: #0ea5e9; border: 2px solid #0ea5e9; }
        .hero { background: linear-gradient(135deg, rgba(14, 165, 233, 0.8), rgba(2, 132, 199, 0.8)), url('../images/hero-hospital-background.jpg'); background-size: cover; background-position: center; background-attachment: fixed; color: white; padding: 8rem 0 4rem; text-align: center; }
        .hero h1 { font-size: 3rem; margin-bottom: 1rem; }
        .hero p { font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; }
        .appointment-form { background: white; padding: 2rem; border-radius: 16px; max-width: 600px; margin: 2rem auto 0; color: #1f2937; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem; }
        .form-group { margin-bottom: 1rem; }
        .form-group input, .form-group select { width: 100%; padding: 0.75rem; border: 2px solid #e5e7eb; border-radius: 8px; }
        .services { padding: 4rem 0; background: #f8fafc; }
        .services-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 2rem; }
        .service-card { background: white; padding: 2rem; border-radius: 16px; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .service-icon { width: 80px; height: 80px; background: linear-gradient(135deg, #0ea5e9, #0284c7); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 2rem; }
        .doctors { padding: 4rem 0; }
        .section-header { text-align: center; margin-bottom: 3rem; }
        .section-header h2 { font-size: 2.5rem; margin-bottom: 1rem; color: #1f2937; }
        .doctors-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; }
        .doctor-card { background: white; border-radius: 16px; padding: 2rem; box-shadow: 0 4px 20px rgba(0,0,0,0.1); text-align: center; }
        .doctor-avatar { width: 100px; height: 100px; background: #e5e7eb; border-radius: 50%; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center; font-size: 2.5rem; color: #6b7280; }
        .doctor-name { font-size: 1.3rem; font-weight: 600; margin-bottom: 0.5rem; }
        .doctor-specialty { color: #0ea5e9; font-weight: 500; margin-bottom: 1rem; }
        .doctor-rating { display: flex; align-items: center; justify-content: center; gap: 0.5rem; margin-bottom: 1rem; }
        .stars { color: #fbbf24; }
        .features { padding: 4rem 0; background: #f8fafc; }
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; }
        .feature-item { text-align: center; padding: 2rem; background: white; border-radius: 16px; }
        .feature-icon { width: 60px; height: 60px; background: #10b981; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem; }
        .emergency { background: #dc2626; color: white; padding: 3rem 0; text-align: center; }
        .emergency h2 { font-size: 2rem; margin-bottom: 1rem; }
        .emergency-number { font-size: 3rem; font-weight: 700; margin: 1rem 0; }
        .footer { background: #1f2937; color: white; padding: 3rem 0 1rem; }
        .footer-content { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; }
        .footer h3 { color: #0ea5e9; margin-bottom: 1rem; }
        @media (max-width: 768px) { 
            .hero { padding: 6rem 0 2rem; } 
            .hero h1 { font-size: 2rem; } 
            .form-row { grid-template-columns: 1fr; } 
            .doctors-grid, .services-grid, .features-grid { grid-template-columns: 1fr; } 
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-heartbeat"></i> MediCare</h1>
                </div>
                <nav class="nav">
                    <ul class="nav-list">
                        <li><a href="#home">Home</a></li>
                        <li><a href="#services">Services</a></li>
                        <li><a href="#doctors">Doctors</a></li>
                        <li><a href="#about">About</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </nav>
                <div>
                    <a href="#" class="btn btn-outline">Patient Portal</a>
                    <a href="#" class="btn btn-primary">Book Appointment</a>
                </div>
            </div>
        </div>
    </header>

    <section class="hero" id="home">
        <div class="container">
            <h1>Your Health, Our Priority</h1>
            <p>Comprehensive healthcare services with experienced medical professionals</p>
            <div class="appointment-form">
                <h3 style="margin-bottom: 1.5rem; text-align: center;">Book Your Appointment</h3>
                <form>
                    <div class="form-row">
                        <div class="form-group">
                            <input type="text" placeholder="Full Name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" placeholder="Email Address" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <input type="tel" placeholder="Phone Number" required>
                        </div>
                        <div class="form-group">
                            <select required>
                                <option value="">Select Department</option>
                                <option value="cardiology">Cardiology</option>
                                <option value="neurology">Neurology</option>
                                <option value="orthopedics">Orthopedics</option>
                                <option value="pediatrics">Pediatrics</option>
                                <option value="dermatology">Dermatology</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <input type="date" required>
                        </div>
                        <div class="form-group">
                            <select required>
                                <option value="">Preferred Time</option>
                                <option value="9:00">9:00 AM</option>
                                <option value="10:00">10:00 AM</option>
                                <option value="11:00">11:00 AM</option>
                                <option value="14:00">2:00 PM</option>
                                <option value="15:00">3:00 PM</option>
                                <option value="16:00">4:00 PM</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <textarea placeholder="Describe your symptoms or reason for visit" rows="3" style="width: 100%; padding: 0.75rem; border: 2px solid #e5e7eb; border-radius: 8px;"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary" style="width: 100%;">Book Appointment</button>
                </form>
            </div>
        </div>
    </section>

    <section class="services" id="services">
        <div class="container">
            <div class="section-header">
                <h2>Our Medical Services</h2>
                <p>Comprehensive healthcare solutions for all your medical needs</p>
            </div>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-heart"></i></div>
                    <h3>Cardiology</h3>
                    <p>Expert heart care with advanced diagnostic and treatment options</p>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-brain"></i></div>
                    <h3>Neurology</h3>
                    <p>Specialized care for neurological conditions and brain health</p>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-bone"></i></div>
                    <h3>Orthopedics</h3>
                    <p>Comprehensive bone, joint, and muscle care services</p>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-baby"></i></div>
                    <h3>Pediatrics</h3>
                    <p>Specialized healthcare for infants, children, and adolescents</p>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-eye"></i></div>
                    <h3>Ophthalmology</h3>
                    <p>Complete eye care services and vision correction</p>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-user-md"></i></div>
                    <h3>General Medicine</h3>
                    <p>Primary healthcare and preventive medicine services</p>
                </div>
            </div>
        </div>
    </section>

    <section class="doctors" id="doctors">
        <div class="container">
            <div class="section-header">
                <h2>Meet Our Doctors</h2>
                <p>Experienced medical professionals dedicated to your health</p>
            </div>
            <div class="doctors-grid">
                <div class="doctor-card">
                    <div class="doctor-avatar" style="background-image: url('../images/doctor-sarah-wilson.jpg'); background-size: cover; background-position: center;"></div>
                    <div class="doctor-name">Dr. Sarah Wilson</div>
                    <div class="doctor-specialty">Cardiologist</div>
                    <div class="doctor-rating">
                        <span class="stars">★★★★★</span>
                        <span>4.9 (234 reviews)</span>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">15+ years of experience in cardiovascular medicine</p>
                    <button class="btn btn-primary">Book Appointment</button>
                </div>
                <div class="doctor-card">
                    <div class="doctor-avatar" style="background-image: url('../images/doctor-michael-chen.jpg'); background-size: cover; background-position: center;"></div>
                    <div class="doctor-name">Dr. Michael Chen</div>
                    <div class="doctor-specialty">Neurologist</div>
                    <div class="doctor-rating">
                        <span class="stars">★★★★★</span>
                        <span>4.8 (189 reviews)</span>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Specialist in neurological disorders and brain health</p>
                    <button class="btn btn-primary">Book Appointment</button>
                </div>
                <div class="doctor-card">
                    <div class="doctor-avatar" style="background-image: url('../images/doctor-emily-rodriguez.jpg'); background-size: cover; background-position: center;"></div>
                    <div class="doctor-name">Dr. Emily Rodriguez</div>
                    <div class="doctor-specialty">Pediatrician</div>
                    <div class="doctor-rating">
                        <span class="stars">★★★★★</span>
                        <span>4.9 (312 reviews)</span>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Dedicated to providing excellent care for children</p>
                    <button class="btn btn-primary">Book Appointment</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Medical Technology Showcase -->
    <section class="medical-tech" style="padding: 4rem 0; background: #f8fafc;">
        <div class="container">
            <div class="tech-content" style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; align-items: center;">
                <div class="tech-image">
                    <img src="../images/medical-equipment.jpg" alt="Advanced Medical Equipment" style="width: 100%; height: 400px; object-fit: cover; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                </div>
                <div class="tech-text">
                    <h2 style="font-size: 2.5rem; margin-bottom: 1rem; color: #1f2937;">Advanced Medical Technology</h2>
                    <p style="font-size: 1.1rem; color: #6b7280; margin-bottom: 2rem;">Our state-of-the-art medical equipment ensures accurate diagnosis and effective treatment for all our patients.</p>
                    <ul style="list-style: none; padding: 0;">
                        <li style="display: flex; align-items: center; margin-bottom: 1rem;"><i class="fas fa-check" style="color: #10b981; margin-right: 0.5rem;"></i> Latest diagnostic imaging technology</li>
                        <li style="display: flex; align-items: center; margin-bottom: 1rem;"><i class="fas fa-check" style="color: #10b981; margin-right: 0.5rem;"></i> Advanced surgical equipment</li>
                        <li style="display: flex; align-items: center; margin-bottom: 1rem;"><i class="fas fa-check" style="color: #10b981; margin-right: 0.5rem;"></i> Digital health monitoring systems</li>
                        <li style="display: flex; align-items: center; margin-bottom: 1rem;"><i class="fas fa-check" style="color: #10b981; margin-right: 0.5rem;"></i> Electronic health records</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <section class="features">
        <div class="container">
            <div class="section-header">
                <h2>Why Choose MediCare?</h2>
                <p>Excellence in healthcare with patient-centered approach</p>
            </div>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-clock"></i></div>
                    <h3>24/7 Emergency Care</h3>
                    <p>Round-the-clock emergency services for urgent medical needs</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-microscope"></i></div>
                    <h3>Advanced Diagnostics</h3>
                    <p>State-of-the-art medical equipment for accurate diagnosis</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-shield-alt"></i></div>
                    <h3>Insurance Accepted</h3>
                    <p>We accept most major insurance plans for your convenience</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon"><i class="fas fa-mobile-alt"></i></div>
                    <h3>Telemedicine</h3>
                    <p>Virtual consultations for convenient healthcare access</p>
                </div>
            </div>
        </div>
    </section>

    <section class="emergency">
        <div class="container">
            <h2>Medical Emergency?</h2>
            <p>Call our emergency hotline immediately</p>
            <div class="emergency-number">911</div>
            <p>Or visit our emergency department - open 24/7</p>
        </div>
    </section>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div>
                    <h3><i class="fas fa-heartbeat"></i> MediCare</h3>
                    <p>Providing comprehensive healthcare services with compassion and excellence since 1985.</p>
                    <div style="margin-top: 1rem;">
                        <a href="#" style="color: white; margin-right: 1rem; font-size: 1.5rem;"><i class="fab fa-facebook"></i></a>
                        <a href="#" style="color: white; margin-right: 1rem; font-size: 1.5rem;"><i class="fab fa-twitter"></i></a>
                        <a href="#" style="color: white; margin-right: 1rem; font-size: 1.5rem;"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div>
                    <h4>Services</h4>
                    <ul style="list-style: none;">
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Emergency Care</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Cardiology</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Neurology</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Pediatrics</a></li>
                    </ul>
                </div>
                <div>
                    <h4>Patient Resources</h4>
                    <ul style="list-style: none;">
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Patient Portal</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Medical Records</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Insurance</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Billing</a></li>
                    </ul>
                </div>
                <div>
                    <h4>Contact Info</h4>
                    <p style="color: #d1d5db;"><i class="fas fa-map-marker-alt"></i> 123 Medical Center Dr<br>Healthcare City, HC 12345</p>
                    <p style="color: #d1d5db;"><i class="fas fa-phone"></i> +1 (555) MEDICARE</p>
                    <p style="color: #d1d5db;"><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #374151;">
                <p>&copy; 2024 MediCare Healthcare. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
