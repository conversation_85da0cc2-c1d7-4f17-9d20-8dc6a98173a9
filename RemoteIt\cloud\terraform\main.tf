# RemoteIt Cloud Infrastructure
# Terraform configuration for AWS deployment

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Variables
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "domain_name" {
  description = "Domain name for RemoteIt services"
  type        = string
  default     = "remoteit.com"
}

# VPC Configuration
resource "aws_vpc" "remoteit_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "remoteit-vpc"
    Environment = var.environment
  }
}

# Internet Gateway
resource "aws_internet_gateway" "remoteit_igw" {
  vpc_id = aws_vpc.remoteit_vpc.id

  tags = {
    Name = "remoteit-igw"
  }
}

# Public Subnets
resource "aws_subnet" "public_subnet_1" {
  vpc_id                  = aws_vpc.remoteit_vpc.id
  cidr_block              = "********/24"
  availability_zone       = "${var.aws_region}a"
  map_public_ip_on_launch = true

  tags = {
    Name = "remoteit-public-1"
  }
}

resource "aws_subnet" "public_subnet_2" {
  vpc_id                  = aws_vpc.remoteit_vpc.id
  cidr_block              = "********/24"
  availability_zone       = "${var.aws_region}b"
  map_public_ip_on_launch = true

  tags = {
    Name = "remoteit-public-2"
  }
}

# Route Table
resource "aws_route_table" "public_rt" {
  vpc_id = aws_vpc.remoteit_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.remoteit_igw.id
  }

  tags = {
    Name = "remoteit-public-rt"
  }
}

resource "aws_route_table_association" "public_rta_1" {
  subnet_id      = aws_subnet.public_subnet_1.id
  route_table_id = aws_route_table.public_rt.id
}

resource "aws_route_table_association" "public_rta_2" {
  subnet_id      = aws_subnet.public_subnet_2.id
  route_table_id = aws_route_table.public_rt.id
}

# Security Groups
resource "aws_security_group" "auth_server_sg" {
  name_prefix = "remoteit-auth-"
  vpc_id      = aws_vpc.remoteit_vpc.id

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 3000
    to_port     = 3000
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "remoteit-auth-sg"
  }
}

resource "aws_security_group" "relay_server_sg" {
  name_prefix = "remoteit-relay-"
  vpc_id      = aws_vpc.remoteit_vpc.id

  ingress {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 8081
    to_port     = 8081
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # STUN/TURN ports
  ingress {
    from_port   = 3478
    to_port     = 3478
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 3478
    to_port     = 3478
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # TURN relay ports
  ingress {
    from_port   = 49152
    to_port     = 65535
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "remoteit-relay-sg"
  }
}

# Application Load Balancer
resource "aws_lb" "remoteit_alb" {
  name               = "remoteit-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.auth_server_sg.id]
  subnets            = [aws_subnet.public_subnet_1.id, aws_subnet.public_subnet_2.id]

  tags = {
    Name = "remoteit-alb"
  }
}

# Target Groups
resource "aws_lb_target_group" "auth_tg" {
  name     = "remoteit-auth-tg"
  port     = 3000
  protocol = "HTTP"
  vpc_id   = aws_vpc.remoteit_vpc.id

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200"
    path                = "/health"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
  }
}

resource "aws_lb_target_group" "relay_tg" {
  name     = "remoteit-relay-tg"
  port     = 8080
  protocol = "HTTP"
  vpc_id   = aws_vpc.remoteit_vpc.id

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200"
    path                = "/health"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
  }
}

# Launch Templates
resource "aws_launch_template" "auth_server_lt" {
  name_prefix   = "remoteit-auth-"
  image_id      = "ami-0c02fb55956c7d316" # Amazon Linux 2
  instance_type = "t3.medium"

  vpc_security_group_ids = [aws_security_group.auth_server_sg.id]

  user_data = base64encode(templatefile("${path.module}/user_data_auth.sh", {
    environment = var.environment
  }))

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name = "remoteit-auth-server"
      Type = "auth"
    }
  }
}

resource "aws_launch_template" "relay_server_lt" {
  name_prefix   = "remoteit-relay-"
  image_id      = "ami-0c02fb55956c7d316" # Amazon Linux 2
  instance_type = "t3.large"

  vpc_security_group_ids = [aws_security_group.relay_server_sg.id]

  user_data = base64encode(templatefile("${path.module}/user_data_relay.sh", {
    environment = var.environment
  }))

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name = "remoteit-relay-server"
      Type = "relay"
    }
  }
}

# Auto Scaling Groups
resource "aws_autoscaling_group" "auth_asg" {
  name                = "remoteit-auth-asg"
  vpc_zone_identifier = [aws_subnet.public_subnet_1.id, aws_subnet.public_subnet_2.id]
  target_group_arns   = [aws_lb_target_group.auth_tg.arn]
  health_check_type   = "ELB"
  min_size            = 2
  max_size            = 10
  desired_capacity    = 2

  launch_template {
    id      = aws_launch_template.auth_server_lt.id
    version = "$Latest"
  }

  tag {
    key                 = "Name"
    value               = "remoteit-auth-asg"
    propagate_at_launch = false
  }
}

resource "aws_autoscaling_group" "relay_asg" {
  name                = "remoteit-relay-asg"
  vpc_zone_identifier = [aws_subnet.public_subnet_1.id, aws_subnet.public_subnet_2.id]
  target_group_arns   = [aws_lb_target_group.relay_tg.arn]
  health_check_type   = "ELB"
  min_size            = 2
  max_size            = 20
  desired_capacity    = 4

  launch_template {
    id      = aws_launch_template.relay_server_lt.id
    version = "$Latest"
  }

  tag {
    key                 = "Name"
    value               = "remoteit-relay-asg"
    propagate_at_launch = false
  }
}

# RDS Database
resource "aws_db_subnet_group" "remoteit_db_subnet_group" {
  name       = "remoteit-db-subnet-group"
  subnet_ids = [aws_subnet.public_subnet_1.id, aws_subnet.public_subnet_2.id]

  tags = {
    Name = "remoteit-db-subnet-group"
  }
}

resource "aws_db_instance" "remoteit_db" {
  identifier     = "remoteit-db"
  engine         = "postgres"
  engine_version = "15.4"
  instance_class = "db.t3.medium"
  
  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_type          = "gp3"
  storage_encrypted     = true

  db_name  = "remoteit"
  username = "remoteit"
  password = "RemoteIt2024!" # Use AWS Secrets Manager in production

  vpc_security_group_ids = [aws_security_group.auth_server_sg.id]
  db_subnet_group_name   = aws_db_subnet_group.remoteit_db_subnet_group.name

  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  skip_final_snapshot = true

  tags = {
    Name = "remoteit-db"
  }
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "remoteit_cache_subnet_group" {
  name       = "remoteit-cache-subnet-group"
  subnet_ids = [aws_subnet.public_subnet_1.id, aws_subnet.public_subnet_2.id]
}

resource "aws_elasticache_cluster" "remoteit_redis" {
  cluster_id           = "remoteit-redis"
  engine               = "redis"
  node_type            = "cache.t3.micro"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  subnet_group_name    = aws_elasticache_subnet_group.remoteit_cache_subnet_group.name
  security_group_ids   = [aws_security_group.auth_server_sg.id]

  tags = {
    Name = "remoteit-redis"
  }
}

# Outputs
output "load_balancer_dns" {
  value = aws_lb.remoteit_alb.dns_name
}

output "database_endpoint" {
  value = aws_db_instance.remoteit_db.endpoint
}

output "redis_endpoint" {
  value = aws_elasticache_cluster.remoteit_redis.cache_nodes[0].address
}
