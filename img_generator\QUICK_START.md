# Quick Start Guide

## 🚀 Get Started in 5 Minutes

This guide will help you set up and test the Content Generator API quickly.

## 📋 Prerequisites Check

Before starting, ensure you have:
- ✅ Node.js (v14+) installed
- ✅ PostgreSQL database running
- ✅ Together.ai API key

## ⚡ Quick Setup

### 1. Environment Configuration
Create `.env` file in the project root:
```env
TOGETHER_API_KEY=your_together_ai_api_key_here
DB_HOST=localhost
DB_PORT=5432
DB_NAME=content_generator
DB_USER=your_db_user
DB_PASSWORD=your_db_password
PORT=7777
```

### 2. Install and Start
```bash
# Install dependencies
npm install

# Start the server
npm start
```

### 3. Verify Server is Running
```bash
curl http://localhost:7777/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-26T02:01:06.000Z",
  "service": "Content Generator API",
  "version": "1.0.0"
}
```

## 🎨 Generate Your First Image

### Step 1: Submit Image Request
```bash
curl -X POST http://localhost:7777/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "type": "image",
    "prompt": "A beautiful sunset over mountains",
    "width": 640,
    "height": 800
  }'
```

**Response:**
```json
{
  "success": true,
  "requestId": "1753475448770",
  "message": "Request queued for processing",
  "estimatedTime": "30-200 seconds"
}
```

### Step 2: Check Status
```bash
curl http://localhost:7777/api/status/1753475448770
```

**Response (Processing):**
```json
{
  "requestId": "1753475448770",
  "status": "processing",
  "type": "image"
}
```

**Response (Completed):**
```json
{
  "requestId": "1753475448770",
  "status": "completed",
  "imageUrl": "/api/image/1753475448770",
  "seedUsed": **********
}
```

### Step 3: View Your Image
Open in browser: `http://localhost:7777/api/image/1753475448770`

## 📝 Generate Your First Text

### Submit Text Request
```bash
curl -X POST http://localhost:7777/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "type": "text",
    "prompt": "Write a short poem about coding"
  }'
```

### Check Result
```bash
curl http://localhost:7777/api/status/{requestId}
```

## 🔄 Test Seed Reproduction

### Step 1: Use Seed from Previous Generation
```bash
curl -X POST http://localhost:7777/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "type": "image",
    "prompt": "A beautiful sunset over mountains",
    "width": 640,
    "height": 800,
    "seed": **********
  }'
```

### Step 2: Compare Results
Both images should be identical when using the same seed!

## 🧪 Run Automated Tests

### Test 3 Images Generation
```bash
node test-three-images.js
```

**Expected Output:**
```
🎉 ALL TESTS PASSED! Ready to update documentation.
✅ Successfully generated: 3/3 images
```

### Quick API Test
```bash
node quick-test.js
```

## 📊 Monitor Your System

### Check Queue Status
Monitor server logs for queue processing:
```
🚀 Processing request: 1753475448770 (image)
✅ Completed request: 1753475448770 (image)
```

### Check Rate Limits
```
📊 Rate limit status: image 1/6 requests in last minute
📊 Rate limit status: text 5/60 requests in last minute
```

### View Generated Files
```bash
# List generated images
ls -la generated/images/

# List generated texts
ls -la generated/texts/
```

## 🎯 Common Use Cases

### 1. Batch Image Generation
```javascript
const requests = [
  { prompt: "A red apple", width: 640, height: 800 },
  { prompt: "A blue ocean", width: 640, height: 800 },
  { prompt: "A green forest", width: 640, height: 800 }
];

for (const req of requests) {
  const response = await axios.post('http://localhost:7777/api/generate', {
    type: 'image',
    ...req
  });
  console.log('Submitted:', response.data.requestId);
}
```

### 2. Image Variations with Seeds
```javascript
// Generate base image
const baseResponse = await axios.post('http://localhost:7777/api/generate', {
  type: 'image',
  prompt: 'A cat sitting on a chair'
});

// Wait for completion and get seed
const status = await axios.get(`/api/status/${baseResponse.data.requestId}`);
const seed = status.data.seedUsed;

// Generate identical image
const reproductionResponse = await axios.post('http://localhost:7777/api/generate', {
  type: 'image',
  prompt: 'A cat sitting on a chair',
  seed: seed
});
```

### 3. Text Content Generation
```javascript
const textResponse = await axios.post('http://localhost:7777/api/generate', {
  type: 'text',
  prompt: 'Explain quantum computing in simple terms',
  maxTokens: 300
});
```

## 🛠️ Troubleshooting Quick Fixes

### Server Won't Start
```bash
# Check if port is in use
netstat -an | grep 7777

# Kill existing process
pkill -f "node app.js"

# Restart
npm start
```

### Database Connection Issues
```bash
# Test PostgreSQL connection
psql -h localhost -U your_user -d content_generator -c "SELECT 1;"
```

### API Key Issues
```bash
# Verify API key is set
echo $TOGETHER_API_KEY

# Test API key directly
curl -H "Authorization: Bearer $TOGETHER_API_KEY" \
  https://api.together.xyz/v1/models
```

### Generation Timeouts
- Increase timeout in request: `"timeout": 300`
- Check Together.ai service status
- Verify network connectivity

## 📈 Performance Tips

### Optimize Generation Speed
1. Use smaller image dimensions for faster generation
2. Keep prompts concise and clear
3. Monitor rate limits to avoid delays
4. Use appropriate timeout values

### Monitor Resource Usage
1. Check disk space in `generated/` folder
2. Monitor database size growth
3. Watch memory usage during processing
4. Keep logs for debugging

## 🎉 Success Indicators

You'll know everything is working when:
- ✅ Health check returns "healthy"
- ✅ Images generate within 30-200 seconds
- ✅ Text generates within 10-60 seconds
- ✅ Files appear in `generated/` folders
- ✅ Seeds enable exact reproduction
- ✅ Rate limits are respected

## 📞 Next Steps

Once you have the basic system working:
1. Read the full [API Reference](API_REFERENCE.md)
2. Explore the [Seed System](SEED_SYSTEM.md)
3. Check the complete [Documentation](README.md)
4. Customize prompts and parameters for your needs

---

**Setup Time**: ~5 minutes  
**First Image**: ~30-200 seconds  
**System Status**: ✅ Fully Operational  
**Last Verified**: July 26, 2025
