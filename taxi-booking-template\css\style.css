/* Taxi Booking Template Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.logo h1 {
    color: #fbbf24;
    font-size: 1.8rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-list a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-list a:hover {
    color: #fbbf24;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-primary {
    background: #fbbf24;
    color: #1f2937;
}

.btn-primary:hover {
    background: #f59e0b;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: #fbbf24;
    border: 2px solid #fbbf24;
}

.btn-outline:hover {
    background: #fbbf24;
    color: #1f2937;
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    overflow: hidden;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(245, 158, 11, 0.1));
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    color: white;
    text-align: center;
    width: 100%;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Booking Form */
.booking-form {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    max-width: 800px;
    margin: 0 auto;
    color: #333;
}

.form-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: center;
}

.tab-btn {
    padding: 0.75rem 2rem;
    border: none;
    background: #f3f4f6;
    color: #6b7280;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: #fbbf24;
    color: #1f2937;
}

.location-fields {
    margin-bottom: 1.5rem;
}

.field-group {
    position: relative;
    display: flex;
    align-items: center;
    background: #f9fafb;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    transition: border-color 0.3s ease;
}

.field-group:focus-within {
    border-color: #fbbf24;
}

.pickup-icon {
    color: #10b981;
    margin-right: 0.75rem;
    font-size: 0.8rem;
}

.destination-icon {
    color: #ef4444;
    margin-right: 0.75rem;
}

.field-group input,
.field-group select {
    border: none;
    background: none;
    outline: none;
    flex: 1;
    font-size: 1rem;
    color: #333;
}

.location-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.location-btn:hover {
    color: #fbbf24;
}

.booking-options {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 1rem;
    align-items: center;
}

.book-btn {
    background: #fbbf24;
    color: #1f2937;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.book-btn:hover {
    background: #f59e0b;
    transform: translateY(-2px);
}

/* Features Section */
.features-section {
    padding: 4rem 0;
    background: #f8fafc;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: #1f2937;
    font-size: 2rem;
}

.feature-item h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.feature-item p {
    color: #6b7280;
    line-height: 1.6;
}

/* Services Section */
.services-section {
    padding: 4rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-image {
    background: linear-gradient(135deg, #1f2937, #374151);
    color: #fbbf24;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
}

.service-info {
    padding: 1.5rem;
}

.service-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.service-info p {
    color: #6b7280;
    margin-bottom: 1rem;
}

.service-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.service-features span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #6b7280;
}

.service-features i {
    color: #fbbf24;
    width: 16px;
}

.service-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #fbbf24;
}

/* How It Works */
.how-it-works {
    padding: 4rem 0;
    background: #f8fafc;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.step-item {
    text-align: center;
    padding: 2rem;
}

.step-number {
    width: 60px;
    height: 60px;
    background: #fbbf24;
    color: #1f2937;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1rem;
}

.step-content h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.step-content p {
    color: #6b7280;
}

/* Safety Section */
.safety-section {
    padding: 4rem 0;
}

.safety-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.safety-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.safety-text p {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 2rem;
}

.safety-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.safety-feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.safety-feature i {
    color: #fbbf24;
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.safety-feature h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.safety-feature p {
    color: #6b7280;
    font-size: 0.9rem;
}

.safety-image,
.driver-image {
    background: #f8fafc;
    border-radius: 16px;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: #6b7280;
}

.safety-placeholder,
.driver-placeholder {
    text-align: center;
}

.safety-placeholder i,
.driver-placeholder i {
    font-size: 4rem;
    color: #fbbf24;
    margin-bottom: 1rem;
}

/* Driver Section */
.driver-section {
    padding: 4rem 0;
    background: #f8fafc;
}

.driver-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.driver-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.driver-text p {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 2rem;
}

.driver-benefits {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.benefit-item i {
    color: #fbbf24;
    font-size: 1.2rem;
}

.benefit-item span {
    font-weight: 500;
    color: #1f2937;
}

/* Contact Section */
.contact-section {
    padding: 4rem 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.contact-info h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-item i {
    color: #fbbf24;
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.contact-item h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.contact-item p {
    color: #6b7280;
}

.contact-form {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 16px;
}

.contact-form h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #fbbf24;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #fbbf24;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-section h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-section p {
    opacity: 0.8;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #fbbf24;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #374151;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #fbbf24;
    color: #1f2937;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    text-align: center;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .booking-options {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .safety-content,
    .driver-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .driver-benefits {
        grid-template-columns: 1fr;
    }
}
