/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: #0a0f0a;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    font-size: 0.9rem;
}

.btn-primary {
    background: #22c55e;
    color: white;
}

.btn-primary:hover {
    background: #16a34a;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: #22c55e;
    border: 2px solid #22c55e;
}

.btn-outline:hover {
    background: #22c55e;
    color: white;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 15, 10, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.2rem;
    color: #22c55e;
}

.logo i {
    font-size: 1.5rem;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-list a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 400;
    transition: color 0.3s ease;
}

.nav-list a:hover {
    color: #22c55e;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.mobile-menu-toggle {
    display: none;
    color: #22c55e;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #0a0f0a 0%, #1a2e1a 100%);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(10, 15, 10, 0.7), rgba(10, 15, 10, 0.5)),
        url('../images/hero-background.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 15, 10, 0.3);
}

.hero-content {
    position: relative;
    z-index: 2;
    padding-top: 6rem;
}

.hero-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text h1 {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #22c55e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-text p {
    font-size: 1.1rem;
    color: #d1d5db;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.hero-visual {
    position: relative;
}

.hero-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.hero-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.card-content h3 {
    color: #22c55e;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.card-content p {
    color: #d1d5db;
    font-size: 0.9rem;
}

/* Features Section */
.features {
    padding: 4rem 0;
    background: rgba(26, 46, 26, 0.3);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem 1rem;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #22c55e, #16a34a);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.feature-card h3 {
    color: #22c55e;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.feature-card p {
    color: #d1d5db;
    font-size: 0.9rem;
}

/* About Section */
.about-section {
    padding: 4rem 0;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #ffffff;
}

.about-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.about-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    height: 150px;
}

.about-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    padding: 1rem;
    color: white;
}

.card-overlay h4 {
    color: #22c55e;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.card-overlay p {
    font-size: 0.8rem;
    opacity: 0.9;
}

.about-visual {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.stats-card {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #22c55e;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #d1d5db;
    font-size: 0.9rem;
}

.innovation-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.innovation-card h3 {
    color: #22c55e;
    margin-bottom: 1rem;
    font-weight: 600;
}

.innovation-card p {
    color: #d1d5db;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

/* Services Section */
.services-section {
    padding: 4rem 0;
    background: rgba(26, 46, 26, 0.2);
}

.services-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.service-info h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #ffffff;
}

.service-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.service-feature {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.service-feature i {
    color: #22c55e;
    font-size: 1.2rem;
}

.service-feature span {
    color: #22c55e;
    font-weight: 500;
}

.service-feature p {
    color: #d1d5db;
    margin: 0;
}

.service-card {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.service-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.service-content {
    padding: 2rem;
}

.service-content h3 {
    color: #22c55e;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.service-content p {
    color: #d1d5db;
    font-size: 0.9rem;
}

/* Footer */
.footer {
    background: rgba(10, 15, 10, 0.8);
    padding: 3rem 0 1rem;
    border-top: 1px solid rgba(34, 197, 94, 0.2);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-brand p {
    color: #d1d5db;
    margin-top: 1rem;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.footer-column h4 {
    color: #22c55e;
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: 0.5rem;
}

.footer-column ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-column ul li a:hover {
    color: #22c55e;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(34, 197, 94, 0.1);
    color: #9ca3af;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .nav {
        display: none;
    }

    .header-actions {
        display: none;
    }

    .hero-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .about-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .container {
        padding: 0 1rem;
    }
}
