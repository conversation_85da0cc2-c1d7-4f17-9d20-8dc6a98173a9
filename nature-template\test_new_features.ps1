# Test New Image Generation Features
param(
    [string]$Prompt = "Green technology innovation with nature integration",
    [int]$Width = 1200,
    [int]$Height = 800,
    [int]$Seed = 12345,
    [string]$Priority = "high",
    [int]$Timeout = 300,
    [string]$Filename = "test-new-features.jpg"
)

Write-Host "Testing New Image Generation Features" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

Write-Host "Configuration:" -ForegroundColor Cyan
Write-Host "  Dimensions: ${Width}x${Height}" -ForegroundColor White
Write-Host "  Seed: $Seed" -ForegroundColor White
Write-Host "  Priority: $Priority" -ForegroundColor White
Write-Host "  Timeout: ${Timeout}s" -ForegroundColor White
Write-Host "  Prompt: $Prompt" -ForegroundColor Gray

# Check service
try {
    $health = Invoke-RestMethod -Uri "http://localhost:7777/health" -TimeoutSec 5
    Write-Host "Service is healthy" -ForegroundColor Green
} catch {
    Write-Host "Service not available" -ForegroundColor Red
    exit 1
}

# Prepare request with new parameters
$body = @{
    type = "image"
    prompt = $Prompt
    width = $Width
    height = $Height
    seed = $Seed
    timeout = $Timeout
    priority = $Priority
} | ConvertTo-Json

Write-Host "`nSubmitting request with new parameters..." -ForegroundColor Cyan
Write-Host $body -ForegroundColor Gray

try {
    $response = Invoke-RestMethod -Uri "http://localhost:7777/api/generate" -Method POST -Body $body -ContentType "application/json"
    
    if ($response.success) {
        Write-Host "Request submitted successfully!" -ForegroundColor Green
        Write-Host "  Request ID: $($response.requestId)" -ForegroundColor White
        Write-Host "  Model: $($response.model)" -ForegroundColor White
        Write-Host "  Estimated Time: $($response.estimatedTime)" -ForegroundColor White
        
        # Show image parameters
        if ($response.imageParameters) {
            Write-Host "Image Parameters:" -ForegroundColor Cyan
            Write-Host "  Width: $($response.imageParameters.width)" -ForegroundColor White
            Write-Host "  Height: $($response.imageParameters.height)" -ForegroundColor White
            Write-Host "  Seed: $($response.imageParameters.seed)" -ForegroundColor White
        }
        
        # Show generation parameters
        if ($response.generationParameters) {
            Write-Host "Generation Parameters:" -ForegroundColor Cyan
            Write-Host "  Timeout: $($response.generationParameters.timeout)s" -ForegroundColor White
            Write-Host "  Priority: $($response.generationParameters.priority)" -ForegroundColor White
        }
        
        $requestId = $response.requestId
        
        # Wait for completion
        Write-Host "`nWaiting for completion..." -ForegroundColor Yellow
        $maxAttempts = 20
        $attempt = 0
        $completed = $false
        
        while ($attempt -lt $maxAttempts -and !$completed) {
            $attempt++
            Write-Host "Status check $attempt/$maxAttempts..." -ForegroundColor Cyan
            
            $status = Invoke-RestMethod -Uri "http://localhost:7777/api/status/$requestId"
            Write-Host "  Status: $($status.status)" -ForegroundColor White
            
            if ($status.status -eq "completed") {
                Write-Host "Generation completed!" -ForegroundColor Green
                Write-Host "  File Path: $($status.filePath)" -ForegroundColor White
                Write-Host "  Created: $($status.createdAt)" -ForegroundColor White
                Write-Host "  Completed: $($status.completedAt)" -ForegroundColor White
                
                # Show final image parameters
                if ($status.imageParameters) {
                    Write-Host "Final Image Parameters:" -ForegroundColor Cyan
                    Write-Host "  Dimensions: $($status.imageParameters.width)x$($status.imageParameters.height)" -ForegroundColor White
                    Write-Host "  Seed Used: $($status.imageParameters.seed)" -ForegroundColor White
                }
                
                $completed = $true
                
                # Download image
                $outputPath = "images\$Filename"
                Write-Host "`nDownloading image..." -ForegroundColor Cyan
                Invoke-WebRequest -Uri "http://localhost:7777/api/image/$requestId" -OutFile $outputPath
                
                if (Test-Path $outputPath) {
                    $fileSize = (Get-Item $outputPath).Length
                    Write-Host "Downloaded successfully! Size: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
                    Write-Host "File saved as: $outputPath" -ForegroundColor Green
                } else {
                    Write-Host "Download failed!" -ForegroundColor Red
                }
                
            } elseif ($status.status -eq "failed") {
                Write-Host "Generation failed!" -ForegroundColor Red
                $completed = $true
                exit 1
            } elseif ($status.status -eq "processing") {
                Write-Host "  Still processing..." -ForegroundColor Yellow
            } elseif ($status.status -eq "pending") {
                Write-Host "  Queued for processing..." -ForegroundColor Magenta
            } else {
                Write-Host "  Unknown status: $($status.status)" -ForegroundColor Yellow
            }
            
            if (!$completed) {
                Start-Sleep -Seconds 15
            }
        }
        
        if (!$completed) {
            Write-Host "Timeout reached" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "`nNew features test completed successfully!" -ForegroundColor Green
        
    } else {
        Write-Host "Request failed" -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
