<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WanderLust - Discover Amazing Destinations</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Inter', sans-serif; line-height: 1.6; color: #1f2937; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .header { background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); box-shadow: 0 2px 10px rgba(0,0,0,0.1); padding: 1rem 0; position: fixed; top: 0; left: 0; right: 0; z-index: 1000; }
        .header-content { display: flex; justify-content: space-between; align-items: center; }
        .logo h1 { color: #0891b2; font-family: 'Playfair Display', serif; font-size: 2rem; font-weight: 700; }
        .nav-list { display: flex; list-style: none; gap: 2rem; }
        .nav-list a { text-decoration: none; color: #1f2937; font-weight: 500; transition: color 0.3s ease; }
        .nav-list a:hover { color: #0891b2; }
        .btn { padding: 0.75rem 1.5rem; border: none; border-radius: 25px; font-weight: 600; cursor: pointer; text-decoration: none; display: inline-block; transition: all 0.3s ease; }
        .btn-primary { background: linear-gradient(135deg, #0891b2, #06b6d4); color: white; }
        .btn-primary:hover { transform: translateY(-2px); }
        .btn-outline { background: transparent; color: #0891b2; border: 2px solid #0891b2; }
        .btn-outline:hover { background: #0891b2; color: white; }
        .hero { background: linear-gradient(135deg, rgba(8,145,178,0.8), rgba(6,182,212,0.7)), url('../images/destination-swiss-alps.jpg'); background-size: cover; background-position: center; background-attachment: fixed; color: white; padding: 8rem 0 4rem; text-align: center; }
        .hero h1 { font-family: 'Playfair Display', serif; font-size: 3.5rem; margin-bottom: 1rem; }
        .hero p { font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9; max-width: 600px; margin-left: auto; margin-right: auto; }
        .search-form { background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); padding: 2rem; border-radius: 20px; max-width: 700px; margin: 2rem auto 0; color: #1f2937; box-shadow: 0 20px 60px rgba(0,0,0,0.1); }
        .search-tabs { display: flex; margin-bottom: 1.5rem; background: #f1f5f9; border-radius: 12px; padding: 0.25rem; }
        .tab-btn { flex: 1; padding: 0.75rem; border: none; background: transparent; border-radius: 10px; cursor: pointer; font-weight: 500; transition: all 0.3s ease; }
        .tab-btn.active { background: white; color: #0891b2; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .search-fields { display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 1rem; align-items: end; }
        .field-group { position: relative; }
        .field-group label { display: block; margin-bottom: 0.5rem; font-weight: 500; color: #374151; }
        .field-group input, .field-group select { width: 100%; padding: 0.75rem; border: 2px solid #e5e7eb; border-radius: 10px; font-size: 1rem; }
        .search-btn { background: linear-gradient(135deg, #f59e0b, #f97316); color: white; border: none; padding: 0.75rem 2rem; border-radius: 10px; font-weight: 600; cursor: pointer; height: fit-content; }
        .destinations { padding: 4rem 0; background: #f8fafc; }
        .section-header { text-align: center; margin-bottom: 3rem; }
        .section-header h2 { font-family: 'Playfair Display', serif; font-size: 2.5rem; margin-bottom: 1rem; color: #1f2937; }
        .destinations-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; }
        .destination-card { background: white; border-radius: 20px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: transform 0.3s ease; }
        .destination-card:hover { transform: translateY(-10px); }
        .destination-image { height: 250px; background: linear-gradient(135deg, #06b6d4, #0891b2); display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem; }
        .destination-content { padding: 2rem; }
        .destination-title { font-family: 'Playfair Display', serif; font-size: 1.5rem; font-weight: 600; margin-bottom: 0.5rem; }
        .destination-location { color: #6b7280; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem; }
        .destination-price { font-size: 1.2rem; font-weight: 700; color: #0891b2; margin-bottom: 1rem; }
        .destination-features { display: flex; gap: 0.5rem; margin-bottom: 1.5rem; flex-wrap: wrap; }
        .feature-tag { background: #e0f2fe; color: #0891b2; padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.8rem; }
        .services { padding: 4rem 0; }
        .services-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 2rem; }
        .service-card { text-align: center; padding: 2rem; background: white; border-radius: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.05); transition: transform 0.3s ease; }
        .service-card:hover { transform: translateY(-5px); }
        .service-icon { width: 80px; height: 80px; background: linear-gradient(135deg, #0891b2, #06b6d4); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; color: white; font-size: 2rem; }
        .testimonials { padding: 4rem 0; background: #f8fafc; }
        .testimonials-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; }
        .testimonial-card { background: white; padding: 2rem; border-radius: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.05); }
        .testimonial-text { font-style: italic; margin-bottom: 1.5rem; color: #4b5563; }
        .testimonial-author { display: flex; align-items: center; gap: 1rem; }
        .author-avatar { width: 50px; height: 50px; background: #e5e7eb; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #6b7280; }
        .author-info h4 { font-weight: 600; margin-bottom: 0.25rem; }
        .author-info p { color: #6b7280; font-size: 0.9rem; }
        .cta { background: linear-gradient(135deg, #0891b2, #06b6d4); color: white; padding: 4rem 0; text-align: center; }
        .cta h2 { font-family: 'Playfair Display', serif; font-size: 2.5rem; margin-bottom: 1rem; }
        .footer { background: #1f2937; color: white; padding: 3rem 0 1rem; }
        .footer-content { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-bottom: 2rem; }
        .footer h3 { color: #06b6d4; margin-bottom: 1rem; font-family: 'Playfair Display', serif; }
        .social-links { display: flex; gap: 1rem; margin-top: 1rem; }
        .social-links a { width: 40px; height: 40px; background: #374151; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; text-decoration: none; transition: all 0.3s ease; }
        .social-links a:hover { background: #0891b2; transform: translateY(-2px); }
        @media (max-width: 768px) { 
            .hero { padding: 6rem 0 2rem; } 
            .hero h1 { font-size: 2.5rem; } 
            .search-fields { grid-template-columns: 1fr; } 
            .destinations-grid, .services-grid, .testimonials-grid { grid-template-columns: 1fr; } 
            .search-tabs { flex-direction: column; }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-plane"></i> WanderLust</h1>
                </div>
                <nav class="nav">
                    <ul class="nav-list">
                        <li><a href="#home">Home</a></li>
                        <li><a href="#destinations">Destinations</a></li>
                        <li><a href="#services">Services</a></li>
                        <li><a href="#about">About</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </nav>
                <div>
                    <a href="#" class="btn btn-outline">Sign In</a>
                    <a href="#" class="btn btn-primary">Book Now</a>
                </div>
            </div>
        </div>
    </header>

    <section class="hero" id="home">
        <div class="container">
            <h1>Discover Your Next Adventure</h1>
            <p>Explore breathtaking destinations and create unforgettable memories with our curated travel experiences</p>
            <div class="search-form">
                <div class="search-tabs">
                    <button class="tab-btn active">Flights</button>
                    <button class="tab-btn">Hotels</button>
                    <button class="tab-btn">Packages</button>
                    <button class="tab-btn">Car Rental</button>
                </div>
                <div class="search-fields">
                    <div class="field-group">
                        <label>From</label>
                        <input type="text" placeholder="Departure city">
                    </div>
                    <div class="field-group">
                        <label>To</label>
                        <input type="text" placeholder="Destination">
                    </div>
                    <div class="field-group">
                        <label>Departure</label>
                        <input type="date">
                    </div>
                    <button class="search-btn"><i class="fas fa-search"></i> Search</button>
                </div>
            </div>
        </div>
    </section>

    <section class="destinations" id="destinations">
        <div class="container">
            <div class="section-header">
                <h2>Popular Destinations</h2>
                <p>Discover the world's most amazing places</p>
            </div>
            <div class="destinations-grid">
                <div class="destination-card">
                    <div class="destination-image">
                        <img src="../images/destination-swiss-alps.jpg" alt="Swiss Alps Adventure" style="width: 100%; height: 100%; object-fit: cover;">
                    </div>
                    <div class="destination-content">
                        <h3 class="destination-title">Swiss Alps Adventure</h3>
                        <div class="destination-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Switzerland</span>
                        </div>
                        <div class="destination-price">From $1,299</div>
                        <div class="destination-features">
                            <span class="feature-tag">7 Days</span>
                            <span class="feature-tag">Mountain Views</span>
                            <span class="feature-tag">Adventure</span>
                        </div>
                        <p style="color: #6b7280; margin-bottom: 1.5rem;">Experience breathtaking mountain landscapes and thrilling outdoor activities</p>
                        <button class="btn btn-primary">View Details</button>
                    </div>
                </div>
                <div class="destination-card">
                    <div class="destination-image">
                        <img src="../images/destination-maldives.jpg" alt="Tropical Paradise Maldives" style="width: 100%; height: 100%; object-fit: cover;">
                    </div>
                    <div class="destination-content">
                        <h3 class="destination-title">Tropical Paradise</h3>
                        <div class="destination-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Maldives</span>
                        </div>
                        <div class="destination-price">From $2,199</div>
                        <div class="destination-features">
                            <span class="feature-tag">5 Days</span>
                            <span class="feature-tag">Beach Resort</span>
                            <span class="feature-tag">Luxury</span>
                        </div>
                        <p style="color: #6b7280; margin-bottom: 1.5rem;">Relax in overwater bungalows with crystal clear waters</p>
                        <button class="btn btn-primary">View Details</button>
                    </div>
                </div>
                <div class="destination-card">
                    <div class="destination-image">
                        <img src="../images/destination-japan.jpg" alt="Cultural Japan Tour" style="width: 100%; height: 100%; object-fit: cover;">
                    </div>
                    <div class="destination-content">
                        <h3 class="destination-title">Cultural Japan Tour</h3>
                        <div class="destination-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Japan</span>
                        </div>
                        <div class="destination-price">From $1,899</div>
                        <div class="destination-features">
                            <span class="feature-tag">10 Days</span>
                            <span class="feature-tag">Culture</span>
                            <span class="feature-tag">History</span>
                        </div>
                        <p style="color: #6b7280; margin-bottom: 1.5rem;">Immerse yourself in ancient traditions and modern wonders</p>
                        <button class="btn btn-primary">View Details</button>
                    </div>
                </div>
                <div class="destination-card">
                    <div class="destination-image">
                        <img src="../images/destination-europe.jpg" alt="European Cities Tour" style="width: 100%; height: 100%; object-fit: cover;">
                    </div>
                    <div class="destination-content">
                        <h3 class="destination-title">European Cities Tour</h3>
                        <div class="destination-location">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Europe</span>
                        </div>
                        <div class="destination-price">From $1,599</div>
                        <div class="destination-features">
                            <span class="feature-tag">12 Days</span>
                            <span class="feature-tag">Historic Cities</span>
                            <span class="feature-tag">Culture</span>
                        </div>
                        <p style="color: #6b7280; margin-bottom: 1.5rem;">Explore historic European cities with rich architecture and culture</p>
                        <button class="btn btn-primary">View Details</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="services" id="services">
        <div class="container">
            <div class="section-header">
                <h2>Our Travel Services</h2>
                <p>Everything you need for the perfect trip</p>
            </div>
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-plane"></i></div>
                    <h3>Flight Booking</h3>
                    <p>Find and book the best flights at competitive prices with flexible options</p>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-bed"></i></div>
                    <h3>Hotel Reservations</h3>
                    <p>Choose from thousands of hotels worldwide, from budget to luxury accommodations</p>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-route"></i></div>
                    <h3>Tour Packages</h3>
                    <p>Curated travel packages with guided tours and unique experiences</p>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-car"></i></div>
                    <h3>Car Rentals</h3>
                    <p>Rent vehicles for your destination with pickup and drop-off convenience</p>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-passport"></i></div>
                    <h3>Visa Assistance</h3>
                    <p>Get help with visa applications and travel documentation requirements</p>
                </div>
                <div class="service-card">
                    <div class="service-icon"><i class="fas fa-headset"></i></div>
                    <h3>24/7 Support</h3>
                    <p>Round-the-clock customer support for all your travel needs and emergencies</p>
                </div>
            </div>
        </div>
    </section>

    <section class="testimonials">
        <div class="container">
            <div class="section-header">
                <h2>What Our Travelers Say</h2>
                <p>Real experiences from real travelers</p>
            </div>
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-text">
                        "WanderLust made our honeymoon absolutely perfect! The attention to detail and personalized service exceeded our expectations."
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar"><i class="fas fa-user"></i></div>
                        <div class="author-info">
                            <h4>Sarah & John Miller</h4>
                            <p>Honeymooners</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-text">
                        "The Japan cultural tour was incredible! Every detail was perfectly planned and our guide was knowledgeable and friendly."
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar"><i class="fas fa-user"></i></div>
                        <div class="author-info">
                            <h4>Michael Chen</h4>
                            <p>Solo Traveler</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-text">
                        "Family vacation made easy! WanderLust took care of everything so we could focus on making memories with our kids."
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar"><i class="fas fa-user"></i></div>
                        <div class="author-info">
                            <h4>The Rodriguez Family</h4>
                            <p>Family Travelers</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="cta">
        <div class="container">
            <h2>Ready for Your Next Adventure?</h2>
            <p style="font-size: 1.1rem; margin-bottom: 2rem; opacity: 0.9;">Let us help you create unforgettable travel memories</p>
            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <button class="btn" style="background: white; color: #0891b2;">Browse Destinations</button>
                <button class="btn" style="background: #f59e0b; color: white;">Contact Travel Expert</button>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div>
                    <h3><i class="fas fa-plane"></i> WanderLust</h3>
                    <p>Your trusted travel partner for discovering amazing destinations and creating unforgettable experiences around the world.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div>
                    <h4>Destinations</h4>
                    <ul style="list-style: none;">
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Europe Tours</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Asia Adventures</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Beach Holidays</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Cultural Tours</a></li>
                    </ul>
                </div>
                <div>
                    <h4>Services</h4>
                    <ul style="list-style: none;">
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Flight Booking</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Hotel Reservations</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Tour Packages</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Travel Insurance</a></li>
                    </ul>
                </div>
                <div>
                    <h4>Support</h4>
                    <ul style="list-style: none;">
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Help Center</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Contact Us</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">Travel Tips</a></li>
                        <li><a href="#" style="color: #d1d5db; text-decoration: none;">FAQ</a></li>
                    </ul>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #374151;">
                <p>&copy; 2024 WanderLust Travel Agency. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
