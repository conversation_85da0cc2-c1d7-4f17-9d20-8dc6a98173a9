# ShopHub - E-commerce Template

A modern, responsive e-commerce template with both web and mobile app versions. Built with HTML5, CSS3, and vanilla JavaScript.

## 🚀 Features

### Web Version
- **Responsive Design**: Fully responsive layout that works on all devices
- **Modern UI/UX**: Clean, modern design with smooth animations
- **Product Catalog**: Grid layout with filtering and search functionality
- **Shopping Cart**: Sidebar cart with quantity controls
- **Product Actions**: Wishlist, quick view, and add to cart
- **Categories**: Visual category navigation
- **Special Offers**: Promotional banners and deals section
- **Newsletter**: Email subscription form
- **Mobile Menu**: Collapsible navigation for mobile devices

### Mobile App Version
- **Native App Feel**: Mobile-first design with app-like interactions
- **Touch Gestures**: Swipe navigation and pull-to-refresh
- **Bottom Navigation**: Easy thumb navigation
- **Flash Sales**: Countdown timer and progress indicators
- **Cart Drawer**: Bottom sheet cart interface
- **Status Bar**: iOS-style status bar simulation
- **Smooth Animations**: Optimized animations for mobile

## 📁 File Structure

```
ecommerce-template/
├── web/
│   └── index.html          # Main web page
├── mobile/
│   ├── index.html          # Mobile app page
│   ├── mobile-style.css    # Mobile-specific styles
│   └── mobile-app.js       # Mobile app functionality
├── css/
│   ├── style.css           # Main stylesheet
│   └── responsive.css      # Responsive design rules
├── js/
│   └── main.js             # Web functionality
├── images/
│   ├── hero-banner.png     # Hero section image
│   └── products-grid.png   # Product showcase image
└── README.md               # This file
```

## 🎨 Design Features

### Color Scheme
- **Primary**: #6366f1 (Indigo)
- **Secondary**: #8b5cf6 (Purple)
- **Success**: #10b981 (Emerald)
- **Warning**: #f59e0b (Amber)
- **Error**: #ef4444 (Red)
- **Gray Scale**: Various shades for text and backgrounds

### Typography
- **Font Family**: Inter (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700
- **Responsive**: Scales appropriately on different screen sizes

### Components
- **Cards**: Product cards, category cards, offer cards
- **Buttons**: Primary, secondary, outline, icon buttons
- **Forms**: Search bars, newsletter forms
- **Navigation**: Header nav, mobile menu, bottom nav
- **Overlays**: Cart sidebar, mobile menu, modals

## 📱 Responsive Breakpoints

- **Desktop**: 1024px and above
- **Tablet**: 768px - 1023px
- **Mobile**: 480px - 767px
- **Small Mobile**: Below 480px

## ⚡ JavaScript Features

### Web Version
- Mobile menu toggle
- Cart sidebar functionality
- Product filtering
- Search functionality
- Wishlist management
- Smooth scrolling
- Scroll animations
- Newsletter subscription
- Notification system

### Mobile Version
- Banner slider with touch swipe
- Bottom navigation
- Cart drawer
- Touch gestures
- Pull-to-refresh
- Countdown timer
- Product actions
- Toast notifications

## 🛠️ Customization

### Colors
Update the CSS custom properties in `style.css`:
```css
:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    /* Add more custom properties */
}
```

### Images
Replace images in the `images/` folder:
- `hero-banner.png` - Main hero section image
- `products-grid.png` - Product showcase image
- Add product images as needed

### Content
- Update product information in HTML files
- Modify categories and navigation items
- Customize footer links and information

## 📦 Dependencies

### External Libraries
- **Font Awesome 6.0.0**: Icons
- **Google Fonts (Inter)**: Typography

### No Build Process Required
This template uses vanilla HTML, CSS, and JavaScript - no build tools or frameworks required.

## 🚀 Getting Started

1. **Download/Clone** the template
2. **Open** `web/index.html` for the web version
3. **Open** `mobile/index.html` for the mobile version
4. **Customize** colors, content, and images as needed
5. **Deploy** to your web server

## 📱 Mobile Testing

For best mobile experience:
1. Open `mobile/index.html` in a mobile browser
2. Use browser dev tools mobile simulation
3. Test on actual mobile devices
4. Consider adding to home screen for app-like experience

## 🎯 Use Cases

- **E-commerce Stores**: Online retail websites
- **Product Catalogs**: Showcase products and services
- **Mobile Commerce**: Mobile-first shopping experiences
- **Marketplace**: Multi-vendor platforms
- **Fashion Stores**: Clothing and accessories
- **Electronics**: Tech product stores
- **General Retail**: Any product-based business

## 🔧 Browser Support

- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+
- **Mobile Browsers**: iOS Safari 14+, Chrome Mobile 90+

## 📄 License

This template is provided as-is for educational and commercial use. Feel free to modify and customize according to your needs.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

## 📞 Support

For questions or support, please refer to the documentation or create an issue in the repository.

---

**Built with ❤️ for modern e-commerce experiences**
