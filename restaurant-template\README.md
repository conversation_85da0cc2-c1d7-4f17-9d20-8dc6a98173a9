# Bella Vista - Restaurant Template

A sophisticated fine dining restaurant template with elegant design and comprehensive functionality.

## Features

- **Interactive Menu System** - Tabbed menu with categories (Appetizers, Main Courses, Desserts, Beverages)
- **Reservation System** - Complete booking form with date/time validation
- **Chef Profiles** - Showcase your culinary team
- **Restaurant Story** - Brand narrative and statistics
- **Contact Information** - Location, hours, and contact details
- **Responsive Design** - Works perfectly on all devices

## Design

- **Color Scheme**: Warm reds and elegant typography
- **Typography**: Playfair Display for headings, Inter for body text
- **Layout**: Modern card-based design with smooth animations
- **Images**: AI-generated restaurant interior imagery

## Files

- `web/index.html` - Main restaurant website
- `css/style.css` - Complete styling (300+ lines)
- `js/main.js` - Interactive functionality (200+ lines)
- `about.txt` - Comprehensive project documentation

## Usage

1. Open `web/index.html` in your browser
2. Customize colors, content, and branding
3. Replace placeholder images with your restaurant photos
4. Update menu items, prices, and restaurant information
5. Deploy to your web server

## Customization

- Update restaurant name and branding in HTML
- Modify color scheme in CSS custom properties
- Replace menu items with your actual menu
- Add your restaurant's contact information
- Include your chef profiles and restaurant story

Perfect for fine dining restaurants, cafes, bistros, and culinary establishments.
