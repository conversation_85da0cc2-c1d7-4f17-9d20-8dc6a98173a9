const winston = require('winston');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');

class Logger {
  constructor(module = 'app') {
    this.module = module;
    this.logger = this.createLogger();
  }

  createLogger() {
    // Create logs directory
    const logsDir = path.join(app.getPath('userData'), 'logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    // Define log format
    const logFormat = winston.format.combine(
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
      winston.format.errors({ stack: true }),
      winston.format.printf(({ timestamp, level, message, module, stack, ...meta }) => {
        let log = `${timestamp} [${level.toUpperCase()}] [${module || this.module}] ${message}`;
        
        if (Object.keys(meta).length > 0) {
          log += ` ${JSON.stringify(meta)}`;
        }
        
        if (stack) {
          log += `\n${stack}`;
        }
        
        return log;
      })
    );

    // Create transports
    const transports = [
      // Console transport for development
      new winston.transports.Console({
        level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
        format: winston.format.combine(
          winston.format.colorize(),
          logFormat
        )
      }),

      // File transport for all logs
      new winston.transports.File({
        filename: path.join(logsDir, 'remoteit.log'),
        level: 'debug',
        format: logFormat,
        maxsize: 10 * 1024 * 1024, // 10MB
        maxFiles: 5,
        tailable: true
      }),

      // Error file transport
      new winston.transports.File({
        filename: path.join(logsDir, 'error.log'),
        level: 'error',
        format: logFormat,
        maxsize: 10 * 1024 * 1024, // 10MB
        maxFiles: 3,
        tailable: true
      })
    ];

    return winston.createLogger({
      level: 'debug',
      format: logFormat,
      transports: transports,
      exitOnError: false
    });
  }

  debug(message, meta = {}) {
    this.logger.debug(message, { module: this.module, ...meta });
  }

  info(message, meta = {}) {
    this.logger.info(message, { module: this.module, ...meta });
  }

  warn(message, meta = {}) {
    this.logger.warn(message, { module: this.module, ...meta });
  }

  error(message, meta = {}) {
    this.logger.error(message, { module: this.module, ...meta });
  }

  fatal(message, meta = {}) {
    this.logger.error(`FATAL: ${message}`, { module: this.module, ...meta });
  }

  // Performance logging
  time(label) {
    console.time(`${this.module}:${label}`);
  }

  timeEnd(label) {
    console.timeEnd(`${this.module}:${label}`);
  }

  // Structured logging for specific events
  logConnection(event, data) {
    this.info(`Connection ${event}`, {
      event: 'connection',
      action: event,
      ...data
    });
  }

  logAuth(event, data) {
    this.info(`Auth ${event}`, {
      event: 'auth',
      action: event,
      ...data
    });
  }

  logPerformance(metric, value, unit = 'ms') {
    this.info(`Performance metric: ${metric}`, {
      event: 'performance',
      metric: metric,
      value: value,
      unit: unit
    });
  }

  logSecurity(event, data) {
    this.warn(`Security event: ${event}`, {
      event: 'security',
      action: event,
      ...data
    });
  }

  // Log cleanup
  static cleanup() {
    const logsDir = path.join(app.getPath('userData'), 'logs');
    
    if (!fs.existsSync(logsDir)) {
      return;
    }

    const files = fs.readdirSync(logsDir);
    const now = Date.now();
    const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days

    files.forEach(file => {
      const filePath = path.join(logsDir, file);
      const stats = fs.statSync(filePath);
      
      if (now - stats.mtime.getTime() > maxAge) {
        fs.unlinkSync(filePath);
        console.log(`Deleted old log file: ${file}`);
      }
    });
  }
}

module.exports = Logger;
