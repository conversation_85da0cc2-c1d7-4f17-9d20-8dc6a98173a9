const { expect } = require('chai');
const sinon = require('sinon');
const AuthManager = require('../../src/auth/AuthManager');

describe('AuthManager Unit Tests', () => {
  let authManager;
  let mockStore;
  let mockAxios;

  beforeEach(() => {
    // Mock electron-store
    mockStore = {
      get: sinon.stub(),
      set: sinon.stub(),
      delete: sinon.stub()
    };

    // Mock axios
    mockAxios = {
      post: sinon.stub(),
      get: sinon.stub()
    };

    // Create AuthManager instance with mocked dependencies
    authManager = new AuthManager();
    authManager.store = mockStore;
    authManager.axios = mockAxios;
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('Initialization', () => {
    it('should initialize with default settings', () => {
      expect(authManager.currentUser).to.be.null;
      expect(authManager.accessToken).to.be.null;
      expect(authManager.refreshToken).to.be.null;
      expect(authManager.deviceId).to.be.a('string');
    });

    it('should generate device ID if not exists', () => {
      mockStore.get.withArgs('deviceId').returns(null);
      
      const deviceId = authManager.getOrCreateDeviceId();
      
      expect(deviceId).to.be.a('string');
      expect(deviceId).to.have.length.greaterThan(0);
      expect(mockStore.set).to.have.been.calledWith('deviceId', deviceId);
    });

    it('should use existing device ID', () => {
      const existingId = 'existing-device-id';
      mockStore.get.withArgs('deviceId').returns(existingId);
      
      const deviceId = authManager.getOrCreateDeviceId();
      
      expect(deviceId).to.equal(existingId);
      expect(mockStore.set).to.not.have.been.called;
    });
  });

  describe('Login', () => {
    const mockCredentials = {
      email: '<EMAIL>',
      password: 'password123'
    };

    const mockResponse = {
      data: {
        user: { id: '1', email: '<EMAIL>', name: 'Test User' },
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
        expiresIn: 3600
      }
    };

    it('should login successfully with valid credentials', async () => {
      mockAxios.post.resolves(mockResponse);
      
      const result = await authManager.login(mockCredentials);
      
      expect(result.success).to.be.true;
      expect(result.user).to.deep.equal(mockResponse.data.user);
      expect(authManager.currentUser).to.deep.equal(mockResponse.data.user);
      expect(authManager.accessToken).to.equal(mockResponse.data.accessToken);
      expect(authManager.refreshToken).to.equal(mockResponse.data.refreshToken);
    });

    it('should handle invalid credentials', async () => {
      mockAxios.post.rejects({
        response: { status: 401, data: { error: 'Invalid credentials' } }
      });
      
      const result = await authManager.login(mockCredentials);
      
      expect(result.success).to.be.false;
      expect(result.error).to.equal('Invalid credentials');
    });

    it('should handle MFA requirement', async () => {
      mockAxios.post.rejects({
        response: { 
          status: 403, 
          data: { error: 'MFA required', requiresMfa: true } 
        }
      });
      
      const result = await authManager.login(mockCredentials);
      
      expect(result.success).to.be.false;
      expect(result.requiresMfa).to.be.true;
    });

    it('should handle network errors', async () => {
      mockAxios.post.rejects(new Error('Network error'));
      
      const result = await authManager.login(mockCredentials);
      
      expect(result.success).to.be.false;
      expect(result.error).to.include('connection');
    });

    it('should setup token refresh timer', async () => {
      mockAxios.post.resolves(mockResponse);
      const setupSpy = sinon.spy(authManager, 'setupTokenRefresh');
      
      await authManager.login(mockCredentials);
      
      expect(setupSpy).to.have.been.calledWith(3600);
    });
  });

  describe('Token Management', () => {
    beforeEach(() => {
      authManager.accessToken = 'current-token';
      authManager.refreshToken = 'current-refresh-token';
      authManager.currentUser = { id: '1', email: '<EMAIL>' };
    });

    it('should refresh access token successfully', async () => {
      const mockRefreshResponse = {
        data: {
          accessToken: 'new-access-token',
          refreshToken: 'new-refresh-token',
          expiresIn: 3600
        }
      };

      mockAxios.post.resolves(mockRefreshResponse);
      
      await authManager.refreshAccessToken();
      
      expect(authManager.accessToken).to.equal('new-access-token');
      expect(authManager.refreshToken).to.equal('new-refresh-token');
    });

    it('should handle refresh token expiration', async () => {
      mockAxios.post.rejects({
        response: { status: 401, data: { error: 'Invalid refresh token' } }
      });
      
      const clearSessionSpy = sinon.spy(authManager, 'clearSession');
      
      try {
        await authManager.refreshAccessToken();
      } catch (error) {
        expect(clearSessionSpy).to.have.been.called;
      }
    });

    it('should validate token successfully', async () => {
      mockAxios.get.resolves({
        data: { user: authManager.currentUser }
      });
      
      const result = await authManager.validateToken();
      
      expect(result).to.be.true;
    });

    it('should handle token validation failure', async () => {
      mockAxios.get.rejects({
        response: { status: 401 }
      });
      
      mockAxios.post.resolves({
        data: { accessToken: 'new-token', expiresIn: 3600 }
      });
      
      const result = await authManager.validateToken();
      
      expect(result).to.be.true;
      expect(authManager.accessToken).to.equal('new-token');
    });
  });

  describe('Logout', () => {
    beforeEach(() => {
      authManager.accessToken = 'current-token';
      authManager.currentUser = { id: '1', email: '<EMAIL>' };
    });

    it('should logout successfully', async () => {
      mockAxios.post.resolves({});
      
      const result = await authManager.logout();
      
      expect(result.success).to.be.true;
      expect(authManager.currentUser).to.be.null;
      expect(authManager.accessToken).to.be.null;
      expect(authManager.refreshToken).to.be.null;
    });

    it('should clear session even if server request fails', async () => {
      mockAxios.post.rejects(new Error('Network error'));
      
      const result = await authManager.logout();
      
      expect(result.success).to.be.true;
      expect(authManager.currentUser).to.be.null;
    });
  });

  describe('Token Storage', () => {
    it('should save tokens securely', () => {
      authManager.accessToken = 'test-access-token';
      authManager.refreshToken = 'test-refresh-token';
      authManager.currentUser = { id: '1', email: '<EMAIL>' };
      
      authManager.saveTokens();
      
      expect(mockStore.set).to.have.been.calledWith('tokens');
      const savedData = mockStore.set.getCall(0).args[1];
      expect(savedData).to.have.property('iv');
      expect(savedData).to.have.property('encrypted');
      expect(savedData).to.have.property('authTag');
    });

    it('should load tokens from storage', () => {
      const mockEncryptedData = {
        iv: 'mock-iv',
        encrypted: 'mock-encrypted-data',
        authTag: 'mock-auth-tag'
      };
      
      mockStore.get.withArgs('tokens').returns(mockEncryptedData);
      
      // Mock the decrypt method to return valid JSON
      sinon.stub(authManager, 'decrypt').returns(JSON.stringify({
        accessToken: 'stored-access-token',
        refreshToken: 'stored-refresh-token',
        user: { id: '1', email: '<EMAIL>' }
      }));
      
      authManager.loadStoredTokens();
      
      expect(authManager.accessToken).to.equal('stored-access-token');
      expect(authManager.refreshToken).to.equal('stored-refresh-token');
      expect(authManager.currentUser.email).to.equal('<EMAIL>');
    });

    it('should handle corrupted token storage', () => {
      mockStore.get.withArgs('tokens').returns('corrupted-data');
      
      // Should not throw error
      expect(() => authManager.loadStoredTokens()).to.not.throw();
      expect(mockStore.delete).to.have.been.calledWith('tokens');
    });
  });

  describe('Device Information', () => {
    it('should generate device info', () => {
      const deviceInfo = authManager.getDeviceInfo();
      
      expect(deviceInfo).to.have.property('platform');
      expect(deviceInfo).to.have.property('arch');
      expect(deviceInfo).to.have.property('hostname');
      expect(deviceInfo).to.have.property('version');
      expect(deviceInfo).to.have.property('userAgent');
    });
  });

  describe('Encryption/Decryption', () => {
    it('should encrypt and decrypt data correctly', () => {
      const testData = 'sensitive information';
      
      const encrypted = authManager.encrypt(testData);
      const decrypted = authManager.decrypt(encrypted);
      
      expect(decrypted).to.equal(testData);
    });

    it('should produce different encrypted output for same input', () => {
      const testData = 'test data';
      
      const encrypted1 = authManager.encrypt(testData);
      const encrypted2 = authManager.encrypt(testData);
      
      expect(encrypted1.encrypted).to.not.equal(encrypted2.encrypted);
      expect(encrypted1.iv).to.not.equal(encrypted2.iv);
    });
  });

  describe('Authentication State', () => {
    it('should return correct authentication status', () => {
      expect(authManager.isAuthenticated()).to.be.false;
      
      authManager.accessToken = 'test-token';
      authManager.currentUser = { id: '1' };
      
      expect(authManager.isAuthenticated()).to.be.true;
    });

    it('should return current user', () => {
      const user = { id: '1', email: '<EMAIL>' };
      authManager.currentUser = user;
      
      expect(authManager.getCurrentUser()).to.deep.equal(user);
    });

    it('should return access token', () => {
      const token = 'test-access-token';
      authManager.accessToken = token;
      
      expect(authManager.getAccessToken()).to.equal(token);
    });
  });
});
