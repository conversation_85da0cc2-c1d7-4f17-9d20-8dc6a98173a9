// Futuristic Tech Website JavaScript

// Performance optimization: Preload critical resources
function preloadResources() {
    const criticalImages = [
        // Add any critical images here
    ];

    criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });
}

// Loading screen management
function initLoadingScreen() {
    const loadingScreen = document.getElementById('loadingScreen');
    let loadingComplete = false;

    // Simulate loading time (minimum 2 seconds for effect)
    const minLoadTime = 2000;
    const startTime = Date.now();

    const hideLoadingScreen = () => {
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minLoadTime - elapsedTime);

        setTimeout(() => {
            loadingScreen.classList.add('hidden');
            document.body.style.overflow = '';

            // Initialize animations after loading
            setTimeout(() => {
                initializeAnimations();
            }, 500);
        }, remainingTime);
    };

    // Hide loading screen when everything is loaded
    if (document.readyState === 'complete') {
        hideLoadingScreen();
    } else {
        window.addEventListener('load', hideLoadingScreen);
    }

    // Prevent scrolling during loading
    document.body.style.overflow = 'hidden';
}

// Initialize all animations after loading
function initializeAnimations() {
    initScrollAnimations();
    initCounterAnimations();
    initParticleEffects();
    initTypingEffect();
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize loading screen first
    initLoadingScreen();

    // Initialize non-animation features immediately
    initSmoothScrolling();
    initHoverEffects();
    initMobileMenu();
    initContactForm();
    initLazyLoading();

    // Start performance monitoring
    initPerformanceMonitoring();

    // Preload resources
    preloadResources();
});

// Scroll-based animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');

                // Add staggered animation delays
                const siblings = Array.from(entry.target.parentElement.children);
                const index = siblings.indexOf(entry.target);
                entry.target.style.animationDelay = `${index * 0.2}s`;
            }
        });
    }, observerOptions);

    // Observe all animated elements with different animation types
    const leftElements = document.querySelectorAll('.hero-left .stat-item, .solution-card');
    const rightElements = document.querySelectorAll('.hero-right .stat-card, .innovation-card');
    const centerElements = document.querySelectorAll('.service-item, .desc-card');

    leftElements.forEach(el => {
        el.classList.add('animate-slide-left');
        observer.observe(el);
    });

    rightElements.forEach(el => {
        el.classList.add('animate-slide-right');
        observer.observe(el);
    });

    centerElements.forEach(el => {
        el.classList.add('animate-fade-scale');
        observer.observe(el);
    });
}

// Counter animations for statistics
function initCounterAnimations() {
    const counters = document.querySelectorAll('.stat-number, .center-stat .number, .stat-card .number');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.textContent);
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                counter.textContent = target + (counter.textContent.includes('%') ? '%' : '+');
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current) + (counter.textContent.includes('%') ? '%' : '+');
            }
        }, 16);
    };

    const counterObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !entry.target.classList.contains('animated')) {
                entry.target.classList.add('animated');
                setTimeout(() => animateCounter(entry.target), 500);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => counterObserver.observe(counter));
}

// Optimized particle effects for the sphere
function initParticleEffects() {
    const sphereContainer = document.querySelector('.sphere-container');
    if (!sphereContainer) return;

    // Reduce particles on mobile for performance
    const isMobile = window.innerWidth <= 768;
    const particleCount = isMobile ? 10 : 20;

    // Use requestAnimationFrame for better performance
    let animationId;
    const particles = [];

    // Create floating particles around the sphere
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.cssText = `
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00ff88;
            border-radius: 50%;
            opacity: 0;
            pointer-events: none;
            will-change: transform, opacity;
        `;
        sphereContainer.appendChild(particle);

        particles.push({
            element: particle,
            angle: Math.random() * Math.PI * 2,
            radius: 150 + Math.random() * 50,
            speed: 0.01 + Math.random() * 0.02,
            opacity: 0
        });
    }

    // Optimized animation loop
    function animateParticles() {
        particles.forEach(particle => {
            particle.angle += particle.speed;
            const x = Math.cos(particle.angle) * particle.radius;
            const y = Math.sin(particle.angle) * particle.radius;

            // Fade in/out based on position
            const distanceFromCenter = Math.sqrt(x * x + y * y);
            particle.opacity = Math.max(0, Math.min(1, (distanceFromCenter - 100) / 100));

            particle.element.style.transform = `translate(${x}px, ${y}px)`;
            particle.element.style.opacity = particle.opacity;
        });

        animationId = requestAnimationFrame(animateParticles);
    }

    // Start animation when sphere is visible
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateParticles();
            } else {
                cancelAnimationFrame(animationId);
            }
        });
    });

    observer.observe(sphereContainer);
}

// Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    } else {
        // Fallback for older browsers
        images.forEach(img => {
            img.src = img.dataset.src;
            img.classList.remove('lazy');
        });
    }
}

// Performance monitoring
function initPerformanceMonitoring() {
    // Monitor frame rate
    let lastTime = performance.now();
    let frameCount = 0;
    let fps = 60;

    function measureFPS() {
        const currentTime = performance.now();
        frameCount++;

        if (currentTime - lastTime >= 1000) {
            fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
            frameCount = 0;
            lastTime = currentTime;

            // Reduce animations if FPS is low
            if (fps < 30) {
                document.body.classList.add('low-performance');
            } else {
                document.body.classList.remove('low-performance');
            }
        }

        requestAnimationFrame(measureFPS);
    }

    requestAnimationFrame(measureFPS);
}

// Smooth scrolling for navigation
function initSmoothScrolling() {
    const navLinks = document.querySelectorAll('.nav-menu a');
    
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            const href = link.getAttribute('href');
            if (href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
}

// Enhanced hover effects
function initHoverEffects() {
    // Card hover effects
    const cards = document.querySelectorAll('.solution-card, .innovation-card, .software-section, .service-item, .desc-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-5px)';
            card.style.boxShadow = '0 10px 30px rgba(0, 255, 136, 0.2)';
            card.style.borderColor = 'rgba(0, 255, 136, 0.3)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0)';
            card.style.boxShadow = 'none';
            card.style.borderColor = 'rgba(255, 255, 255, 0.1)';
        });
    });

    // Sphere interaction
    const sphere = document.querySelector('.sphere');
    if (sphere) {
        sphere.addEventListener('mouseenter', () => {
            sphere.style.transform = 'scale(1.05)';
            sphere.style.boxShadow = '0 25px 50px rgba(0, 255, 136, 0.3)';
        });
        
        sphere.addEventListener('mouseleave', () => {
            sphere.style.transform = 'scale(1)';
            sphere.style.boxShadow = '0 20px 40px rgba(255, 255, 255, 0.1)';
        });
    }
}

// Typing effect for main title
function initTypingEffect() {
    const mainTitle = document.querySelector('.main-title');
    if (!mainTitle) return;

    const text = mainTitle.textContent;
    mainTitle.textContent = '';
    mainTitle.style.borderRight = '2px solid #00ff88';

    let i = 0;
    const typeWriter = () => {
        if (i < text.length) {
            mainTitle.textContent += text.charAt(i);
            i++;
            setTimeout(typeWriter, 100);
        } else {
            // Remove cursor after typing is complete
            setTimeout(() => {
                mainTitle.style.borderRight = 'none';
            }, 1000);
        }
    };

    // Start typing effect after a delay
    setTimeout(typeWriter, 1000);
}

// Mobile menu functionality
function initMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-menu a');

    if (!mobileToggle || !mobileMenu) return;

    // Toggle mobile menu
    mobileToggle.addEventListener('click', () => {
        mobileToggle.classList.toggle('active');
        mobileMenu.classList.toggle('active');

        // Prevent body scroll when menu is open
        if (mobileMenu.classList.contains('active')) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = '';
        }
    });

    // Close menu when clicking on a link
    mobileNavLinks.forEach(link => {
        link.addEventListener('click', () => {
            mobileToggle.classList.remove('active');
            mobileMenu.classList.remove('active');
            document.body.style.overflow = '';
        });
    });

    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
        if (!mobileToggle.contains(e.target) && !mobileMenu.contains(e.target)) {
            mobileToggle.classList.remove('active');
            mobileMenu.classList.remove('active');
            document.body.style.overflow = '';
        }
    });

    // Close menu on window resize
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768) {
            mobileToggle.classList.remove('active');
            mobileMenu.classList.remove('active');
            document.body.style.overflow = '';
        }
    });
}

// Contact form functionality
function initContactForm() {
    const form = document.getElementById('contactForm');
    const submitBtn = form.querySelector('.submit-btn');

    if (!form) return;

    // Form validation
    const validateField = (field, errorElement, validationFn) => {
        const isValid = validationFn(field.value);
        if (isValid) {
            field.classList.remove('error');
            errorElement.classList.remove('show');
            return true;
        } else {
            field.classList.add('error');
            errorElement.classList.add('show');
            return false;
        }
    };

    // Validation functions
    const validators = {
        name: (value) => value.trim().length >= 2,
        email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
        message: (value) => value.trim().length >= 10
    };

    // Real-time validation
    ['name', 'email', 'message'].forEach(fieldName => {
        const field = document.getElementById(fieldName);
        const errorElement = document.getElementById(fieldName + 'Error');

        field.addEventListener('blur', () => {
            validateField(field, errorElement, validators[fieldName]);
        });

        field.addEventListener('input', () => {
            if (field.classList.contains('error')) {
                validateField(field, errorElement, validators[fieldName]);
            }
        });
    });

    // Form submission
    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        // Validate all fields
        const nameField = document.getElementById('name');
        const emailField = document.getElementById('email');
        const messageField = document.getElementById('message');

        const nameError = document.getElementById('nameError');
        const emailError = document.getElementById('emailError');
        const messageError = document.getElementById('messageError');

        const isNameValid = validateField(nameField, nameError, validators.name);
        const isEmailValid = validateField(emailField, emailError, validators.email);
        const isMessageValid = validateField(messageField, messageError, validators.message);

        // Set error messages
        if (!isNameValid) nameError.textContent = 'Name must be at least 2 characters long';
        if (!isEmailValid) emailError.textContent = 'Please enter a valid email address';
        if (!isMessageValid) messageError.textContent = 'Message must be at least 10 characters long';

        if (!isNameValid || !isEmailValid || !isMessageValid) {
            return;
        }

        // Show loading state
        submitBtn.classList.add('loading');

        // Simulate form submission (replace with actual API call)
        try {
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Success feedback
            showNotification('Message sent successfully! We\'ll get back to you soon.', 'success');
            form.reset();

        } catch (error) {
            // Error feedback
            showNotification('Failed to send message. Please try again.', 'error');
        } finally {
            submitBtn.classList.remove('loading');
        }
    });
}

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(n => n.remove());

    // Create notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'success' ? '#00ff88' : type === 'error' ? '#ff4444' : '#0088ff'};
        color: ${type === 'success' ? '#000000' : '#ffffff'};
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 400px;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Auto remove
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

// Parallax scrolling effect
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const parallaxElements = document.querySelectorAll('.sphere-container, .main-title');
    
    parallaxElements.forEach(element => {
        const speed = 0.5;
        element.style.transform = `translateY(${scrolled * speed}px)`;
    });
});

// Add CSS animations via JavaScript
const style = document.createElement('style');
style.textContent = `
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }
    
    .animate-in {
        opacity: 1;
        transform: translateY(0);
    }
    
    .particle {
        z-index: 1;
    }
    
    .solution-card, .innovation-card, .software-section, .service-item, .desc-card {
        transition: all 0.3s ease;
    }
    
    .sphere {
        transition: all 0.3s ease;
    }
    
    @keyframes glow {
        0%, 100% { box-shadow: 0 0 20px rgba(0, 255, 136, 0.3); }
        50% { box-shadow: 0 0 40px rgba(0, 255, 136, 0.6); }
    }
    
    .stat-number, .center-stat .number, .stat-card .number {
        transition: color 0.3s ease;
    }
    
    .stat-number:hover, .center-stat .number:hover, .stat-card .number:hover {
        color: #00ffaa;
        text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    }
`;
document.head.appendChild(style);

// Performance optimization: Throttle scroll events
function throttle(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply throttling to scroll events
window.addEventListener('scroll', throttle(() => {
    // Scroll-based animations can be added here
}, 16));

// Add loading animation
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
    
    // Trigger initial animations
    setTimeout(() => {
        const heroElements = document.querySelectorAll('.hero-left, .hero-center, .hero-right');
        heroElements.forEach((el, index) => {
            setTimeout(() => {
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            }, index * 200);
        });
    }, 500);
});

// Initial setup for hero elements
document.addEventListener('DOMContentLoaded', () => {
    const heroElements = document.querySelectorAll('.hero-left, .hero-center, .hero-right');
    heroElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'all 0.8s ease';
    });
});
