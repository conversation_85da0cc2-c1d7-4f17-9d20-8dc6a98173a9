# Content Generator API Documentation

## 🚀 Overview

The Content Generator API is a local Node.js application that provides image and text generation capabilities using Together.ai's API. It features a robust queue system, seed management for image reproduction, and comprehensive rate limiting.

## ✨ Features

- **Image Generation**: High-quality images using FLUX.1-schnell-Free model
- **Text Generation**: AI-powered text using DeepSeek-R1 model
- **Seed Management**: Automatic seed capture and reproduction system
- **Queue System**: Efficient request processing with priority handling
- **Rate Limiting**: Built-in protection against API abuse
- **File Storage**: Automatic saving of generated content
- **RESTful API**: Clean, well-documented endpoints

## 🏗️ Architecture

```
Content Generator API
├── Express.js Server (Port 7777)
├── PostgreSQL Database
├── Together.ai Integration
├── Queue Management System
├── Rate Limiting
└── File Storage System
```

## 📋 Prerequisites

- Node.js (v14 or higher)
- PostgreSQL database
- Together.ai API key

## ⚙️ Installation

1. **Clone and Install Dependencies**
   ```bash
   cd content_generator
   npm install
   ```

2. **Environment Configuration**
   Create `.env` file:
   ```env
   TOGETHER_API_KEY=your_together_ai_api_key
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=content_generator
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   PORT=7777
   ```

3. **Database Setup**
   The application automatically creates required tables on startup.

4. **Start the Server**
   ```bash
   npm start
   ```

## 🌐 API Endpoints

### Health Check
```http
GET /health
```
Returns server health status.

### Generate Content
```http
POST /api/generate
```

**Request Body:**
```json
{
  "type": "image",
  "prompt": "A beautiful sunset landscape",
  "width": 640,
  "height": 800,
  "seed": 12345,
  "timeout": 300
}
```

**Response:**
```json
{
  "success": true,
  "requestId": "1753475448770",
  "message": "Request queued for processing",
  "type": "image",
  "model": "black-forest-labs/FLUX.1-schnell-Free",
  "estimatedTime": "30-200 seconds"
}
```

### Check Status
```http
GET /api/status/{requestId}
```

**Response:**
```json
{
  "requestId": "1753475448770",
  "status": "completed",
  "type": "image",
  "prompt": "A beautiful sunset landscape",
  "filePath": "generated/images/1753475448770.png",
  "imageUrl": "/api/image/1753475448770",
  "seedUsed": **********,
  "createdAt": "2025-07-26T02:01:06.000Z",
  "completedAt": "2025-07-26T02:01:26.000Z"
}
```

### View Generated Image
```http
GET /api/image/{requestId}
```
Returns the generated image file.

### Available Models
```http
GET /api/models
```
Returns list of available AI models.

## 🌱 Seed Management System

### How Seeds Work

1. **New Image Generation (No Seed)**
   - Request sent without seed parameter
   - Together.ai generates random seed
   - Seed automatically captured and stored
   - Returned in response for future use

2. **Image Reproduction (With Seed)**
   - Use seed from previous generation
   - Generates identical image
   - Seed must match exact prompt and dimensions

### Seed Usage Examples

**Generate New Image:**
```bash
curl -X POST http://localhost:7777/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "type": "image",
    "prompt": "A red apple on white background",
    "width": 640,
    "height": 800
  }'
```

**Reproduce Exact Image:**
```bash
curl -X POST http://localhost:7777/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "type": "image",
    "prompt": "A red apple on white background",
    "width": 640,
    "height": 800,
    "seed": **********
  }'
```

## 📊 Rate Limits

- **Images**: 6 requests per minute
- **Text**: 60 requests per minute

Rate limits are automatically enforced and reset every minute.

## 📁 File Storage

Generated content is stored in:
```
generated/
├── images/          # PNG image files
│   ├── {requestId}.png
│   └── ...
└── texts/           # Text files
    ├── {requestId}.txt
    └── ...
```

## 🔄 Request Workflow

1. **Submit Request** → API validates and queues request
2. **Queue Processing** → Request processed based on priority
3. **AI Generation** → Content generated via Together.ai
4. **File Storage** → Result saved to local filesystem
5. **Database Update** → Status and metadata stored
6. **Response Ready** → Client can retrieve result

## 📈 Monitoring

### Queue Status
Monitor active requests and queue depth through logs.

### Rate Limit Status
Check current rate limit usage in server logs.

### Error Handling
All errors are logged with detailed information for debugging.

## 🛠️ Troubleshooting

### Common Issues

1. **Images Not Generating**
   - Check Together.ai API key
   - Verify network connectivity
   - Check rate limits

2. **Database Connection Errors**
   - Verify PostgreSQL is running
   - Check database credentials
   - Ensure database exists

3. **File Storage Issues**
   - Check disk space
   - Verify write permissions
   - Check generated/ directory exists

### Debug Mode
Enable detailed logging by setting `NODE_ENV=development`.

## 🧪 Testing

### Verified Test Results (July 26, 2025)

**✅ 3-Image Generation Test - ALL PASSED**
- Image 1: Snowy Mountain Peak (90.12 KB) - Completed in 20s
- Image 2: Tropical Parrot (62.88 KB) - Completed in 5s
- Image 3: Vintage Steam Train (134.18 KB) - Completed in 10s

Run the test suite to verify functionality:

```bash
# Test 3 image generation
node test-three-images.js

# Quick API test
node quick-test.js

# Generate two specific images
node generate-images-final.js
```

## 📝 API Response Codes

- `200` - Success
- `400` - Bad Request (invalid parameters)
- `404` - Request not found
- `429` - Rate limit exceeded
- `500` - Internal server error

## 🔒 Security

- Rate limiting prevents API abuse
- Input validation on all endpoints
- Secure file storage with proper permissions
- Environment variable protection for API keys

## 📞 Support

For issues or questions:
1. Check server logs for error details
2. Verify API key and database connectivity
3. Test with simple requests first
4. Review this documentation for proper usage

---

**Last Updated**: July 26, 2025  
**Version**: 2.2.0  
**API Base URL**: http://localhost:7777
