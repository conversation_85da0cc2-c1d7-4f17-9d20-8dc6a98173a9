const dgram = require('dgram');
const crypto = require('crypto');

// STUN Server Implementation for NAT Traversal
class STUNServer {
  constructor(options = {}) {
    this.port = options.port || 3478;
    this.host = options.host || '0.0.0.0';
    this.socket = null;
    
    // STUN message types
    this.MESSAGE_TYPES = {
      BINDING_REQUEST: 0x0001,
      BINDING_RESPONSE: 0x0101,
      BINDING_ERROR: 0x0111
    };
    
    // STUN attributes
    this.ATTRIBUTES = {
      MAPPED_ADDRESS: 0x0001,
      XOR_MAPPED_ADDRESS: 0x0020,
      ERROR_CODE: 0x0009,
      UNKNOWN_ATTRIBUTES: 0x000A,
      SOFTWARE: 0x8022
    };
    
    this.MAGIC_COOKIE = 0x2112A442;
    this.SOFTWARE_NAME = 'RemoteIt STUN Server 1.0';
    
    console.log(`STUN Server initializing on ${this.host}:${this.port}`);
  }

  start() {
    return new Promise((resolve, reject) => {
      this.socket = dgram.createSocket('udp4');
      
      this.socket.on('message', (msg, rinfo) => {
        this.handleMessage(msg, rinfo);
      });
      
      this.socket.on('error', (err) => {
        console.error('STUN Server error:', err);
        reject(err);
      });
      
      this.socket.bind(this.port, this.host, () => {
        console.log(`✅ STUN Server listening on ${this.host}:${this.port}`);
        resolve();
      });
    });
  }

  handleMessage(message, rinfo) {
    try {
      const stunMessage = this.parseSTUNMessage(message);
      
      if (stunMessage.type === this.MESSAGE_TYPES.BINDING_REQUEST) {
        this.handleBindingRequest(stunMessage, rinfo);
      } else {
        console.log(`Unknown STUN message type: 0x${stunMessage.type.toString(16)}`);
      }
      
    } catch (error) {
      console.error('Error handling STUN message:', error);
    }
  }

  parseSTUNMessage(buffer) {
    if (buffer.length < 20) {
      throw new Error('STUN message too short');
    }
    
    const type = buffer.readUInt16BE(0);
    const length = buffer.readUInt16BE(2);
    const magicCookie = buffer.readUInt32BE(4);
    const transactionId = buffer.slice(8, 20);
    
    if (magicCookie !== this.MAGIC_COOKIE) {
      throw new Error('Invalid STUN magic cookie');
    }
    
    const attributes = this.parseAttributes(buffer.slice(20, 20 + length));
    
    return {
      type,
      length,
      magicCookie,
      transactionId,
      attributes
    };
  }

  parseAttributes(buffer) {
    const attributes = [];
    let offset = 0;
    
    while (offset < buffer.length) {
      if (offset + 4 > buffer.length) break;
      
      const type = buffer.readUInt16BE(offset);
      const length = buffer.readUInt16BE(offset + 2);
      const value = buffer.slice(offset + 4, offset + 4 + length);
      
      attributes.push({ type, length, value });
      
      // Attributes are padded to 4-byte boundaries
      offset += 4 + Math.ceil(length / 4) * 4;
    }
    
    return attributes;
  }

  handleBindingRequest(stunMessage, rinfo) {
    console.log(`📡 STUN Binding Request from ${rinfo.address}:${rinfo.port}`);
    
    const response = this.createBindingResponse(stunMessage, rinfo);
    
    this.socket.send(response, rinfo.port, rinfo.address, (err) => {
      if (err) {
        console.error('Error sending STUN response:', err);
      } else {
        console.log(`✅ STUN Response sent to ${rinfo.address}:${rinfo.port}`);
      }
    });
  }

  createBindingResponse(request, rinfo) {
    const responseBuffer = Buffer.alloc(1024);
    let offset = 0;
    
    // STUN header
    responseBuffer.writeUInt16BE(this.MESSAGE_TYPES.BINDING_RESPONSE, offset);
    offset += 2;
    
    // Length (will be updated later)
    const lengthOffset = offset;
    offset += 2;
    
    // Magic cookie
    responseBuffer.writeUInt32BE(this.MAGIC_COOKIE, offset);
    offset += 4;
    
    // Transaction ID (copy from request)
    request.transactionId.copy(responseBuffer, offset);
    offset += 12;
    
    const attributesStart = offset;
    
    // XOR-MAPPED-ADDRESS attribute
    offset += this.addXorMappedAddress(responseBuffer, offset, rinfo, request.transactionId);
    
    // SOFTWARE attribute
    offset += this.addSoftwareAttribute(responseBuffer, offset);
    
    // Update length field
    const attributesLength = offset - attributesStart;
    responseBuffer.writeUInt16BE(attributesLength, lengthOffset);
    
    return responseBuffer.slice(0, offset);
  }

  addXorMappedAddress(buffer, offset, rinfo, transactionId) {
    const startOffset = offset;
    
    // Attribute type
    buffer.writeUInt16BE(this.ATTRIBUTES.XOR_MAPPED_ADDRESS, offset);
    offset += 2;
    
    // Attribute length
    buffer.writeUInt16BE(8, offset);
    offset += 2;
    
    // Reserved byte
    buffer.writeUInt8(0, offset);
    offset += 1;
    
    // Family (IPv4 = 0x01)
    buffer.writeUInt8(0x01, offset);
    offset += 1;
    
    // XOR'd port
    const xorPort = rinfo.port ^ (this.MAGIC_COOKIE >> 16);
    buffer.writeUInt16BE(xorPort, offset);
    offset += 2;
    
    // XOR'd IP address
    const ipParts = rinfo.address.split('.').map(part => parseInt(part));
    const ipBuffer = Buffer.from(ipParts);
    const magicCookieBuffer = Buffer.alloc(4);
    magicCookieBuffer.writeUInt32BE(this.MAGIC_COOKIE, 0);
    
    for (let i = 0; i < 4; i++) {
      buffer.writeUInt8(ipBuffer[i] ^ magicCookieBuffer[i], offset + i);
    }
    offset += 4;
    
    return offset - startOffset;
  }

  addSoftwareAttribute(buffer, offset) {
    const startOffset = offset;
    const softwareBytes = Buffer.from(this.SOFTWARE_NAME, 'utf8');
    
    // Attribute type
    buffer.writeUInt16BE(this.ATTRIBUTES.SOFTWARE, offset);
    offset += 2;
    
    // Attribute length
    buffer.writeUInt16BE(softwareBytes.length, offset);
    offset += 2;
    
    // Software name
    softwareBytes.copy(buffer, offset);
    offset += softwareBytes.length;
    
    // Padding to 4-byte boundary
    const padding = (4 - (softwareBytes.length % 4)) % 4;
    for (let i = 0; i < padding; i++) {
      buffer.writeUInt8(0, offset + i);
    }
    offset += padding;
    
    return offset - startOffset;
  }

  stop() {
    return new Promise((resolve) => {
      if (this.socket) {
        this.socket.close(() => {
          console.log('STUN Server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

// TURN Server Implementation (simplified)
class TURNServer {
  constructor(options = {}) {
    this.stunServer = new STUNServer(options);
    this.allocations = new Map(); // Track TURN allocations
    this.relayPorts = new Set(); // Available relay ports
    
    // Initialize relay port pool
    for (let port = 49152; port <= 49200; port++) {
      this.relayPorts.add(port);
    }
    
    console.log('TURN Server initialized with STUN capabilities');
  }

  async start() {
    await this.stunServer.start();
    console.log('✅ TURN Server started (STUN + Relay capabilities)');
  }

  async stop() {
    await this.stunServer.stop();
    
    // Close all relay allocations
    for (const [allocationId, allocation] of this.allocations) {
      if (allocation.socket) {
        allocation.socket.close();
      }
    }
    
    console.log('TURN Server stopped');
  }

  // Allocate a relay port for a client
  allocateRelay(clientInfo) {
    if (this.relayPorts.size === 0) {
      throw new Error('No relay ports available');
    }
    
    const port = this.relayPorts.values().next().value;
    this.relayPorts.delete(port);
    
    const allocationId = crypto.randomBytes(16).toString('hex');
    const socket = dgram.createSocket('udp4');
    
    const allocation = {
      id: allocationId,
      port: port,
      socket: socket,
      client: clientInfo,
      createdAt: Date.now(),
      lastActivity: Date.now()
    };
    
    socket.bind(port);
    socket.on('message', (msg, rinfo) => {
      this.handleRelayMessage(allocationId, msg, rinfo);
    });
    
    this.allocations.set(allocationId, allocation);
    
    console.log(`📡 TURN Relay allocated: ${allocationId} on port ${port}`);
    
    return {
      allocationId,
      relayPort: port,
      relayAddress: '0.0.0.0' // Server's public IP
    };
  }

  handleRelayMessage(allocationId, message, rinfo) {
    const allocation = this.allocations.get(allocationId);
    if (!allocation) {
      console.warn(`Unknown allocation: ${allocationId}`);
      return;
    }
    
    allocation.lastActivity = Date.now();
    
    // Relay the message to the peer
    // In a full implementation, this would handle TURN protocol
    console.log(`🔄 Relaying message for allocation ${allocationId}`);
  }

  // Cleanup expired allocations
  cleanupAllocations() {
    const now = Date.now();
    const maxAge = 10 * 60 * 1000; // 10 minutes
    
    for (const [allocationId, allocation] of this.allocations) {
      if (now - allocation.lastActivity > maxAge) {
        console.log(`🧹 Cleaning up expired allocation: ${allocationId}`);
        
        allocation.socket.close();
        this.relayPorts.add(allocation.port);
        this.allocations.delete(allocationId);
      }
    }
  }
}

module.exports = { STUNServer, TURNServer };

// Start server if run directly
if (require.main === module) {
  const turnServer = new TURNServer({
    port: process.env.STUN_PORT || 3478,
    host: process.env.STUN_HOST || '0.0.0.0'
  });
  
  turnServer.start().catch(error => {
    console.error('Failed to start TURN server:', error);
    process.exit(1);
  });
  
  // Cleanup interval
  setInterval(() => {
    turnServer.cleanupAllocations();
  }, 60000); // Every minute
  
  // Graceful shutdown
  process.on('SIGINT', async () => {
    console.log('Shutting down TURN server...');
    await turnServer.stop();
    process.exit(0);
  });
}
