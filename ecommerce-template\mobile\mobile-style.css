/* Mobile App Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: #f8fafc;
    color: #1f2937;
    overflow-x: hidden;
    padding-bottom: 80px;
}

/* Status Bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 20px;
    background: #000;
    color: white;
    font-size: 14px;
    font-weight: 600;
}

.status-right {
    display: flex;
    gap: 8px;
}

/* App Header */
.app-header {
    background: white;
    padding: 15px 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.location {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-size: 14px;
}

.location i:first-child {
    color: #6366f1;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 20px;
    color: #6b7280;
    cursor: pointer;
}

.notification-btn .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-btn {
    background: none;
    border: none;
    cursor: pointer;
}

.profile-btn img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

/* Search Container */
.search-container {
    padding: 15px 20px;
    background: white;
}

.search-bar {
    display: flex;
    align-items: center;
    background: #f3f4f6;
    border-radius: 25px;
    padding: 12px 16px;
    gap: 12px;
}

.search-bar i {
    color: #9ca3af;
    font-size: 16px;
}

.search-bar input {
    flex: 1;
    border: none;
    background: none;
    outline: none;
    font-size: 16px;
    color: #1f2937;
}

.search-bar input::placeholder {
    color: #9ca3af;
}

.filter-btn {
    background: #6366f1;
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

/* Main Content */
.main-content {
    padding: 0 0 20px 0;
}

/* Hero Banner */
.hero-banner {
    position: relative;
    margin: 0 20px 20px 20px;
    border-radius: 16px;
    overflow: hidden;
    height: 180px;
}

.banner-slide {
    position: relative;
    width: 100%;
    height: 100%;
    display: none;
}

.banner-slide.active {
    display: block;
}

.banner-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-content {
    position: absolute;
    top: 50%;
    left: 30px;
    transform: translateY(-50%);
    color: white;
}

.banner-content h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.banner-content p {
    font-size: 16px;
    margin-bottom: 15px;
    opacity: 0.9;
}

.shop-now-btn {
    background: white;
    color: #6366f1;
    border: none;
    padding: 8px 20px;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
}

.banner-indicators {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
}

.indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    cursor: pointer;
}

.indicator.active {
    background: white;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px 15px 20px;
}

.section-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.see-all {
    color: #6366f1;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

/* Categories */
.categories-section {
    margin-bottom: 25px;
}

.categories-scroll {
    display: flex;
    gap: 20px;
    padding: 0 20px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.categories-scroll::-webkit-scrollbar {
    display: none;
}

.category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 70px;
    cursor: pointer;
}

.category-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.category-item span {
    font-size: 12px;
    color: #6b7280;
    text-align: center;
}

/* Flash Sale */
.flash-sale {
    background: white;
    margin: 0 20px 25px 20px;
    border-radius: 16px;
    padding: 20px;
}

.flash-sale .section-header {
    padding: 0 0 15px 0;
}

.countdown {
    display: flex;
    align-items: center;
    gap: 2px;
    color: #ef4444;
    font-weight: 600;
    font-size: 16px;
}

.time-unit {
    background: #ef4444;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    min-width: 30px;
    text-align: center;
}

.flash-products {
    display: flex;
    gap: 15px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.flash-products::-webkit-scrollbar {
    display: none;
}

.flash-product {
    position: relative;
    min-width: 140px;
    background: #f8fafc;
    border-radius: 12px;
    overflow: hidden;
}

.flash-product img {
    width: 100%;
    height: 100px;
    object-fit: cover;
}

.discount-badge {
    position: absolute;
    top: 8px;
    left: 8px;
    background: #ef4444;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
}

.flash-product .product-info {
    padding: 10px;
}

.flash-product h4 {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #1f2937;
}

.flash-product .price {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 8px;
}

.flash-product .current {
    font-size: 14px;
    font-weight: 700;
    color: #6366f1;
}

.flash-product .original {
    font-size: 12px;
    color: #9ca3af;
    text-decoration: line-through;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 5px;
}

.progress {
    height: 100%;
    background: #ef4444;
    border-radius: 2px;
}

.sold {
    font-size: 10px;
    color: #6b7280;
}

/* Recommended Products */
.recommended-section {
    margin-bottom: 25px;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 0 20px;
}

.product-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.product-image {
    position: relative;
    height: 140px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255,255,255,0.9);
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #6b7280;
}

.wishlist-btn.active {
    color: #ef4444;
}

.product-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.product-badge.new {
    background: #10b981;
    color: white;
}

.product-badge.hot {
    background: #f59e0b;
    color: white;
}

.product-badge.sale {
    background: #ef4444;
    color: white;
}

.product-details {
    padding: 12px;
    position: relative;
}

.product-details h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 6px;
    color: #1f2937;
    line-height: 1.3;
}

.rating {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 8px;
}

.stars {
    display: flex;
    gap: 2px;
}

.stars i {
    font-size: 12px;
    color: #fbbf24;
}

.rating-count {
    font-size: 11px;
    color: #9ca3af;
}

.product-details .price {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 700;
}

.product-details .price .current {
    color: #6366f1;
    font-size: 16px;
}

.product-details .price .original {
    color: #9ca3af;
    font-size: 14px;
    text-decoration: line-through;
}

.add-to-cart {
    position: absolute;
    bottom: 12px;
    right: 12px;
    background: #6366f1;
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
}

/* Special Offers */
.special-offers {
    padding: 0 20px;
    margin-bottom: 25px;
}

.offer-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
}

.offer-content h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 5px;
}

.offer-content p {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 15px;
}

.claim-btn {
    background: white;
    color: #6366f1;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
}

.offer-image i {
    font-size: 40px;
    opacity: 0.8;
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    display: flex;
    justify-content: space-around;
    padding: 10px 0 20px 0;
    box-shadow: 0 -2px 20px rgba(0,0,0,0.1);
    z-index: 1000;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    color: #9ca3af;
    font-size: 12px;
    position: relative;
    padding: 8px 12px;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.nav-item.active {
    color: #6366f1;
    background: rgba(99, 102, 241, 0.1);
}

.nav-item i {
    font-size: 20px;
}

.nav-badge {
    position: absolute;
    top: 2px;
    right: 8px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Cart Drawer */
.cart-drawer {
    position: fixed;
    bottom: -100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 20px 20px 0 0;
    max-height: 70vh;
    z-index: 2000;
    transition: bottom 0.3s ease;
    display: flex;
    flex-direction: column;
}

.cart-drawer.active {
    bottom: 0;
}

.drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.drawer-header h3 {
    font-size: 18px;
    font-weight: 600;
}

.close-drawer {
    background: none;
    border: none;
    font-size: 20px;
    color: #9ca3af;
    cursor: pointer;
}

.cart-items {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.cart-item {
    display: flex;
    gap: 12px;
    padding: 15px 0;
    border-bottom: 1px solid #f3f4f6;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item img {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
}

.item-info {
    flex: 1;
}

.item-info h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
}

.item-price {
    color: #6366f1;
    font-weight: 700;
    margin-bottom: 8px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.qty-btn {
    width: 28px;
    height: 28px;
    border: 1px solid #e5e7eb;
    background: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
}

.quantity {
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.remove-item {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    font-size: 16px;
    padding: 5px;
}

.cart-summary {
    padding: 20px;
    border-top: 1px solid #e5e7eb;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
}

.summary-row.total {
    font-size: 16px;
    font-weight: 700;
    color: #1f2937;
    border-top: 1px solid #e5e7eb;
    padding-top: 10px;
    margin-top: 10px;
}

.checkout-btn {
    width: 100%;
    background: #6366f1;
    color: white;
    border: none;
    padding: 16px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 15px;
}

/* Overlay */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 1500;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Animations */
@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.slide-up {
    animation: slideUp 0.3s ease;
}

.fade-in {
    animation: fadeIn 0.3s ease;
}
