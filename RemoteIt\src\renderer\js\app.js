// RemoteIt Main Application
class RemoteItApp {
  constructor() {
    this.currentUser = null;
    this.isAuthenticated = false;
    this.devices = [];
    this.connections = [];
    this.currentView = 'dashboard';
    
    this.init();
  }

  async init() {
    console.log('Initializing RemoteIt application...');
    
    // Show loading screen
    this.showLoading();
    
    try {
      // Initialize system info
      await this.loadSystemInfo();
      
      // Check authentication status
      await this.checkAuthStatus();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Setup IPC listeners
      this.setupIpcListeners();
      
      // Hide loading screen and show app
      this.hideLoading();
      
      console.log('RemoteIt application initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize application:', error);
      this.showError('Failed to initialize application. Please restart the app.');
    }
  }

  showLoading() {
    document.getElementById('loading-screen').classList.remove('hidden');
    document.getElementById('app').classList.add('hidden');
  }

  hideLoading() {
    document.getElementById('loading-screen').classList.add('hidden');
    document.getElementById('app').classList.remove('hidden');
  }

  async loadSystemInfo() {
    try {
      const systemInfo = await window.electronAPI.system.getInfo();
      console.log('System info loaded:', systemInfo);
      
      // Update version in help section
      const versionElement = document.getElementById('app-version');
      if (versionElement) {
        versionElement.textContent = systemInfo.version;
      }
      
    } catch (error) {
      console.error('Failed to load system info:', error);
    }
  }

  async checkAuthStatus() {
    try {
      const user = await window.electronAPI.auth.getUser();
      
      if (user) {
        this.currentUser = user;
        this.isAuthenticated = true;
        this.updateUserInfo();
        await this.loadInitialData();
      } else {
        this.showLoginModal();
      }
      
    } catch (error) {
      console.error('Auth check failed:', error);
      this.showLoginModal();
    }
  }

  updateUserInfo() {
    const userNameElement = document.getElementById('user-name');
    if (userNameElement && this.currentUser) {
      userNameElement.textContent = this.currentUser.name || this.currentUser.email;
    }
  }

  async loadInitialData() {
    try {
      // Load devices
      await this.loadDevices();
      
      // Load recent connections
      await this.loadRecentConnections();
      
      // Update dashboard
      this.updateDashboard();
      
    } catch (error) {
      console.error('Failed to load initial data:', error);
    }
  }

  async loadDevices() {
    try {
      this.devices = await window.electronAPI.connection.getDevices();
      this.renderDevices();
    } catch (error) {
      console.error('Failed to load devices:', error);
    }
  }

  async loadRecentConnections() {
    // This would load recent connections from storage or API
    // For now, we'll use mock data
    this.recentConnections = [
      {
        id: '1',
        name: 'Work Desktop',
        lastConnected: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        platform: 'windows'
      },
      {
        id: '2',
        name: 'Home Laptop',
        lastConnected: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        platform: 'macos'
      }
    ];
    
    this.renderRecentConnections();
  }

  setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-item').forEach(item => {
      item.addEventListener('click', (e) => {
        const view = e.currentTarget.dataset.view;
        if (view) {
          this.switchView(view);
        }
      });
    });

    // Title bar buttons
    document.getElementById('minimize-btn')?.addEventListener('click', () => {
      window.electronAPI.window.minimize();
    });

    document.getElementById('maximize-btn')?.addEventListener('click', () => {
      window.electronAPI.window.maximize();
    });

    document.getElementById('close-btn')?.addEventListener('click', () => {
      window.electronAPI.window.close();
    });

    // Dashboard actions
    document.getElementById('new-connection-btn')?.addEventListener('click', () => {
      this.showNewConnectionDialog();
    });

    document.getElementById('host-mode-btn')?.addEventListener('click', () => {
      this.enableHostMode();
    });

    document.getElementById('quick-connect-btn')?.addEventListener('click', () => {
      this.quickConnect();
    });

    // Device actions
    document.getElementById('refresh-devices-btn')?.addEventListener('click', () => {
      this.loadDevices();
    });

    document.getElementById('add-device-btn')?.addEventListener('click', () => {
      this.showAddDeviceDialog();
    });

    // File transfer
    document.getElementById('select-files-btn')?.addEventListener('click', () => {
      this.selectFiles();
    });

    // Settings
    this.setupSettingsListeners();

    // Logout
    document.getElementById('logout-btn')?.addEventListener('click', () => {
      this.logout();
    });

    // Login form
    document.getElementById('login-form')?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleLogin();
    });

    // Register button
    document.getElementById('register-btn')?.addEventListener('click', () => {
      this.showRegisterDialog();
    });

    // Connection request modal
    document.getElementById('accept-connection')?.addEventListener('click', () => {
      this.acceptConnectionRequest();
    });

    document.getElementById('reject-connection')?.addEventListener('click', () => {
      this.rejectConnectionRequest();
    });

    // Help links
    document.getElementById('contact-support')?.addEventListener('click', () => {
      window.electronAPI.system.openExternal('mailto:<EMAIL>');
    });
  }

  setupIpcListeners() {
    // Auth state changes
    window.electronAPI.auth.onAuthStateChanged((event, data) => {
      if (data.authenticated) {
        this.currentUser = data.user;
        this.isAuthenticated = true;
        this.updateUserInfo();
        this.hideLoginModal();
        this.loadInitialData();
      } else {
        this.currentUser = null;
        this.isAuthenticated = false;
        this.showLoginModal();
      }
    });

    // Connection state changes
    window.electronAPI.connection.onConnectionStateChanged((event, data) => {
      this.handleConnectionStateChange(data);
    });

    // Device list changes
    window.electronAPI.connection.onDeviceListChanged((event, devices) => {
      this.devices = devices;
      this.renderDevices();
    });

    // Menu events
    window.electronAPI.menu.onNewConnection(() => {
      this.showNewConnectionDialog();
    });
  }

  setupSettingsListeners() {
    // Auto start
    document.getElementById('auto-start')?.addEventListener('change', (e) => {
      // Save setting
      console.log('Auto start:', e.target.checked);
    });

    // Minimize to tray
    document.getElementById('minimize-to-tray')?.addEventListener('change', (e) => {
      // Save setting
      console.log('Minimize to tray:', e.target.checked);
    });

    // Connection quality
    document.getElementById('connection-quality')?.addEventListener('change', (e) => {
      // Save setting
      console.log('Connection quality:', e.target.value);
    });

    // Frame rate
    const frameRateSlider = document.getElementById('frame-rate');
    const frameRateValue = document.getElementById('frame-rate-value');
    
    frameRateSlider?.addEventListener('input', (e) => {
      const value = e.target.value;
      frameRateValue.textContent = `${value} FPS`;
    });

    frameRateSlider?.addEventListener('change', (e) => {
      // Save setting
      console.log('Frame rate:', e.target.value);
    });

    // Security settings
    document.getElementById('require-permission')?.addEventListener('change', (e) => {
      console.log('Require permission:', e.target.checked);
    });

    document.getElementById('session-recording')?.addEventListener('change', (e) => {
      console.log('Session recording:', e.target.checked);
    });
  }

  switchView(viewName) {
    // Update navigation
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.remove('active');
    });
    
    document.querySelector(`[data-view="${viewName}"]`)?.classList.add('active');

    // Update views
    document.querySelectorAll('.view').forEach(view => {
      view.classList.remove('active');
    });
    
    document.getElementById(`${viewName}-view`)?.classList.add('active');

    this.currentView = viewName;

    // Load view-specific data
    switch (viewName) {
      case 'devices':
        this.loadDevices();
        break;
      case 'connections':
        this.loadConnections();
        break;
      case 'files':
        this.loadFileTransfers();
        break;
    }
  }

  async handleLogin() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const mfaCode = document.getElementById('mfa-code').value;

    if (!email || !password) {
      this.showLoginError('Please enter email and password');
      return;
    }

    try {
      const result = await window.electronAPI.auth.login({
        email,
        password,
        mfaCode
      });

      if (result.success) {
        // Login successful - auth state change will be handled by IPC listener
        this.hideLoginError();
      } else {
        if (result.requiresMfa) {
          document.getElementById('mfa-group').style.display = 'block';
          this.showLoginError('Please enter your MFA code');
        } else {
          this.showLoginError(result.error || 'Login failed');
        }
      }
    } catch (error) {
      console.error('Login error:', error);
      this.showLoginError('Login failed. Please try again.');
    }
  }

  async logout() {
    try {
      await window.electronAPI.auth.logout();
      // Auth state change will be handled by IPC listener
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  showLoginModal() {
    document.getElementById('login-modal').style.display = 'flex';
  }

  hideLoginModal() {
    document.getElementById('login-modal').style.display = 'none';
    // Reset form
    document.getElementById('login-form').reset();
    document.getElementById('mfa-group').style.display = 'none';
    this.hideLoginError();
  }

  showLoginError(message) {
    const errorElement = document.getElementById('login-error');
    errorElement.textContent = message;
    errorElement.style.display = 'block';
  }

  hideLoginError() {
    document.getElementById('login-error').style.display = 'none';
  }

  showError(message) {
    // Show a general error notification
    console.error(message);
    // You could implement a toast notification system here
  }

  async quickConnect() {
    const input = document.getElementById('quick-connect-input');
    const deviceId = input.value.trim();

    if (!deviceId) {
      this.showError('Please enter a device ID or name');
      return;
    }

    try {
      const sessionId = await window.electronAPI.connection.connect(deviceId);
      console.log('Connection initiated:', sessionId);
      input.value = '';
    } catch (error) {
      console.error('Quick connect failed:', error);
      this.showError('Failed to connect to device');
    }
  }

  async enableHostMode() {
    try {
      await window.electronAPI.window.showHost();
    } catch (error) {
      console.error('Failed to enable host mode:', error);
      this.showError('Failed to enable host mode');
    }
  }

  // Placeholder methods for features to be implemented
  showNewConnectionDialog() {
    console.log('Show new connection dialog');
  }

  showAddDeviceDialog() {
    console.log('Show add device dialog');
  }

  showRegisterDialog() {
    console.log('Show register dialog');
  }

  async selectFiles() {
    try {
      const result = await window.electronAPI.file.selectFiles({
        properties: ['openFile', 'multiSelections']
      });
      
      if (!result.canceled && result.filePaths.length > 0) {
        console.log('Selected files:', result.filePaths);
        // Handle file selection
      }
    } catch (error) {
      console.error('File selection failed:', error);
    }
  }

  renderDevices() {
    const container = document.getElementById('devices-grid');
    if (!container) return;

    container.innerHTML = '';

    this.devices.forEach(device => {
      const deviceElement = this.createDeviceElement(device);
      container.appendChild(deviceElement);
    });
  }

  createDeviceElement(device) {
    const div = document.createElement('div');
    div.className = 'device-card';
    div.innerHTML = `
      <div class="device-header">
        <div class="device-icon">
          <i class="fas fa-${this.getDeviceIcon(device.platform)}"></i>
        </div>
        <div class="device-info">
          <h3>${device.name}</h3>
          <span class="device-status ${device.isOnline ? 'online' : 'offline'}">
            ${device.isOnline ? 'Online' : 'Offline'}
          </span>
        </div>
      </div>
      <div class="device-details">
        <div class="device-detail">
          <span class="label">Platform:</span>
          <span class="value">${device.platform}</span>
        </div>
        <div class="device-detail">
          <span class="label">Last Seen:</span>
          <span class="value">${this.formatDate(device.lastSeen)}</span>
        </div>
      </div>
      <div class="device-actions">
        <button class="btn btn-primary" onclick="app.connectToDevice('${device.id}')" ${!device.isOnline ? 'disabled' : ''}>
          <i class="fas fa-link"></i>
          Connect
        </button>
        <button class="btn btn-secondary" onclick="app.configureDevice('${device.id}')">
          <i class="fas fa-cog"></i>
          Configure
        </button>
      </div>
    `;
    return div;
  }

  renderRecentConnections() {
    const container = document.getElementById('recent-connections');
    if (!container) return;

    container.innerHTML = '';

    this.recentConnections.forEach(connection => {
      const connectionElement = this.createConnectionElement(connection);
      container.appendChild(connectionElement);
    });
  }

  createConnectionElement(connection) {
    const div = document.createElement('div');
    div.className = 'connection-item';
    div.innerHTML = `
      <div class="connection-icon">
        <i class="fas fa-${this.getDeviceIcon(connection.platform)}"></i>
      </div>
      <div class="connection-details">
        <div class="connection-name">${connection.name}</div>
        <div class="connection-info">Last connected: ${this.formatDate(connection.lastConnected)}</div>
      </div>
    `;
    
    div.addEventListener('click', () => {
      this.connectToDevice(connection.id);
    });
    
    return div;
  }

  updateDashboard() {
    // Update connection status
    document.getElementById('connection-status').textContent = 'Online';
    document.getElementById('connection-status').className = 'status-value online';
    
    // Update active sessions
    document.getElementById('active-sessions').textContent = this.connections.length;
    
    // Update data transfer (placeholder)
    document.getElementById('data-transfer').textContent = '0 MB/s';
  }

  getDeviceIcon(platform) {
    switch (platform?.toLowerCase()) {
      case 'windows': return 'windows';
      case 'macos': case 'darwin': return 'apple';
      case 'linux': return 'linux';
      case 'android': return 'android';
      case 'ios': return 'apple';
      default: return 'desktop';
    }
  }

  formatDate(date) {
    if (!date) return 'Never';
    
    const now = new Date();
    const diff = now - new Date(date);
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    
    return new Date(date).toLocaleDateString();
  }

  async connectToDevice(deviceId) {
    try {
      const sessionId = await window.electronAPI.connection.connect(deviceId);
      console.log('Connected to device:', deviceId, 'Session:', sessionId);
    } catch (error) {
      console.error('Failed to connect to device:', error);
      this.showError('Failed to connect to device');
    }
  }

  configureDevice(deviceId) {
    console.log('Configure device:', deviceId);
    // Implement device configuration
  }

  handleConnectionStateChange(data) {
    console.log('Connection state changed:', data);
    // Update UI based on connection state
  }

  acceptConnectionRequest() {
    console.log('Accept connection request');
    // Implement connection request acceptance
  }

  rejectConnectionRequest() {
    console.log('Reject connection request');
    // Implement connection request rejection
  }

  loadConnections() {
    console.log('Load connections');
    // Implement connections loading
  }

  loadFileTransfers() {
    console.log('Load file transfers');
    // Implement file transfers loading
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.app = new RemoteItApp();
});
