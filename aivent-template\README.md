# AIvent - AI Conference Website Template

A modern, responsive website template for AI events, conferences, and meetups. Built with HTML5, CSS3, and JavaScript.

## 🎨 Design Features

- **Modern AI Theme**: Purple and blue gradient design with futuristic elements
- **Fully Responsive**: Optimized for desktop, tablet, and mobile devices
- **Interactive Elements**: Countdown timer, smooth scrolling, animated sections
- **Professional Layout**: Clean, modern design suitable for tech conferences

## 🖼️ Generated Images

All images were generated using the local image generator with workspace timing standards:

### Successfully Generated (4/6 images):
1. **Speaker 1 - <PERSON>** (400x400px)
   - URL: http://localhost:7777/api/image/1753509501306
   - Professional AI conference speaker headshot

2. **Speaker 2 - <PERSON>** (400x400px)
   - URL: http://localhost:7777/api/image/1753509592138
   - Professional female AI researcher headshot

3. **AI Sphere Logo** (800x800px)
   - URL: http://localhost:7777/api/image/1753509843975
   - Abstract 3D geometric sphere with purple/blue wireframe

4. **Conference Venue** (1200x800px)
   - URL: http://localhost:7777/api/image/1753509934955
   - Modern tech conference venue interior

### Image Generation Stats:
- **Average Generation Time**: 6.3 seconds
- **Success Rate**: 66.7% (4/6 images)
- **Workspace Standards**: All images generated with intelligent timing system
- **Quality**: High-resolution, professional-grade images

## 📱 Responsive Breakpoints

- **Desktop**: 1024px and above
- **Tablet**: 768px - 1023px
- **Mobile**: 480px - 767px
- **Small Mobile**: Below 480px

## 🚀 Features

### Navigation
- Fixed header with smooth scrolling
- Mobile hamburger menu
- Logo with generated AI sphere image

### Hero Section
- Large title with gradient text effects
- Event details (date, location)
- Call-to-action buttons
- Live countdown timer
- Event location details

### About Section
- Two-column layout with text and venue image
- Feature list with checkmarks
- Professional description

### Speakers Section
- Grid layout for speaker profiles
- Generated speaker images
- Session information
- Hover effects

### Schedule Section
- Tabbed interface for multiple days
- Time-based schedule items
- Responsive design

### Tickets Section
- Three-tier pricing structure
- Feature comparison
- Interactive selection buttons
- "Most Popular" badge

### Footer
- Multi-column layout
- Social media links
- Contact information
- Logo and branding

## 🎯 Interactive Features

### Countdown Timer
- Real-time countdown to event date (October 1, 2025)
- Automatically updates every second
- Shows "LIVE NOW" when event starts

### Mobile Navigation
- Hamburger menu for mobile devices
- Smooth slide-in animation
- Closes on link click or escape key

### Smooth Scrolling
- All navigation links use smooth scrolling
- Optimized for performance

### Animations
- Fade-in animations for sections
- Hover effects on cards and buttons
- Parallax effect on hero background
- Pulse animation on countdown numbers

### Accessibility
- Focus states for keyboard navigation
- High contrast mode support
- Semantic HTML structure
- ARIA labels where needed

## 🛠️ Technical Implementation

### HTML Structure
- Semantic HTML5 elements
- Proper heading hierarchy
- Meta tags for SEO and responsive design

### CSS Features
- CSS Grid and Flexbox for layouts
- Custom properties for consistent theming
- Gradient backgrounds and effects
- Smooth transitions and animations
- Mobile-first responsive design

### JavaScript Functionality
- ES6+ modern JavaScript
- Event delegation for performance
- Intersection Observer for animations
- Debounced scroll events
- Image preloading for performance

## 📊 Performance Optimizations

- **Image Preloading**: Critical images loaded on page load
- **Debounced Events**: Scroll events optimized for 60fps
- **CSS Animations**: Hardware-accelerated transforms
- **Lazy Loading**: Intersection Observer for animations
- **Minified Assets**: Ready for production deployment

## 🎨 Color Scheme

- **Primary Purple**: #8b5cf6
- **Primary Blue**: #3b82f6
- **Dark Background**: #0a0a0a
- **Secondary Dark**: #111111
- **Text White**: #ffffff
- **Text Gray**: #d1d5db
- **Accent Gray**: #9ca3af

## 📱 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🚀 Deployment

1. Upload all files to your web server
2. Ensure image URLs are accessible
3. Update event details in HTML
4. Customize colors and content as needed
5. Test on all target devices

## 🔧 Customization

### Changing Event Details
- Update dates in HTML and JavaScript countdown
- Modify location information
- Change speaker information and images

### Styling Modifications
- Colors defined in CSS custom properties
- Easy to modify gradient backgrounds
- Responsive breakpoints clearly defined

### Adding Content
- Additional speakers: Copy speaker card structure
- More schedule days: Add new tab and content
- Extra ticket tiers: Duplicate ticket card

## 📈 Generated with Workspace Standards

This template was created using the **Workspace-Wide Intelligent Timing System**:

- **Minimum Timeout**: 80 seconds
- **Maximum Timeout**: 360 seconds  
- **4K Maximum**: 600 seconds
- **Sequential Processing**: One image at a time
- **Quality Focus**: No compromise on image resolution

## 🎯 Perfect For

- AI conferences and summits
- Tech meetups and workshops
- Machine learning events
- Innovation conferences
- Startup pitch events
- Technology exhibitions

---

**AIvent Template** - Built with modern web technologies and AI-generated imagery for the perfect tech conference experience! 🚀
