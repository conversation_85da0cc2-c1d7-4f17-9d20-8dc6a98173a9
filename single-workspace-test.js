const axios = require('axios');

// WORKSPACE-WIDE INTELLIGENT TIMING SYSTEM
// FINAL SPECIFICATIONS: Min 80s, Max 360s, 4K Max 600s
function calculateImageTiming(width, height) {
  const totalPixels = width * height;
  const megapixels = totalPixels / 1000000;
  
  let timeout = 80; // Minimum timeout: 80 seconds
  let waitTime = 80; // Minimum wait time: 80 seconds
  let category = 'small';
  
  if (megapixels <= 0.5) {
    timeout = 80; waitTime = 80; category = 'small';
  } else if (megapixels <= 1.0) {
    timeout = 150; waitTime = 80; category = 'medium';
  } else if (megapixels <= 2.0) {
    timeout = 250; waitTime = 80; category = 'large';
  } else if (megapixels <= 8.0) {
    timeout = 360; waitTime = 80; category = 'extra-large';
  } else {
    timeout = 600; waitTime = 80; category = '4k-ultra';
  }
  
  return { timeout, waitTime, megapixels: megapixels.toFixed(2), category, isHighRes: megapixels > 8.0 };
}

async function singleWorkspaceTest() {
  console.log('🏢 WORKSPACE TIMING SYSTEM - Single Image Validation');
  console.log('📋 Testing workspace standards: Min 80s | Max 360s | 4K Max 600s\n');
  
  const testImage = {
    prompt: 'Professional workspace test image with clean modern design',
    width: 800,
    height: 600,
    description: 'Workspace Test Image'
  };
  
  const timing = calculateImageTiming(testImage.width, testImage.height);
  
  console.log('📊 WORKSPACE SPECIFICATIONS:');
  console.log(`   Image: ${testImage.description}`);
  console.log(`   Resolution: ${testImage.width}x${testImage.height} (${timing.megapixels}MP)`);
  console.log(`   Category: ${timing.category}`);
  console.log(`   Workspace Timeout: ${timing.timeout}s`);
  console.log(`   Wait Time: ${timing.waitTime}s`);
  console.log('');
  
  try {
    console.log('📸 Submitting image generation request...');
    const response = await axios.post('http://localhost:7777/api/generate', {
      type: 'image',
      prompt: testImage.prompt,
      width: testImage.width,
      height: testImage.height,
      timeout: timing.timeout
    });
    
    if (!response.data.success) {
      console.log('❌ Failed to submit request');
      return;
    }
    
    console.log(`✅ Request submitted successfully:`);
    console.log(`   Request ID: ${response.data.requestId}`);
    console.log(`   Model: ${response.data.model}`);
    console.log(`   Workspace Timeout: ${timing.timeout}s`);
    console.log('');
    
    console.log('⏰ Monitoring generation progress...');
    
    // Monitor with workspace timeout
    const maxWaitTime = (timing.timeout + 30) * 1000; // Add 30s buffer
    const startTime = Date.now();
    let completed = false;
    
    while (!completed && (Date.now() - startTime) < maxWaitTime) {
      try {
        const statusResponse = await axios.get(`http://localhost:7777/api/status/${response.data.requestId}`);
        const status = statusResponse.data;
        
        if (status.status === 'completed') {
          const created = new Date(status.createdAt);
          const completedTime = new Date(status.completedAt);
          const actualTime = ((completedTime - created) / 1000).toFixed(1);
          const efficiency = ((timing.timeout / actualTime) * 100).toFixed(1);
          
          console.log('🎉 WORKSPACE SUCCESS!');
          console.log('========================');
          console.log(`✅ Image: ${testImage.description}`);
          console.log(`   Resolution: ${testImage.width}x${testImage.height} (${timing.megapixels}MP)`);
          console.log(`   Category: ${timing.category}`);
          console.log(`   Workspace Timeout: ${timing.timeout}s`);
          console.log(`   Actual Generation: ${actualTime}s`);
          console.log(`   Efficiency: ${efficiency}%`);
          console.log(`   Standards Met: ${actualTime <= timing.timeout ? '✅ YES' : '❌ NO'}`);
          console.log(`   Image URL: http://localhost:7777${status.imageUrl}`);
          console.log(`   File Path: ${status.filePath}`);
          console.log('');
          console.log('🏢 WORKSPACE TIMING SYSTEM VALIDATED!');
          console.log('✅ Ready for ALL projects in this workspace');
          
          completed = true;
          
        } else if (status.status === 'failed') {
          console.log('❌ Generation failed');
          console.log(`   Error: ${status.error || 'Unknown error'}`);
          completed = true;
          
        } else {
          console.log(`⏳ Status: ${status.status}`);
          await new Promise(resolve => setTimeout(resolve, 5000));
        }
        
      } catch (error) {
        console.log(`❌ Error checking status: ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
    if (!completed) {
      console.log('⏰ Timeout reached - checking final status...');
      try {
        const finalStatus = await axios.get(`http://localhost:7777/api/status/${response.data.requestId}`);
        console.log(`   Final status: ${finalStatus.data.status}`);
      } catch (error) {
        console.log(`   Could not get final status: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.log(`❌ Test failed: ${error.message}`);
  }
  
  console.log('\n🔧 WORKSPACE STANDARDS SUMMARY:');
  console.log('================================');
  console.log('• Small images (≤0.5MP): 80s timeout, 80s wait');
  console.log('• Medium images (0.5-1MP): 150s timeout, 80s wait');
  console.log('• Large images (1-2MP): 250s timeout, 80s wait');
  console.log('• Extra-large images (2-8MP): 360s timeout, 80s wait');
  console.log('• 4K+ images (>8MP): 600s timeout, 80s wait');
  console.log('\n🎯 These standards apply to ALL future projects in this workspace!');
}

singleWorkspaceTest().catch(error => {
  console.error('❌ Workspace test failed:', error.message);
});
